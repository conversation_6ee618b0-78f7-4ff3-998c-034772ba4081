#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试email模块导入 - 验证PyInstaller打包时不会排除email模块
"""
import sys

def test_email_module_import():
    """测试email模块是否可以正常导入"""
    print("测试email模块导入...")
    
    try:
        import email
        print("✓ email 模块导入成功")
        
        # 测试一些基本的email子模块
        import email.mime
        print("✓ email.mime 模块导入成功")
        
        import email.utils
        print("✓ email.utils 模块导入成功")
        
        return True
    except ImportError as e:
        print(f"✗ email模块导入失败: {e}")
        return False

def test_pkg_resources_dependency():
    """测试pkg_resources对email的依赖"""
    print("\n测试pkg_resources模块...")
    
    try:
        import pkg_resources
        print("✓ pkg_resources 模块导入成功")
        
        # 测试基本功能
        version = pkg_resources.get_distribution('pip').version
        print(f"✓ pkg_resources 功能正常 (pip版本: {version})")
        
        return True
    except ImportError as e:
        print(f"✗ pkg_resources模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"⚠ pkg_resources功能测试异常: {e}")
        return True  # 导入成功就算通过

def test_build_script_email_config():
    """测试构建脚本中email模块不被排除"""
    print("\n测试构建脚本email配置...")
    
    import os
    
    try:
        from build_exe import create_optimized_spec_file
        
        # 创建spec文件
        result = create_optimized_spec_file()
        
        if not result:
            print("✗ spec文件创建失败")
            return False
        
        # 读取生成的spec文件
        spec_file = "医疗数据校验工具.spec"
        if not os.path.exists(spec_file):
            print("✗ spec文件不存在")
            return False
        
        with open(spec_file, 'r', encoding='utf-8') as f:
            spec_content = f.read()
        
        # 检查email模块是否被排除
        if "'email'" in spec_content and "excludes" in spec_content:
            # 查找excludes部分
            excludes_start = spec_content.find("excludes = [")
            if excludes_start != -1:
                excludes_end = spec_content.find("]", excludes_start)
                excludes_section = spec_content[excludes_start:excludes_end]
                
                if "'email'" in excludes_section and not excludes_section.count("# 'email'") > 0:
                    print("✗ email模块仍在排除列表中")
                    return False
        
        print("✓ email模块未被排除")
        return True
        
    except Exception as e:
        print(f"✗ 构建脚本email配置测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists("医疗数据校验工具.spec"):
            os.remove("医疗数据校验工具.spec")

def main():
    """运行所有测试"""
    print("=" * 60)
    print("     email模块和pkg_resources依赖测试")
    print("=" * 60)
    
    tests = [
        test_email_module_import,
        test_pkg_resources_dependency,
        test_build_script_email_config
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ 测试 {test_func.__name__} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    if passed == total:
        print("✓ 所有email相关测试通过!")
        print("✓ PyInstaller打包应该能正确处理email依赖")
    else:
        print(f"✗ {total - passed} 个测试失败")
        print("⚠ 可能需要进一步调整PyInstaller配置")
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
from datetime import datetime  # 修改这行
import functools
from time import strptime, mktime
from datetime import <PERSON><PERSON><PERSON>
def empty(string):
    return str(string).strip() in ("", "-", "None", "—", "——")

def emptys(string):
    return str(string).strip() in ("", "None")

def true_list(_list):
    return list(filter(lambda x: not empty(x), _list))
def sc(row, start, end, pattern):
    return [row.get(pattern.format(i), "") for i in range(start, end + 1)]

def include(iter, string):
    for each in iter:
        if str(each) in str(string):
            return True

def has(iter, string):
    for each in iter:
        if str(string) in str(each):
            return True

def iter_iter(iter_sub, iter_number):
    for i in iter_number:
        for j in iter_sub:
            if str(i).startswith(str(j)):
                return True

def startswith_iter(iter, string):
    """string 开头等于 iter 中的元素"""
    for each in iter:
        if string.startswith(each):
            return True

def iter_startswith(iter, string):
    """iter 中的元素开头等于 string"""
    for each in iter:
        if str(each).strip().startswith(str(string)):
            return True

def iter_iter_startswith(iter_sub, iter_number):
    """iter_number 中的元素开头等于 iter_sub 中的元素"""
    for i in iter_number:
        for j in iter_sub:
            if str(i).startswith(str(j)):
                return True

def repeat(iter):
    na = iter.count("NA")
    for i in range(na):
        iter.remove("NA")

    iter = list(filter(None, iter))
    return len(set(iter)) != len(iter)
def clean_phone(phone):
    """清理电话号码，去除连字符和空格"""
    if not phone or empty(phone):
        return ""
    return str(phone).strip().replace('-', '').replace(' ', '')
def time_compare(operations, _in, _out):
    """比较手术时间是否在住院期间内

    Args:
        operations: 手术时间
        _in: 入院时间
        _out: 出院时间

    Returns:
        bool: True表示时间不在范围内,False表示时间在范围内
    """
    try:
        # 先检查参数是否为空
        if empty(operations) or empty(_in) or empty(_out):
            return False

        # 使用 convert_date_format 标准化日期格式
        operations = convert_date_format(operations)
        _in = convert_date_format(_in)
        _out = convert_date_format(_out)

        # 再次检查转换后的值是否为空
        if not operations or not _in or not _out:
            return False

        # 进行时间比较
        op_time = mktime(strptime(operations, "%Y/%m/%d %H:%M:%S"))
        in_time = mktime(strptime(_in, "%Y/%m/%d %H:%M:%S"))
        out_time = mktime(strptime(_out, "%Y/%m/%d %H:%M:%S"))

        # 检查手术时间是否在住院期间内
        if not (in_time <= op_time < out_time):
            return True

        return False

    except Exception as e:
        print(f"时间比较错误: {str(e)}")
        return False

def grange(*args):
    result = []
    for rg in args:
        if "-" not in rg:
            # 如果没有连字符，直接添加单个编码
            result.append(rg)
            continue
        start, end = rg.split("-")
        letter = start[0]
        start, end = int(start[1:].strip()), int(end[1:].strip())
        result.extend([f"{letter}{str(i).zfill(2)}" for i in range(start, end+1)])
    return result

def get_timechuo(time_):
    try:
        # 先标准化日期格式
        time_ = convert_date_format(time_)
        if not time_:
            return 0

        # 使用 datetime 而不是 datetime.datetime
        cd = datetime.strptime(time_, "%Y/%m/%d %H:%M:%S")
        ts = (cd - datetime(1970, 1, 1, 8)).total_seconds()
        return ts
    except Exception as e:
        print(f"时间戳转换错误: {str(e)}")
        return 0
def get_str(var):
    if isinstance(var, str):
        return var
    else:
        return "%.f" % var

def _19(主, 所有其):
    for each in 所有其:
        if "Z37" in each:
            return True
    所有其.append(主)
    for each in 所有其:
        for num in ["080","081","082","083","084"]:
            if num in each:
                return False
    return True
# 增加日期格式转换辅助函数
def parse_date(date_str):
    try:
        # 尝试解析带有日期和时间的格式
        return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        # 如果抛出 ValueError，说明没有时间部分，再尝试解析只有日期的格式
        return datetime.strptime(date_str, "%Y-%m-%d")
def safe_convert(value):
    try:
        return int(value)
    except (ValueError, TypeError):
        return None  # 或者返回一个默认值，如 0
def safe_int(value):
    try:
        return int(float(str(value).strip()))
    except (ValueError, TypeError):
        return None
def convert_date_format(date_str):
    """将多种日期格式统一转换为 YYYY/MM/DD HH:mm:ss 格式"""
    try:
        if not date_str or date_str == '-':
            return ''

        # 去除首尾空格
        date_str = str(date_str).strip()

        # 定义可能的日期格式
        date_formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y/%m/%d %H:%M:%S',
            '%Y-%m-%d',
            '%Y/%m/%d'
        ]

        # 尝试解析日期
        parsed_date = None
        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                break
            except ValueError:
                continue

        if parsed_date is None:
            return ''

        # 返回标准格式
        return parsed_date.strftime('%Y/%m/%d %H:%M:%S')

    except Exception as e:
        print(f"日期转换错误 {date_str}: {str(e)}")
        return ''

def try_convert(value):
    """尝试将值转换为数字，如果转换失败，返回0"""
    try:
        if value is None or (isinstance(value, str) and not value.strip()):
            return 0
        # 尝试使用float转换，这样可以处理小数和整数
        return float(value)
    except (ValueError, TypeError):
        return 0  # 如果转换失败，返回0
def validate_phone(phone):
    """验证电话号码格式

    支持格式:
    1. 11位手机号
    2. 区号-电话号(区号3-4位,电话号7-8位)

    Args:
        phone: 电话号码字符串

    Returns:
        bool: 是否是有效的电话号码格式
    """
    try:
        phone = str(phone).strip()

        # 检查11位手机号
        if phone.isdigit() and len(phone) == 11:
            return True

        # 检查座机号格式
        if "-" in phone:
            area_code, phone_number = phone.split("-")

            # 验证区号(3-4位)和电话号码(7-8位)
            if (area_code.isdigit() and
                phone_number.isdigit() and
                len(area_code) in (3, 4) and
                len(phone_number) in (7, 8)):
                return True

        return False
    except:
        return False
def true_list(_list):
    return list(filter(lambda x: not empty(x), _list))
# 定义需要检查的前缀
newborn_prefixes = ['Z37.0', 'Z37.2', 'Z37.3', 'Z37.5', 'Z37.6', 'Z37.9']

def check_icu_time(入院时间, 出院时间, 进重症监护室时间, 出重症监护室时间):
    try:

        # 如果进重症监护室时间或出重症监护室时间为空，直接返回 True（不验证）
        if not (进重症监护室时间 is not None and 进重症监护室时间 != "" and
                出重症监护室时间 is not None and 出重症监护室时间 != ""):
            return True

        # 解析为日期时间对象，并验证日期有效性
        def parse_and_validate_time(time_str):
            if not time_str or not str(time_str).strip():
                return None
            try:
                return datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
            except ValueError as e:
                # 尝试修正常见的日期错误（如 11-31 改为 11-30）
                try:
                    date_part, time_part = str(time_str).strip().split()
                    year, month, day = map(int, date_part.split('-'))
                    if day > 28:  # 简单修正，假设最大为 30 或 31，根据月份调整
                        if month in [4, 6, 9, 11] and day == 31:
                            day = 30  # 修正为 30 天月份
                        elif month == 2 and day > 29:
                            day = 29 if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0) else 28
                    corrected_date = f"{year:04d}-{month:02d}-{day:02d} {time_part}"
                    return datetime.strptime(corrected_date, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    return None

        # 解析所有时间
        admit_time = parse_and_validate_time(入院时间)
        discharge_time = parse_and_validate_time(出院时间)
        icu_in_time = parse_and_validate_time(进重症监护室时间)
        icu_out_time = parse_and_validate_time(出重症监护室时间)

        # 再次检查解析是否成功
        if any(t is None for t in [admit_time, discharge_time, icu_in_time, icu_out_time]):
            return False  # 任何解析失败都返回 False

        # 检查时间范围
        return admit_time <= icu_in_time <= icu_out_time <= discharge_time
    except Exception:
        return False  # 任何其他异常也返回 False
def check_surgery_data(row):
    try:
        # 获取手术费用，处理空值和类型转换
        surgery_fee = row["D20"]
        if surgery_fee is None or str(surgery_fee).strip() == "":
            return True  # 手术费用为空或无效时不验证，直接返回 True
        try:
            surgery_fee = float(surgery_fee)  # 转换为浮点数
        except (ValueError, TypeError):
            return False  # 费用无法转换为数字，返回 False

        # 如果手术费用 <= 0，不验证直接返回 True
        if surgery_fee <= 0:
            return True

        # 检查主要手术相关字段是否非空
        fields = [
            row["C14x01C"],  # 主要手术操作编码
            row["C15x01N"],  # 主要手术操作名称
            row["C16x01"],   # 主要手术操作日期
            row["C18x01"]    # 主要手术操作术者
        ]

        # 验证每个字段是否非空
        if not all(field is not None and str(field).strip() != "" for field in fields):
            return False

        # 验证主要手术操作日期是否有效（格式 "YYYY-MM-DD HH:MM:SS"）
        if not is_valid_date(row["C16x01"], "%Y-%m-%d %H:%M:%S"):
            return False

        return True  # 所有条件满足，返回 True
    except Exception:
        return False  # 任何其他异常返回 False
def check_surgery_name_and_levels(主要手术_名称, 主要手术级别, 主要手术操作切口等级):
    try:
        # 如果主要手术_名称为空，不验证，直接返回 True
        if 主要手术_名称 is None or str(主要手术_名称).strip() == "-":
            return True

        # 主要手术_名称非空时，检查主要手术级别和主要手术操作切口等级是否非空
        # 允许 "-" 作为有效值
        fields = [
            主要手术级别,
            主要手术操作切口等级
        ]

        # 验证每个字段是否非空（允许 "-"）
        for field in fields:
            if field is None or str(field).strip() == "":
                return False
            # 如果字段不是 "-"，检查是否为非空字符串
            if str(field).strip() == "-":
                continue
            if not str(field).strip():
                return False

        return True  # 所有条件满足，返回 True
    except Exception:
        return False  # 任何其他异常返回 False

def is_valid_date(date_str, format_str):
    if not date_str or not str(date_str).strip():
        return False
    try:
        datetime.strptime(date_str, format_str)
        return True
    except ValueError:
        return False
a = [
    lambda 组织机构代码: not empty(组织机构代码),
    lambda 总费用, 各项费用: float(总费用) > 0 and float(总费用) >= functools.reduce(lambda x, y: x + float(y), 各项费用, 0),
    lambda 手术治疗费, 麻醉费, 手术费: abs(float(手术治疗费 or 0) - (float(麻醉费 or 0) + float(手术费 or 0))) < 0.01,
    lambda 住址邮编: len(str(住址邮编).zfill(6)) == 6,   # 测试
    lambda 住院医师: not empty(住院医师),
    lambda 主治医师: not empty(主治医师),   #5
    lambda 质控医师: not empty(质控医师),
    lambda 病案质量: not empty(病案质量),
    lambda 质控护师: not empty(质控护师),  #10
    lambda 质控日期: not empty(质控日期),
    lambda ABO: not empty(ABO),
    lambda 血费, 输血反应: ((safe_convert(输血反应) in [1, 2]) if try_convert(血费) > 0 else safe_convert(输血反应) == 0),
    lambda D26, F22, F23, F24, F25, F26: (empty(D26) or safe_convert(D26) is None or safe_convert(D26) <= 0) or (safe_convert(D26) > 0 and any(safe_convert(val) is not None and not empty(val) for val in [F22, F23, F24, F25, F26])),
    lambda 血费, ABO血型: (float(血费) <= 0 or not empty(ABO血型)),
    lambda ABO血型, RH血型: (empty(ABO血型)) or
                         (not empty(RH血型) and(
                             (str(ABO血型).strip().casefold() in ["1", "2", "3", "4"] and str(RH血型).strip() in ["1", "2"]) or
                             (str(ABO血型).strip() == "5" and "3" == str(RH血型).strip()) or
                             (str(ABO血型).strip() == "6" and "4" == str(RH血型).strip()))),
    lambda 主要诊断编码, 所有其他诊断编码 , 损伤_中毒的外部原因: not empty(损伤_中毒的外部原因) or not iter_iter(grange("S00-S99") + grange("T00-T98"), (所有其他诊断编码 if isinstance(所有其他诊断编码, list) else [所有其他诊断编码])+[主要诊断编码]),
    lambda 主要诊断编码, 病理诊断编码: not include(grange("D37-D48"), 主要诊断编码) or ("/1" in 病理诊断编码),  #16
    lambda 主要诊断编码, 病理诊断编码: not include(grange("D10-D36"), 主要诊断编码) or ("/0" in 病理诊断编码),
    lambda 主要诊断编码, 病理诊断编码: not include(grange("D00-D09"), 主要诊断编码) or ("/2" in 病理诊断编码),
    lambda 主要诊断编码, 病理诊断编码: not include(grange("C80-C97"), 主要诊断编码) or ("/3" in 病理诊断编码),
    lambda 主要诊断编码, 病理诊断编码: not include(grange("C77-C79"), 主要诊断编码) or ("/6" in 病理诊断编码),
    lambda 主要诊断编码, 病理诊断编码: not include(grange("C00-C76"), 主要诊断编码) or ("/3" in 病理诊断编码),
    lambda 主要诊断编码, 病理诊断编码: not include(grange("C00-C97") + grange("D00-D48"), 主要诊断编码) or not empty(病理诊断编码),
    lambda 主要诊断编码, 所有其他诊断编码: not repeat((所有其他诊断编码 if isinstance(所有其他诊断编码, list) else [所有其他诊断编码])+[主要诊断编码]),   #所有诊断不重复
    lambda 主要诊断编码, 所有其他诊断编码: not any(code.startswith(prefix) for prefix in ["O80","O81","O82","O83","O84"] for code in ([主要诊断编码] + (所有其他诊断编码 if isinstance(所有其他诊断编码, list) else [所有其他诊断编码])) if not empty(code)) or any(code.startswith("Z37") or code.startswith("Z38") for code in (所有其他诊断编码 if isinstance(所有其他诊断编码, list) else [所有其他诊断编码]) if not empty(code)),
    lambda 主要诊断编码: not include(["T96","T97"], 主要诊断编码),  # 25
    lambda 主要诊断编码: "Z98" not in 主要诊断编码,
    lambda 主要诊断编码: not include(["Z96","Z97"], 主要诊断编码),
    lambda 主要诊断编码: "Z95" not in 主要诊断编码,
    lambda 主要诊断编码: "Z94" not in 主要诊断编码,
    lambda 主要诊断编码: "Z93" not in 主要诊断编码,  #30
    lambda 主要诊断编码: "Z92" not in 主要诊断编码,
    lambda 主要诊断编码: "Z91" not in 主要诊断编码,
    lambda 主要诊断编码: "Z88" not in 主要诊断编码,
    lambda 主要诊断编码: not include(["Z86","Z87"], 主要诊断编码),
    lambda 主要诊断编码: "Z85" not in 主要诊断编码,
    lambda 主要诊断编码: not include(["Z37","Z38"], 主要诊断编码),
    lambda 主要诊断编码: "Z33" not in 主要诊断编码,
    lambda 主要诊断编码: not include(["U80","U81","U82","U83","U84","U85"], 主要诊断编码),
    lambda 主要诊断编码: "T31" not in 主要诊断编码,
    lambda 主要诊断编码: "I69" not in 主要诊断编码,
    lambda 主要诊断编码: not include(["I50.900x007","I50.900x008","I50.900x009","I50.900x010","I50.900x014","I50.900x015","I50.900x016","I50.902","I50.903","I50.904","I50.905","I51.900x003","I51.900x005"], 主要诊断编码),
    lambda 主要诊断编码: not include(["B95", "B97"], 主要诊断编码),
    lambda 主要诊断编码, 所有其他诊断编码: not iter_startswith((所有其他诊断编码 if isinstance(所有其他诊断编码, list) else [所有其他诊断编码]) + [主要诊断编码], "O06"), #43
    lambda 主任_副主任_医师: not empty(主任_副主任_医师),
    lambda 质控日期, 出院时间: (质控日期 and 出院时间 and isinstance(质控日期, str) and isinstance(出院时间, str) and (
            mktime(strptime(质控日期 + " 00:00:00" if len(质控日期) == 10 else 质控日期, "%Y-%m-%d %H:%M:%S")) -
            mktime(strptime(出院时间 + " 00:00:00" if len(出院时间) == 10 else 出院时间, "%Y-%m-%d %H:%M:%S")) < 72*60*60)),
    lambda 职业: not emptys(职业),
    lambda 责任护士: not empty(责任护士),
    lambda 医疗机构: not empty(医疗机构),  #48
    lambda 医疗付费方式, 工作单位, 工作单位电话, 工作邮编: (not empty(工作单位) and not empty(工作单位电话) and not empty(工作邮编)) or 医疗付费方式 != "城镇职工基本医疗保险",
    lambda 医疗付费方式: not empty(医疗付费方式),
    lambda 药物过敏, 过敏药物: (not empty(过敏药物) if str(药物过敏) == "2" else True),
    lambda 姓名: not empty(姓名),   #52
    lambda 性别, 主要手术操作编码, 所有其他手术操作编码: not iter_iter_startswith(["60", "61", "62", "63", "64"], (所有其他手术操作编码 if isinstance(所有其他手术操作编码, list) else [所有其他手术操作编码])+[主要手术操作编码]) or str(性别) != "2",
    lambda 性别, 主要手术操作编码, 所有其他手术操作编码: not iter_iter_startswith([str(i) for i in range(65, 76)], (所有其他手术操作编码 if isinstance(所有其他手术操作编码, list) else [所有其他手术操作编码])+[主要手术操作编码]) or str(性别) != "1",
    lambda 性别, 门急诊诊断编码, 主要诊断编码, 所有其他诊断编码: not iter_iter(grange("C60-C63") + grange("N40-N51") + grange("E29"), [主要诊断编码] + (所有其他诊断编码 if isinstance(所有其他诊断编码, list) else [所有其他诊断编码]) + [门急诊诊断编码]) or str(性别) != "2",
    lambda 性别, 门急诊诊断编码, 主要诊断编码, 所有其他诊断编码: not iter_iter(grange("C51-C58") + grange("N70-N77") + grange("N80-N98") + grange("O00-O99") + grange("E28"), [主要诊断编码] + (所有其他诊断编码 if isinstance(所有其他诊断编码, list) else [所有其他诊断编码]) + [门急诊诊断编码]) or str(性别) != "1",
    lambda 性别, 年龄, 婚姻: str(婚姻) == "1" or (str(性别) == "1" and int(年龄) >= 22) or (str(性别) == "2" and int(年龄) >= 20),
    lambda 性别: not empty(性别),  # 58
    lambda 新生儿入院体重: empty(新生儿入院体重) or ((safe_convert(新生儿入院体重) is not None and int(新生儿入院体重) % 10 == 0)),
    lambda 新生儿入院体重: empty(新生儿入院体重) or (safe_convert(新生儿入院体重) is not None and 100 < safe_convert(新生儿入院体重) < 9999),
    lambda 新生儿入院体重, 年龄, 年龄不足1周岁的年龄_天: empty(新生儿入院体重) or (safe_convert(年龄不足1周岁的年龄_天) is not None and safe_convert(年龄不足1周岁的年龄_天) <= 28) or (safe_convert(新生儿入院体重) is not None and safe_convert(新生儿入院体重) <= 0),
    lambda 所有新生儿出生体重: not true_list(所有新生儿出生体重) or [float(each) % 10 == float(0) for each in 所有新生儿出生体重 if not empty(each)],
    lambda 所有新生儿出生体重: not true_list(所有新生儿出生体重) or [100 < float(each) < 9999 for each in 所有新生儿出生体重 if not empty(each)],
    lambda 所有新生儿出生体重, 主要诊断编码, 所有其他诊断编码, 年龄, 年龄不足1周岁的年龄_天: not true_list(所有新生儿出生体重) or (iter_iter(["Z37.0", "Z37.2", "Z37.3", "Z37.5", "Z37.6"],(所有其他诊断编码 if isinstance(所有其他诊断编码, list) else [所有其他诊断编码])+[主要诊断编码]) or (年龄 == 0 and float(年龄不足1周岁的年龄_天) <= 28) or [float(each) <= 0 for each in 所有新生儿出生体重 if not empty(each)]),
    lambda 现住址邮编: not emptys(现住址邮编) or 现住址邮编 == "-",   #65
    lambda 损伤_中毒的外部原因: empty(损伤_中毒的外部原因) or str(损伤_中毒的外部原因).strip()[0] in ["V", "W", "X", "Y"],
    lambda 现住址电话: empty(现住址电话) or validate_phone(现住址电话),
    lambda 现住址电话: not empty(现住址电话) or 现住址电话 == "-",  #68
    lambda 现住址: not empty(现住址),  # 69
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 手术日期, 入院时间, 出院时间: empty(手术日期) or time_compare(入院时间, 手术日期, 出院时间),
    lambda 是否有31天再入院计划, 目的: not emptys(目的) or str(是否有31天再入院计划) != "2",   #90
    lambda 是否有31天再入院计划: not empty(是否有31天再入院计划),
    lambda 实际住院天数: 0 < float(实际住院天数) < 8000,
    lambda 实际住院天数: not empty(实际住院天数),   #93
    lambda 实际住院天数, 入院时间, 出院时间: abs(get_timechuo(出院时间) - get_timechuo(入院时间) - float(实际住院天数) * 86400 ) < 86400 ,
    lambda 身份证号, 性别: (str(身份证号).strip() == "-" or (len(get_str(身份证号)) == 18 and get_str(身份证号) != "-" and get_str(身份证号)[16].isdigit() and ((int(get_str(身份证号)[16]) % 2 == 1 and str(性别) == "1") or (int(get_str(身份证号)[16]) % 2 == 0 and str(性别) == "2")))),
    lambda 身份证号: str(身份证号).strip() not in ("", "None"), #96
    lambda 身份证号: (str(身份证号).strip() == "-" or "-" not in get_str(身份证号)),
    lambda 入院途径, 转诊机构: not empty(转诊机构) or str(入院途径) != "3",#98
    lambda 入院途径, 主要诊断: not include(["A40","A41","E89","I26.9","I80.207","I63","I80.208","I82.805","J96,O70","O71","O74","O90.0","O90.1","O90.2","R96","T81.0","T81.1","T81.3","T81.7","T81.8","T81.9","T88.2","T88.3","T88.5"], 主要诊断) or 入院途径 != "2",
    lambda 入院途径: not empty(入院途径),
    lambda 入院时间, 出院时间: ((in_time := convert_date_format(入院时间)) and (out_time := convert_date_format(出院时间)) and strptime(in_time, "%Y/%m/%d %H:%M:%S") < strptime(out_time, "%Y/%m/%d %H:%M:%S")),
    lambda 入院时间: not empty(入院时间),
    lambda 入院时间, 出生日期, 不足一周岁患者年龄: (str(不足一周岁患者年龄) != "0" if 不足一周岁患者年龄 is not None else True) and (datetime.strptime(入院时间, "%Y-%m-%d %H:%M:%S") > datetime.strptime(出生日期, "%Y-%m-%d") and(datetime.strptime(入院时间, "%Y-%m-%d %H:%M:%S") - datetime.strptime(出生日期, "%Y-%m-%d")).days / 365.25 >= 1),
    lambda 入院科别: not empty(入院科别),   #104
    lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),   #105
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),   #105
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),   #105
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),   #105
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),#101
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),   #105
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),
    #lambda 诊断名称, 诊断编码, 入院病情, 出院情况: (len(true_list([诊断名称, 诊断编码, 入院病情, 出院情况])) in (0, 4) and(str(入院病情).strip() in ("1", "2", "3", "4") if 入院病情 is not None and 入院病情 != "" else True) and(str(出院情况).strip() in ("1", "2", "3", "4", "9") if 出院情况 is not None and 出院情况 != "" else True)),# 107
    lambda 入院天龄, 年龄: (True if not (isinstance(入院天龄, str) and 入院天龄.strip().isdigit()) else(int(入院天龄) <= 28 and str(年龄).strip() == "0") if int(入院天龄) <= 28 else False),
    lambda 年龄, 职业: ((empty(年龄) and empty(职业)) or(str(年龄).strip().isdigit() and (str(职业) == "31" or int(年龄) < 3 or int(年龄) > 14))),
    lambda 年龄, 入院时间, 出生日期: abs(int(入院时间[:4]) - int(出生日期[:4]) - float(str(年龄).strip() or '0')) <= 1,
    lambda 年龄, 新生儿入院体重: not empty(新生儿入院体重) or int(年龄) != "0",  #111
    lambda 年龄, 所有新生儿出生体重: true_list(所有新生儿出生体重) or int(年龄) != "0",
    lambda 年龄, 婚姻: str(婚姻) not in ['2','3','4'] or int(年龄) > 14,
    lambda 年龄, 联系人关系: str(联系人关系) not in ['1','2','3','4'] or int(年龄) > 14,
    lambda 年龄, 职业: str(职业) not in ['11','37','80'] or int(年龄) > 14,
    lambda 年龄: not empty(年龄),
    lambda 年龄: int(年龄) <= 200,
    lambda 年龄: not empty(年龄),   # 118
    lambda 年龄不足1周岁的年龄, 年龄: (True if not (isinstance(年龄, str) and 年龄.strip().isdigit()) else(年龄不足1周岁的年龄 in ("0", "", None) if int(年龄) > 0 else False)),   #122
    lambda 年龄, 医疗付费方式: str(医疗付费方式) not in ("1.1", "1.2") or int(年龄) >= 16,
    lambda 年龄, 所有出院诊断编码: not iter_iter(grange("C50-C63"), 所有出院诊断编码) or int(年龄) >= 12,
    lambda 年龄不足1周岁的年龄, 年龄: (empty(年龄不足1周岁的年龄) or ((val := safe_int(年龄不足1周岁的年龄)) is not None and 0 < val < 365 and str(年龄).strip() == "0")),  #122
    lambda 民族: not emptys(民族),
    lambda 门急诊诊断编码: not empty(门急诊诊断编码),
    lambda 门急诊诊断名称: not empty(门急诊诊断名称),
    lambda 门_急_诊诊断编码, 主要诊断编码, 所有其他诊编码: not iter_iter(grange("V01-V99","W00-W99","X00-X99","Y00-Y98"), [主要诊断编码, 门_急_诊诊断编码]+(所有其他诊编码 if isinstance(所有其他诊编码, list) else [所有其他诊编码])),
    lambda 门_急_诊诊断编码, 所有出院诊断编码: not (has((所有出院诊断编码 if isinstance(所有出院诊断编码, list) else [所有出院诊断编码])+[门_急_诊诊断编码], "/") and iter_startswith((所有出院诊断编码 if isinstance(所有出院诊断编码, list) else [所有出院诊断编码])+[门_急_诊诊断编码], "M")),
    lambda 麻醉费, 所有麻醉医师: float(麻醉费 or 0) <= 0 or len(true_list(所有麻醉医师)) > 0,
    lambda 麻醉费, 所有麻醉方式: float(麻醉费 or 0) <= 0 or len(true_list(所有麻醉方式)) > 0,
    lambda 联系人姓名, 姓名: 联系人姓名 != 姓名,  #130
    lambda 联系人姓名: not empty(联系人姓名),
    lambda 联系人姓名, 关系, 住址, 电话: not emptys(联系人姓名) and not emptys(关系) and not emptys(住址) and not emptys(电话),
    lambda 联系人关系, 婚姻: str(婚姻) != "1" or str(联系人关系) != "1",
    lambda 联系人关系:  not emptys(联系人关系) and str(联系人关系) != "0",
    lambda 联系人关系:  not emptys(联系人关系),
    lambda 单位电话: (empty(单位电话) or len(clean_phone(单位电话)) in (7, 9, 10, 11)),
    lambda 联系人电话: not emptys(联系人电话),
    lambda 联系人地址: not emptys(联系人地址),  #138
    lambda 离院方式, 转院_拟接收医疗机构名称: not emptys(转院_拟接收医疗机构名称) or str(离院方式) != "2",
    lambda 离院方式, 转社区_拟接收医疗机构名称: not emptys(转社区_拟接收医疗机构名称) or str(离院方式) != "3",
    lambda 离院方式, 死亡患者尸检: not emptys(死亡患者尸检) or str(离院方式) != "5",
    lambda 离院方式: not empty(离院方式),
    lambda 科主任: not empty(科主任),
    lambda 入院时间, 出院时间, 进重症监护室时间, 出重症监护室时间: check_icu_time(入院时间, 出院时间, 进重症监护室时间, 出重症监护室时间),
    lambda 入院时间, 出院时间, 进重症监护室时间, 出重症监护室时间: check_icu_time(入院时间, 出院时间, 进重症监护室时间, 出重症监护室时间),
    lambda 入院时间, 出院时间, 进重症监护室时间, 出重症监护室时间: check_icu_time(入院时间, 出院时间, 进重症监护室时间, 出重症监护室时间),
    lambda 入院时间, 出院时间, 进重症监护室时间, 出重症监护室时间: check_icu_time(入院时间, 出院时间, 进重症监护室时间, 出重症监护室时间),
    lambda 入院时间, 出院时间, 进重症监护室时间, 出重症监护室时间: check_icu_time(入院时间, 出院时间, 进重症监护室时间, 出重症监护室时间),
    lambda 籍贯: not empty(籍贯),
    lambda 婚姻状况, 联系人关系: str(联系人关系) != "1" or str(婚姻状况) != "3",
    lambda 婚姻: not empty(婚姻),  #147
    lambda 户口邮编: str(户口邮编).isdigit() and len(str(户口邮编).zfill(6)) == 6,
    lambda 户口地址邮编: not emptys(户口地址邮编) or 户口地址邮编 == "-",
    lambda 户口地址: not emptys(户口地址),
    lambda 国籍, 民族: 民族 != "66" or 国籍 != "中国",  #151
    lambda 国籍: not emptys(国籍),
    lambda 工作邮编: 工作邮编 is not None and ((str(工作邮编).isdigit() and len(str(工作邮编)) == 6) or (str(工作邮编).strip() == "-")),
    lambda 工作单位邮编: not emptys(工作单位邮编) or 工作单位邮编 == "-",  #154
    lambda 工作单位及地址: not emptys(工作单位及地址),
    lambda 工作单位电话: not emptys(工作单位电话),
    lambda 第次住院: not empty(第次住院), #157
    lambda 单位电话: (empty(单位电话) or len(clean_phone(单位电话)) in (7, 8, 9, 10, 11,12)),
    lambda 所有出院诊断编码, 所有颅脑损失患者昏迷时间: true_list(所有颅脑损失患者昏迷时间) or not iter_startswith(所有出院诊断编码, "S06"),  #159
    lambda 所有其他诊断编码, 所有新生儿出生体重: (len(true_list(所有新生儿出生体重)) > 0 or not any(diag.startswith(prefix) for prefix in newborn_prefixes for diag in 所有其他诊断编码 if not empty(diag))),
    lambda 出院时间: not empty(出院时间),
    lambda 出院科别: not empty(出院科别),
    lambda 出生日期, 身份证号: (lambda birth_date, id_date: get_str(身份证号) in ("", "-") or (len(birth_date) == 8 and len(id_date) == 8 and birth_date == id_date))(出生日期.strip().replace("/", "").replace("-", ""), get_str(身份证号)[6:14] if len(get_str(身份证号)) >= 14 else ""),
    lambda 出生日期: not empty(出生日期), #164
    lambda 出生地: not emptys(出生地),
    lambda 病理诊断费, 病理号: not empty(病理号) or float(病理诊断费) <= 0,
    lambda 病理诊断编码: 病理诊断编码.startswith("M") and "/" in 病理诊断编码 or 病理诊断编码 == "NA" or empty(病理诊断编码),
    lambda 病理号, 病理诊断: not empty(病理诊断) or empty(病理号),
    lambda 病案号: not empty(病案号),
    lambda 编码员: not empty(编码员),  #170
    lambda 手术费用, 主要手术编码, 主要手术名称, 主要手术日期, 主要手术术者: check_surgery_data({"D20": 手术费用,"C14x01C": 主要手术编码,"C15x01N": 主要手术名称,"C16x01": 主要手术日期,"C18x01": 主要手术术者}),
    lambda 主要手术_名称, 主要手术级别, 主要手术操作切口等级: check_surgery_name_and_levels(主要手术_名称, 主要手术级别, 主要手术操作切口等级),
    lambda 主要手术_麻醉方式, 麻醉医师: not empty(麻醉医师) or empty(主要手术_麻醉方式),
    lambda 主要出院诊断_入院病情:not empty(主要出院诊断_入院病情),
    lambda 主要出院诊断_名称:not empty(主要出院诊断_名称),
    lambda 主要出院诊断_编码:not empty(主要出院诊断_编码),
    lambda 入院_病房:not empty(入院_病房),
    lambda 出院_病房:not empty(出院_病房),  #178
    lambda 不足一周岁_年龄, 所有诊断编码, 年龄: (False not in [each.startswith("P") for each in list(filter(None, 所有诊断编码))]) or empty(不足一周岁_年龄) or int(不足一周岁_年龄) >= 28 or int(年龄) > 0,
    lambda 总费用, 分费用合: float(总费用) <= float(分费用合)*1.200001,
    lambda 总费用, 自付金额: not empty(总费用) and not empty(自付金额) and float(总费用 or 0) >= float(自付金额 or 0),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J42") if 主要诊断编码 is not None else False) or any(str(x).startswith("J42") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J43") if 主要诊断编码 is not None else False) or any(str(x).startswith("J43") for x in 所有其他诊断编码 if x is not None))),
    lambda 年龄, 主要诊断编码, 其他诊断编码: not ((isinstance(年龄, (int, float)) or (isinstance(年龄, str) and 年龄.strip().replace(".", "", 1).isdigit())) and float(年龄) < 15 and ((str(主要诊断编码).startswith("J40") if 主要诊断编码 is not None else False) or any(str(x).startswith("J40") for x in 其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("A40") or str(主要诊断编码).startswith("A41") if 主要诊断编码 is not None else False) or any((str(x).startswith("A40") or str(x).startswith("A41")) for x in 所有其他诊断编码 if x is not None)) and any(str(x).startswith("A49") for x in 所有其他诊断编码 if x is not None)), #184
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N13.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.2") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N39.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("N39.0") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N13.3") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.3") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N20.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.2") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S52.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("S52.2") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S52.3") if 主要诊断编码 is not None else False) or any(str(x).startswith("S52.3") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S52.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("S52.5") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S52.8") if 主要诊断编码 is not None else False) or any(str(x).startswith("S52.8") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M51.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("M51.2") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M54.3") if 主要诊断编码 is not None else False) or any(str(x).startswith("M54.3") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.2") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.0") for x in 所有其他诊断编码 if x is not None))),#191
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.2") for x in (所有其他诊断编码 if isinstance(所有其他诊断编码, list) else [所有其他诊断编码]) if x is not None))and(any(any(str(x).startswith(code) for x in ([主要诊断编码] + (所有其他诊断编码 if isinstance(所有其他诊断编码, list) else [所有其他诊断编码])) if x is not None)for code in ["K81.1", "K81.8", "K81.9"]))),
    lambda 主要诊断编码, 所有其他诊断编码: (((str(主要诊断编码).startswith("I50.902") if 主要诊断编码 is not None else False) or any(str(x).startswith("I50.902") for x in 所有其他诊断编码 if x is not None))or not (((str(主要诊断编码).startswith("I11.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("I11.9") for x in 所有其他诊断编码 if x is not None))and(((str(主要诊断编码).startswith("I50") and not str(主要诊断编码).startswith("I50.902")) if 主要诊断编码 is not None else False) or any(str(x).startswith("I50") and not str(x).startswith("I50.902") for x in 所有其他诊断编码 if x is not None)))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J44.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("J44.9") for x in (所有其他诊断编码 if isinstance(所有其他诊断编码, list) else [所有其他诊断编码]) if x is not None))and(any(any(str(x).startswith(code) for x in ([主要诊断编码] + (所有其他诊断编码 if isinstance(所有其他诊断编码, list) else [所有其他诊断编码])) if x is not None)for code in ["J13", "J14", "J15", "J18", "J20", "J21", "J22"]))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J42") if 主要诊断编码 is not None else False) or any(str(x).startswith("J42") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J43.904") if 主要诊断编码 is not None else False) or any(str(x).startswith("J43.904") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("R09.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("R09.1") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J94.8") if 主要诊断编码 is not None else False) or any(str(x).startswith("J94.8") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I15.102") if 主要诊断编码 is not None else False) or any(str(x).startswith("I15.102") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I11.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("I11.901") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I11.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I11.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I12.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I12.0") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I63.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("I63.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I65") if 主要诊断编码 is not None else False) or any(str(x).startswith("I65") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I63.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("I63.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I66") if 主要诊断编码 is not None else False) or any(str(x).startswith("I66") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I06.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.9") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I06.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.0") for x in 所有其他诊断编码 if x is not None))),#202
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I06.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.1") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.1") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I06.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.0") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.1") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I06.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.2") for x in 所有其他诊断编码 if x is not None))),#205
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I06.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.2") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.2") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I06.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.1") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.1") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I06.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.1") for x in 所有其他诊断编码 if x is not None))),#208
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.2") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I06.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.2") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.2") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I06.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.0") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I07.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07.9") for x in 所有其他诊断编码 if x is not None))),#211
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I07.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07.1") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.2") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I07.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07.1") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.1") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I07.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07.1") for x in 所有其他诊断编码 if x is not None))),#214
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I07.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07.0") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I06.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I07.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07.9") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I06.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.1") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I07.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07.1") for x in 所有其他诊断编码 if x is not None))),#217
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.9") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I06.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.9") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I07.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07.9") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.2") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I06.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.1") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I07.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07.1") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.0") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I06.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.1") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I07.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07.1") for x in 所有其他诊断编码 if x is not None))),#220
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.1") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I06.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.1") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I07.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07.1") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.2") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I06.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.2") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I07.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07.2") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.2") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I06.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.0") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I07.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07.0") for x in 所有其他诊断编码 if x is not None))),#223
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.2") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I06.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06.2") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I07.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07.1") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05.2") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I09.802") if 主要诊断编码 is not None else False) or any(str(x).startswith("I09.802") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I50.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I50.1") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J81") if 主要诊断编码 is not None else False) or any(str(x).startswith("J81") for x in 所有其他诊断编码 if x is not None))),#226
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I50") if 主要诊断编码 is not None else False) or any(str(x).startswith("I50") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N18.8") if 主要诊断编码 is not None else False) or any(str(x).startswith("N18.8") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("D64") if 主要诊断编码 is not None else False) or any(str(x).startswith("D64") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N18.001") if 主要诊断编码 is not None else False) or any(str(x).startswith("N18.001") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("D64") if 主要诊断编码 is not None else False) or any(str(x).startswith("D64") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N18") if 主要诊断编码 is not None else False) or any(str(x).startswith("N18") for x in 所有其他诊断编码 if x is not None))),#229
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E16.800x901") if 主要诊断编码 is not None else False) or any(str(x).startswith("E16.800x901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I10") if 主要诊断编码 is not None else False) or any(str(x).startswith("I10") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E16.800x901") if 主要诊断编码 is not None else False) or any(str(x).startswith("E16.800x901") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I10") if 主要诊断编码 is not None else False) or any(str(x).startswith("I10") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("E66") if 主要诊断编码 is not None else False) or any(str(x).startswith("E66") for x in 所有其他诊断编码 if x is not None))),#231
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E66") if 主要诊断编码 is not None else False) or any(str(x).startswith("E66") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I10") if 主要诊断编码 is not None else False) or any(str(x).startswith("I10") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("E10") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10") for x in 所有其他诊断编码 if x is not None))),#232
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E66") if 主要诊断编码 is not None else False) or any(str(x).startswith("E66") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I10") if 主要诊断编码 is not None else False) or any(str(x).startswith("I10") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("E11") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11") for x in 所有其他诊断编码 if x is not None))),#233
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E66") if 主要诊断编码 is not None else False) or any(str(x).startswith("E66") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I10") if 主要诊断编码 is not None else False) or any(str(x).startswith("I10") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("E14") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14") for x in 所有其他诊断编码 if x is not None))),#234
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K31.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("K31.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.2") for x in 所有其他诊断编码 if x is not None))),#235
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K25.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.2") for x in 所有其他诊断编码 if x is not None))),#236
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.2") for x in 所有其他诊断编码 if x is not None))),#237
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K27.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.2") for x in 所有其他诊断编码 if x is not None))),#238
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K28.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("K28.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.2") for x in 所有其他诊断编码 if x is not None))),#239
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J35.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("J35.100") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J35.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("J35.200") for x in 所有其他诊断编码 if x is not None))),#240
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M06.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("M06.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J84.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("J84.101") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M06.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("M06.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J84.105") if 主要诊断编码 is not None else False) or any(str(x).startswith("J84.105") for x in 所有其他诊断编码 if x is not None))),#242
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I50.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("I50.101") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J81") if 主要诊断编码 is not None else False) or any(str(x).startswith("J81") for x in 所有其他诊断编码 if x is not None))),#243
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B49.x00x007") if 主要诊断编码 is not None else False) or any(str(x).startswith("B49.x00x007") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J18.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("J18.9") for x in 所有其他诊断编码 if x is not None))),#244
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N20.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N20.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.1") for x in 所有其他诊断编码 if x is not None))),#245
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N20.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.100") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("N13.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.4") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("N10.x02") if 主要诊断编码 is not None else False) or any(str(x).startswith("N10.x02") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J18.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("J18.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("A49") if 主要诊断编码 is not None else False) or any(str(x).startswith("A49") for x in 所有其他诊断编码 if x is not None))),#247
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N13.301") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.301") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N13.504") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.504") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.300x081") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.300x081") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.4") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.800x082") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.800x082") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.4") for x in 所有其他诊断编码 if x is not None))),#250
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.100") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I85.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I85.900x001") for x in 所有其他诊断编码 if x is not None))),#251
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K70.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K70.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I85.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I85.900x001") for x in 所有其他诊断编码 if x is not None))),#252
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K70.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K70.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I85.000x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I85.000x001") for x in 所有其他诊断编码 if x is not None))),#253
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K70.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K70.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I86.400x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.400x001") for x in 所有其他诊断编码 if x is not None))),#254
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K70.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K70.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I86.400x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.400x011") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K70.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K70.300") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I85.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I85.900x001") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I86.400x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.400x001") for x in 所有其他诊断编码 if x is not None))),#256
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K70.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K70.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I86.800x014") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.800x014") for x in 所有其他诊断编码 if x is not None))),#257
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I85.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I85.900x001") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I85.000x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I85.000x001") for x in 所有其他诊断编码 if x is not None))),#259
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I86.400x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.400x001") for x in 所有其他诊断编码 if x is not None))),#260
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I86.400x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.400x011") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K70.300") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I85.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I85.900x001") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I86.400x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.400x001") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K70.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I86.800x014") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.800x014") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.600x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.600x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I85.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I85.900x001") for x in 所有其他诊断编码 if x is not None))),#264
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.600x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.600x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I85.000x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I85.000x001") for x in 所有其他诊断编码 if x is not None))),#265
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.600x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.600x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I86.400x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.400x001") for x in 所有其他诊断编码 if x is not None))),#266
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.600x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.600x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I86.400x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.400x011") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.600x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.600x003") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I85.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I85.900x001") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I86.400x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.400x001") for x in 所有其他诊断编码 if x is not None))),#268
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.600x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.600x003") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I86.800x014") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.800x014") for x in 所有其他诊断编码 if x is not None))),#269
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.5") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.0") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J44.806") if 主要诊断编码 is not None else False) or any(str(x).startswith("J44.806") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J43.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("J43.9") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J47.x00") if 主要诊断编码 is not None else False) or any(str(x).startswith("J47.x00") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("R04.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("R04.200") for x in 所有其他诊断编码 if x is not None))),#272
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.5") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81") for x in 所有其他诊断编码 if x is not None))),#273
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K31.800x806") if 主要诊断编码 is not None else False) or any(str(x).startswith("K31.800x806") for x in 所有其他诊断编码 if x is not None))),#274
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.2") for x in 所有其他诊断编码 if x is not None))),#275
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.9") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K31.800x806") if 主要诊断编码 is not None else False) or any(str(x).startswith("K31.800x806") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.2") for x in 所有其他诊断编码 if x is not None))),#276
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B24.x01") if 主要诊断编码 is not None else False) or any(str(x).startswith("B24.x01") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B38.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("B38.200") for x in 所有其他诊断编码 if x is not None))),#277
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B24.x01") if 主要诊断编码 is not None else False) or any(str(x).startswith("B24.x01") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("A16.200x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("A16.200x002") for x in 所有其他诊断编码 if x is not None))),#278
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B24.x01") if 主要诊断编码 is not None else False) or any(str(x).startswith("B24.x01") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("C46") if 主要诊断编码 is not None else False) or any(str(x).startswith("C46") for x in 所有其他诊断编码 if x is not None))),#279
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.100x087") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.100x087") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.4") for x in 所有其他诊断编码 if x is not None))),#280
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I35.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I35.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I35.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I35.1") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I34.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I34.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I34.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I34.2") for x in 所有其他诊断编码 if x is not None))),#282
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I34.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I34.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I35.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I35.1") for x in 所有其他诊断编码 if x is not None))),#283
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I36.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I36.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I36.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I36.1") for x in 所有其他诊断编码 if x is not None))),#284
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I37.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("I37.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I37.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("I37.1") for x in 所有其他诊断编码 if x is not None))),#285
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33.x") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33.x") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B18") if 主要诊断编码 is not None else False) or any(str(x).startswith("B18") for x in 所有其他诊断编码 if x is not None))),#286
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.900x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.900x002") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K92.208") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.208") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K27.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.5") for x in 所有其他诊断编码 if x is not None))),#287
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E16.800x901") if 主要诊断编码 is not None else False) or any(str(x).startswith("E16.800x901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E16.801") if 主要诊断编码 is not None else False) or any(str(x).startswith("E16.801") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E10.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E16.801") if 主要诊断编码 is not None else False) or any(str(x).startswith("E16.801") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E11.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E16.801") if 主要诊断编码 is not None else False) or any(str(x).startswith("E16.801") for x in 所有其他诊断编码 if x is not None))),#290
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E14.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E16.801") if 主要诊断编码 is not None else False) or any(str(x).startswith("E16.801") for x in 所有其他诊断编码 if x is not None))),#291
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B02.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("B02.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M79.207") if 主要诊断编码 is not None else False) or any(str(x).startswith("M79.207") for x in 所有其他诊断编码 if x is not None))),#292
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I20") if 主要诊断编码 is not None else False) or any(str(x).startswith("I20") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I25.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("I25.2") for x in 所有其他诊断编码 if x is not None))),#293
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.100x087") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.100x087") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.100x085") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.100x085") for x in 所有其他诊断编码 if x is not None))),#294
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.4") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.600") for x in 所有其他诊断编码 if x is not None))),#295
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S83.201") if 主要诊断编码 is not None else False) or any(str(x).startswith("S83.201") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S83.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("S83.5") for x in 所有其他诊断编码 if x is not None))),#296
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S83.202") if 主要诊断编码 is not None else False) or any(str(x).startswith("S83.202") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S83.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("S83.5") for x in 所有其他诊断编码 if x is not None))),#297
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.4") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.800x081") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.800x081") for x in 所有其他诊断编码 if x is not None))),#298
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.4") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.800x082") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.800x082") for x in 所有其他诊断编码 if x is not None))),#299
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.600") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.500") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.500") for x in 所有其他诊断编码 if x is not None))),#300
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.600") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.500") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.500") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("S82.300x081") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.300x081") for x in 所有其他诊断编码 if x is not None))),#301
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M54.502") if 主要诊断编码 is not None else False) or any(str(x).startswith("M54.502") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M54.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("M54.300") for x in 所有其他诊断编码 if x is not None))),#302
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B02.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("B02.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G58.001") if 主要诊断编码 is not None else False) or any(str(x).startswith("G58.001") for x in 所有其他诊断编码 if x is not None))),#303
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.702") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.702") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S62.61") if 主要诊断编码 is not None else False) or any(str(x).startswith("S62.61") for x in 所有其他诊断编码 if x is not None))),#304
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.701") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.701") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S62.11") if 主要诊断编码 is not None else False) or any(str(x).startswith("S62.11") for x in 所有其他诊断编码 if x is not None))),#305
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S62.810") if 主要诊断编码 is not None else False) or any(str(x).startswith("S62.810") for x in 所有其他诊断编码 if x is not None))),#306
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S63.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("S63.2") for x in 所有其他诊断编码 if x is not None))),#307
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S63.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("S63.0") for x in 所有其他诊断编码 if x is not None))),#308
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S63.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("S63.1") for x in 所有其他诊断编码 if x is not None))),#309
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.702") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.702") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S63.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("S63.1") for x in 所有其他诊断编码 if x is not None))),#310
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.701") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.701") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S63.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("S63.0") for x in 所有其他诊断编码 if x is not None))),#311
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K46.900x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K46.900x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K56.7") if 主要诊断编码 is not None else False) or any(str(x).startswith("K56.7") for x in 所有其他诊断编码 if x is not None))),#312
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("G45.801") if 主要诊断编码 is not None else False) or any(str(x).startswith("G45.801") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I77.102") if 主要诊断编码 is not None else False) or any(str(x).startswith("I77.102") for x in 所有其他诊断编码 if x is not None))),#313
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("G45.801") if 主要诊断编码 is not None else False) or any(str(x).startswith("G45.801") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I74.801") if 主要诊断编码 is not None else False) or any(str(x).startswith("I74.801") for x in 所有其他诊断编码 if x is not None))),#314
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B49.x00x007") if 主要诊断编码 is not None else False) or any(str(x).startswith("B49.x00x007") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("A09.900x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("A09.900x003") for x in 所有其他诊断编码 if x is not None))),#315
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.200x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.200x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.000") for x in 所有其他诊断编码 if x is not None))),#316
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K56.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("K56.700") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K55.004") if 主要诊断编码 is not None else False) or any(str(x).startswith("K55.004") for x in 所有其他诊断编码 if x is not None))),#317
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K55.010") if 主要诊断编码 is not None else False) or any(str(x).startswith("K55.010") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K55.004") if 主要诊断编码 is not None else False) or any(str(x).startswith("K55.004") for x in 所有其他诊断编码 if x is not None))),#318
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K55.006") if 主要诊断编码 is not None else False) or any(str(x).startswith("K55.006") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K55.004") if 主要诊断编码 is not None else False) or any(str(x).startswith("K55.004") for x in 所有其他诊断编码 if x is not None))),#319
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K40.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("K40.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K40.902") if 主要诊断编码 is not None else False) or any(str(x).startswith("K40.902") for x in 所有其他诊断编码 if x is not None))),#320
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K40.900x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K40.900x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K40.900x004") if 主要诊断编码 is not None else False) or any(str(x).startswith("K40.900x004") for x in 所有其他诊断编码 if x is not None))),#321
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.000") for x in 所有其他诊断编码 if x is not None))),#322
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.0") for x in 所有其他诊断编码 if x is not None))),#323
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.004") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.004") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.0") for x in 所有其他诊断编码 if x is not None))),#324
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.901") for x in 所有其他诊断编码 if x is not None))),#325
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.004") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.004") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.901") for x in 所有其他诊断编码 if x is not None))),#326
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.901") for x in 所有其他诊断编码 if x is not None))),#327
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.0") for x in 所有其他诊断编码 if x is not None))),#328
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.903") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.903") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.0") for x in 所有其他诊断编码 if x is not None))),#329
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.0") for x in 所有其他诊断编码 if x is not None))),#330
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.901") for x in 所有其他诊断编码 if x is not None))),#331
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K35.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.101") for x in 所有其他诊断编码 if x is not None))),#332
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K36.x02") if 主要诊断编码 is not None else False) or any(str(x).startswith("K36.x02") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K35.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.101") for x in 所有其他诊断编码 if x is not None))),#333
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K35.103") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.103") for x in 所有其他诊断编码 if x is not None))),#334
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.901") for x in 所有其他诊断编码 if x is not None))),#335
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.900") for x in 所有其他诊断编码 if x is not None))),#336
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K37.x00x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K37.x00x002") for x in 所有其他诊断编码 if x is not None))),#337
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.903") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.903") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K37.x00x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K37.x00x002") for x in 所有其他诊断编码 if x is not None))),#338
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K37.x00") if 主要诊断编码 is not None else False) or any(str(x).startswith("K37.x00") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K35.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.101") for x in 所有其他诊断编码 if x is not None))),#339
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.005") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.005") for x in 所有其他诊断编码 if x is not None))),#340
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.004") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.004") for x in 所有其他诊断编码 if x is not None))),#341
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J30.400") if 主要诊断编码 is not None else False) or any(str(x).startswith("J30.400") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J45.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("J45.900") for x in 所有其他诊断编码 if x is not None))),#342
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("D59.500x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("D59.500x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("D61.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("D61.9") for x in 所有其他诊断编码 if x is not None))),#343
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B49.x00x007") if 主要诊断编码 is not None else False) or any(str(x).startswith("B49.x00x007") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J20.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("J20.900") for x in 所有其他诊断编码 if x is not None))),#344
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B02.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("B02.700") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M79.207") if 主要诊断编码 is not None else False) or any(str(x).startswith("M79.207") for x in 所有其他诊断编码 if x is not None))),#345
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B02.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("B02.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M79.207") if 主要诊断编码 is not None else False) or any(str(x).startswith("M79.207") for x in 所有其他诊断编码 if x is not None))),#346
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B02.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("B02.700") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G62.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("G62.900") for x in 所有其他诊断编码 if x is not None))),#347
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B02.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("B02.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G62.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("G62.900") for x in 所有其他诊断编码 if x is not None))),#348
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B02.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("B02.700") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G58.001") if 主要诊断编码 is not None else False) or any(str(x).startswith("G58.001") for x in 所有其他诊断编码 if x is not None))),#349
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B02.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("B02.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G50.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("G50.000") for x in 所有其他诊断编码 if x is not None))),#350
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B02.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("B02.700") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G50.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("G50.000") for x in 所有其他诊断编码 if x is not None))),#351
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.202") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.202") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.500") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.500") for x in 所有其他诊断编码 if x is not None))),#352
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.4") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.500") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.500") for x in 所有其他诊断编码 if x is not None))),#353
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.2") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.8") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.8") for x in 所有其他诊断编码 if x is not None))),#354
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.4") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.8") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.8") for x in 所有其他诊断编码 if x is not None))),#355
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.202") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.202") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.600") for x in 所有其他诊断编码 if x is not None))),#356
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S82.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.4") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S82.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("S82.600") for x in 所有其他诊断编码 if x is not None))),#357
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N13.201") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.201") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N10.x02") if 主要诊断编码 is not None else False) or any(str(x).startswith("N10.x02") for x in 所有其他诊断编码 if x is not None))),#358
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E05.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("E05.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E05.003") if 主要诊断编码 is not None else False) or any(str(x).startswith("E05.003") for x in 所有其他诊断编码 if x is not None))),#359
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E05.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("E05.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E04.902") if 主要诊断编码 is not None else False) or any(str(x).startswith("E04.902") for x in 所有其他诊断编码 if x is not None))),#360
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N70.900x007") if 主要诊断编码 is not None else False) or any(str(x).startswith("N70.900x007") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N70.902") if 主要诊断编码 is not None else False) or any(str(x).startswith("N70.902") for x in 所有其他诊断编码 if x is not None))),#361
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M34.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("M34.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J84.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("J84.101") for x in 所有其他诊断编码 if x is not None))),#362
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M34.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("M34.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J84.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("J84.101") for x in 所有其他诊断编码 if x is not None))),#363
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M35.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("M35.000") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J84.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("J84.101") for x in 所有其他诊断编码 if x is not None))),#364
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M35.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("M35.000") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K76.900x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K76.900x002") for x in 所有其他诊断编码 if x is not None))),#365
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M35.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("M35.000") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("H16.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("H16.200") for x in 所有其他诊断编码 if x is not None))),#366
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M35.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("M35.000") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N12.x00x007") if 主要诊断编码 is not None else False) or any(str(x).startswith("N12.x00x007") for x in 所有其他诊断编码 if x is not None))),#367
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M35.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("M35.000") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N12.x02") if 主要诊断编码 is not None else False) or any(str(x).startswith("N12.x02") for x in 所有其他诊断编码 if x is not None))),#368
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M35.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("M35.101") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N18.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("N18.900") for x in 所有其他诊断编码 if x is not None))),#369
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M06.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("M06.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I51.400") if 主要诊断编码 is not None else False) or any(str(x).startswith("I51.400") for x in 所有其他诊断编码 if x is not None))),#370
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M06.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("M06.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I31.902") if 主要诊断编码 is not None else False) or any(str(x).startswith("I31.902") for x in 所有其他诊断编码 if x is not None))),#371
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M06.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("M06.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I38.x00x006") if 主要诊断编码 is not None else False) or any(str(x).startswith("I38.x00x006") for x in 所有其他诊断编码 if x is not None))),#372
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K57.303") if 主要诊断编码 is not None else False) or any(str(x).startswith("K57.303") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.9") for x in 所有其他诊断编码 if x is not None))),#373
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I11.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("I11.901") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I50.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("I50.900") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("I12.000x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I12.000x001") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("A53.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("A53.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K74.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.100") for x in 所有其他诊断编码 if x is not None))),#375
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S83.201") if 主要诊断编码 is not None else False) or any(str(x).startswith("S83.201") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S83.400x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("S83.400x003") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S83.202") if 主要诊断编码 is not None else False) or any(str(x).startswith("S83.202") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S83.400x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("S83.400x003") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S83.202") if 主要诊断编码 is not None else False) or any(str(x).startswith("S83.202") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S83.500x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("S83.500x003") for x in 所有其他诊断编码 if x is not None))),#378
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S83.201") if 主要诊断编码 is not None else False) or any(str(x).startswith("S83.201") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S83.500x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("S83.500x003") for x in 所有其他诊断编码 if x is not None))),#379
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.800x081") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.800x081") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S62.210") if 主要诊断编码 is not None else False) or any(str(x).startswith("S62.210") for x in 所有其他诊断编码 if x is not None))),#380
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.900x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.900x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S62.111") if 主要诊断编码 is not None else False) or any(str(x).startswith("S62.111") for x in 所有其他诊断编码 if x is not None))),#381
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S62.810") if 主要诊断编码 is not None else False) or any(str(x).startswith("S62.810") for x in 所有其他诊断编码 if x is not None))),#382
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N13.301") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.301") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N13.501") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.501") for x in 所有其他诊断编码 if x is not None))),#383
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M34.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("M34.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J84.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("J84.101") for x in 所有其他诊断编码 if x is not None))),#384
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.200x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.200x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.006") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.006") for x in 所有其他诊断编码 if x is not None))),#385
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.200x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.200x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.003") for x in 所有其他诊断编码 if x is not None))),#386
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.200x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.200x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.002") for x in 所有其他诊断编码 if x is not None))),#387
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.200x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.200x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.000") for x in 所有其他诊断编码 if x is not None))),#388
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.200x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.200x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.900") for x in 所有其他诊断编码 if x is not None))),#389
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.200x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.200x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.100") for x in 所有其他诊断编码 if x is not None))),#390
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.501") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.501") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.000") for x in 所有其他诊断编码 if x is not None))),#391
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.501") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.501") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.100") for x in 所有其他诊断编码 if x is not None))),#392
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.500x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.500x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.000") for x in 所有其他诊断编码 if x is not None))),#393
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.501") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.501") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.003") for x in 所有其他诊断编码 if x is not None))),#394
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.501") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.501") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.000x007") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.000x007") for x in 所有其他诊断编码 if x is not None))),#395
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.501") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.501") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.000") for x in 所有其他诊断编码 if x is not None))),#396
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.504") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.504") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.000") for x in 所有其他诊断编码 if x is not None))),#397
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.501") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.501") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.001") for x in 所有其他诊断编码 if x is not None))),#398
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.503") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.503") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.000") for x in 所有其他诊断编码 if x is not None))),#399
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.503") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.503") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.100") for x in 所有其他诊断编码 if x is not None))),#400
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.501") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.501") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.000") for x in 所有其他诊断编码 if x is not None))),#401
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.501") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.501") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.100") for x in 所有其他诊断编码 if x is not None))),#402
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.501") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.501") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.900") for x in 所有其他诊断编码 if x is not None))),#403
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.507") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.507") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.100") for x in 所有其他诊断编码 if x is not None))),#404
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K46.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("K46.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K56.6") if 主要诊断编码 is not None else False) or any(str(x).startswith("K56.6") for x in 所有其他诊断编码 if x is not None))),#405
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J47.x00") if 主要诊断编码 is not None else False) or any(str(x).startswith("J47.x00") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("R04.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("R04.200") for x in 所有其他诊断编码 if x is not None))),#406
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B16.905") if 主要诊断编码 is not None else False) or any(str(x).startswith("B16.905") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K72.903") if 主要诊断编码 is not None else False) or any(str(x).startswith("K72.903") for x in 所有其他诊断编码 if x is not None))),#407
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B16.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("B16.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K72.903") if 主要诊断编码 is not None else False) or any(str(x).startswith("K72.903") for x in 所有其他诊断编码 if x is not None))),#408
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E78.500") if 主要诊断编码 is not None else False) or any(str(x).startswith("E78.500") for x in 所有其他诊断编码 if x is not None))),#409
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E78.500") if 主要诊断编码 is not None else False) or any(str(x).startswith("E78.500") for x in 所有其他诊断编码 if x is not None))),#410
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("H33") if 主要诊断编码 is not None else False) or any(str(x).startswith("H33") for x in 所有其他诊断编码 if x is not None))),#411
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("H33") if 主要诊断编码 is not None else False) or any(str(x).startswith("H33") for x in 所有其他诊断编码 if x is not None))),#412
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M79.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("M79.0") for x in 所有其他诊断编码 if x is not None))),#413
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M79.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("M79.0") for x in 所有其他诊断编码 if x is not None))),#414
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M35.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("M35.0") for x in 所有其他诊断编码 if x is not None))),#415
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M35.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("M35.0") for x in 所有其他诊断编码 if x is not None))),#416
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M32") if 主要诊断编码 is not None else False) or any(str(x).startswith("M32") for x in 所有其他诊断编码 if x is not None))),#417
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M32") if 主要诊断编码 is not None else False) or any(str(x).startswith("M32") for x in 所有其他诊断编码 if x is not None))),#418
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N20.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.0") for x in 所有其他诊断编码 if x is not None))),#419
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N20.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.0") for x in 所有其他诊断编码 if x is not None))),#420
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("C9") if 主要诊断编码 is not None else False) or any(str(x).startswith("C9") for x in 所有其他诊断编码 if x is not None))),#421
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("C9") if 主要诊断编码 is not None else False) or any(str(x).startswith("C9") for x in 所有其他诊断编码 if x is not None))),#422
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("R73.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("R73.0") for x in 所有其他诊断编码 if x is not None))),#423
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("R73.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("R73.0") for x in 所有其他诊断编码 if x is not None))),#424
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q21") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q21") for x in 所有其他诊断编码 if x is not None))),#425
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q21") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q21") for x in 所有其他诊断编码 if x is not None))),#426
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q28.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q28.2") for x in 所有其他诊断编码 if x is not None))),#427
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q28.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q28.2") for x in 所有其他诊断编码 if x is not None))),#428
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q28.3") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q28.3") for x in 所有其他诊断编码 if x is not None))),#429
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q28.3") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q28.3") for x in 所有其他诊断编码 if x is not None))),#430
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q20") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q20") for x in 所有其他诊断编码 if x is not None))),#431
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q20") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q20") for x in 所有其他诊断编码 if x is not None))),#432
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q21") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q21") for x in 所有其他诊断编码 if x is not None))),#433
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q21") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q21") for x in 所有其他诊断编码 if x is not None))),#434
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q22") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q22") for x in 所有其他诊断编码 if x is not None))),#435
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q22") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q22") for x in 所有其他诊断编码 if x is not None))),#436
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q23") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q23") for x in 所有其他诊断编码 if x is not None))),#437
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q23") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q23") for x in 所有其他诊断编码 if x is not None))),#438
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q24") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q24") for x in 所有其他诊断编码 if x is not None))),#439
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("Q24") if 主要诊断编码 is not None else False) or any(str(x).startswith("Q24") for x in 所有其他诊断编码 if x is not None))),#440
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N75.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("N75.000") for x in 所有其他诊断编码 if x is not None))),#441
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N75.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("N75.000") for x in 所有其他诊断编码 if x is not None))),#442
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N98.100x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("N98.100x001") for x in 所有其他诊断编码 if x is not None))),#443
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N98.100x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("N98.100x001") for x in 所有其他诊断编码 if x is not None))),#444
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N20.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.1") for x in 所有其他诊断编码 if x is not None))),#445
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N20.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.1") for x in 所有其他诊断编码 if x is not None))),#446
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N25.8") if 主要诊断编码 is not None else False) or any(str(x).startswith("N25.8") for x in 所有其他诊断编码 if x is not None))),#447
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N25.8") if 主要诊断编码 is not None else False) or any(str(x).startswith("N25.8") for x in 所有其他诊断编码 if x is not None))),#448
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N21.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("N21.100") for x in 所有其他诊断编码 if x is not None))),#449
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N21.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("N21.100") for x in 所有其他诊断编码 if x is not None))),#450
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N90.400") if 主要诊断编码 is not None else False) or any(str(x).startswith("N90.400") for x in 所有其他诊断编码 if x is not None))),#451
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N90.400") if 主要诊断编码 is not None else False) or any(str(x).startswith("N90.400") for x in 所有其他诊断编码 if x is not None))),#452
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M45.x00") if 主要诊断编码 is not None else False) or any(str(x).startswith("M45.x00") for x in 所有其他诊断编码 if x is not None))),#453
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M45.x00") if 主要诊断编码 is not None else False) or any(str(x).startswith("M45.x00") for x in 所有其他诊断编码 if x is not None))),#454
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M51") if 主要诊断编码 is not None else False) or any(str(x).startswith("M51") for x in 所有其他诊断编码 if x is not None))),#455
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M51") if 主要诊断编码 is not None else False) or any(str(x).startswith("M51") for x in 所有其他诊断编码 if x is not None))),#456
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("H35.304") if 主要诊断编码 is not None else False) or any(str(x).startswith("H35.304") for x in 所有其他诊断编码 if x is not None))),#457
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("H35.304") if 主要诊断编码 is not None else False) or any(str(x).startswith("H35.304") for x in 所有其他诊断编码 if x is not None))),#458
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("H35") if 主要诊断编码 is not None else False) or any(str(x).startswith("H35") for x in 所有其他诊断编码 if x is not None))),#459
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("H35") if 主要诊断编码 is not None else False) or any(str(x).startswith("H35") for x in 所有其他诊断编码 if x is not None))),#460
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K31.814") if 主要诊断编码 is not None else False) or any(str(x).startswith("K31.814") for x in 所有其他诊断编码 if x is not None))),#461
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K31.814") if 主要诊断编码 is not None else False) or any(str(x).startswith("K31.814") for x in 所有其他诊断编码 if x is not None))),#462
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K40") if 主要诊断编码 is not None else False) or any(str(x).startswith("K40") for x in 所有其他诊断编码 if x is not None))),#463
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K40") if 主要诊断编码 is not None else False) or any(str(x).startswith("K40") for x in 所有其他诊断编码 if x is not None))),#464
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K41") if 主要诊断编码 is not None else False) or any(str(x).startswith("K41") for x in 所有其他诊断编码 if x is not None))),#465
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K41") if 主要诊断编码 is not None else False) or any(str(x).startswith("K41") for x in 所有其他诊断编码 if x is not None))),#466
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K60.3") if 主要诊断编码 is not None else False) or any(str(x).startswith("K60.3") for x in 所有其他诊断编码 if x is not None))),#467
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K60.3") if 主要诊断编码 is not None else False) or any(str(x).startswith("K60.3") for x in 所有其他诊断编码 if x is not None))),#468
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K85") if 主要诊断编码 is not None else False) or any(str(x).startswith("K85") for x in 所有其他诊断编码 if x is not None))),#469
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K85") if 主要诊断编码 is not None else False) or any(str(x).startswith("K85") for x in 所有其他诊断编码 if x is not None))),#470
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.012") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.012") for x in 所有其他诊断编码 if x is not None))),#471
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.012") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.012") for x in 所有其他诊断编码 if x is not None))),#472
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.900") for x in 所有其他诊断编码 if x is not None))),#473
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.900") for x in 所有其他诊断编码 if x is not None))),#474
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K82.802") if 主要诊断编码 is not None else False) or any(str(x).startswith("K82.802") for x in 所有其他诊断编码 if x is not None))),#475
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K82.802") if 主要诊断编码 is not None else False) or any(str(x).startswith("K82.802") for x in 所有其他诊断编码 if x is not None))),#476
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K80.200x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.200x003") for x in 所有其他诊断编码 if x is not None))),#477
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K80.200x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.200x003") for x in 所有其他诊断编码 if x is not None))),#478
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K76.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("K76.600") for x in 所有其他诊断编码 if x is not None))),#479
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K76.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("K76.600") for x in 所有其他诊断编码 if x is not None))),#480
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K35") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35") for x in 所有其他诊断编码 if x is not None))),#481
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K35") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35") for x in 所有其他诊断编码 if x is not None))),#482
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K46") if 主要诊断编码 is not None else False) or any(str(x).startswith("K46") for x in 所有其他诊断编码 if x is not None))),#483
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K46") if 主要诊断编码 is not None else False) or any(str(x).startswith("K46") for x in 所有其他诊断编码 if x is not None))),#484
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K56") if 主要诊断编码 is not None else False) or any(str(x).startswith("K56") for x in 所有其他诊断编码 if x is not None))),#485
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K56") if 主要诊断编码 is not None else False) or any(str(x).startswith("K56") for x in 所有其他诊断编码 if x is not None))),#486
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K29.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("K29.700") for x in 所有其他诊断编码 if x is not None))),#487
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K29.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("K29.700") for x in 所有其他诊断编码 if x is not None))),#488
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K29.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("K29.1") for x in 所有其他诊断编码 if x is not None))),#489
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K29.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("K29.1") for x in 所有其他诊断编码 if x is not None))),#490
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.208") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.208") for x in 所有其他诊断编码 if x is not None))),#491
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.208") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.208") for x in 所有其他诊断编码 if x is not None))),#492
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K52.916") if 主要诊断编码 is not None else False) or any(str(x).startswith("K52.916") for x in 所有其他诊断编码 if x is not None))),#493
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K52.916") if 主要诊断编码 is not None else False) or any(str(x).startswith("K52.916") for x in 所有其他诊断编码 if x is not None))),#494
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K52.915") if 主要诊断编码 is not None else False) or any(str(x).startswith("K52.915") for x in 所有其他诊断编码 if x is not None))),#495
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K52.915") if 主要诊断编码 is not None else False) or any(str(x).startswith("K52.915") for x in 所有其他诊断编码 if x is not None))),#496
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K52.905") if 主要诊断编码 is not None else False) or any(str(x).startswith("K52.905") for x in 所有其他诊断编码 if x is not None))),#497
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K52.905") if 主要诊断编码 is not None else False) or any(str(x).startswith("K52.905") for x in 所有其他诊断编码 if x is not None))),#498
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("A09") if 主要诊断编码 is not None else False) or any(str(x).startswith("A09") for x in 所有其他诊断编码 if x is not None))),#499
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("A09") if 主要诊断编码 is not None else False) or any(str(x).startswith("A09") for x in 所有其他诊断编码 if x is not None))),#500
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K35") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35") for x in 所有其他诊断编码 if x is not None))),#501
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K35") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35") for x in 所有其他诊断编码 if x is not None))),#502
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J80.x00x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("J80.x00x002") for x in 所有其他诊断编码 if x is not None))),#503
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J80.x00x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("J80.x00x002") for x in 所有其他诊断编码 if x is not None))),#504
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J90") if 主要诊断编码 is not None else False) or any(str(x).startswith("J90") for x in 所有其他诊断编码 if x is not None))),#505
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J90") if 主要诊断编码 is not None else False) or any(str(x).startswith("J90") for x in 所有其他诊断编码 if x is not None))),#506
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J06.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("J06.900") for x in 所有其他诊断编码 if x is not None))),#507
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J06.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("J06.900") for x in 所有其他诊断编码 if x is not None))),#508
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J96") if 主要诊断编码 is not None else False) or any(str(x).startswith("J96") for x in 所有其他诊断编码 if x is not None))),#509
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J96") if 主要诊断编码 is not None else False) or any(str(x).startswith("J96") for x in 所有其他诊断编码 if x is not None))),#510
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J45.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("J45.0") for x in 所有其他诊断编码 if x is not None))),#511
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J45.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("J45.0") for x in 所有其他诊断编码 if x is not None))),#512
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J81.x00") if 主要诊断编码 is not None else False) or any(str(x).startswith("J81.x00") for x in 所有其他诊断编码 if x is not None))),#513
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J81.x00") if 主要诊断编码 is not None else False) or any(str(x).startswith("J81.x00") for x in 所有其他诊断编码 if x is not None))),#514
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J98.414") if 主要诊断编码 is not None else False) or any(str(x).startswith("J98.414") for x in 所有其他诊断编码 if x is not None))),#515
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J98.414") if 主要诊断编码 is not None else False) or any(str(x).startswith("J98.414") for x in 所有其他诊断编码 if x is not None))),#516
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J98.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("J98.101") for x in 所有其他诊断编码 if x is not None))),#517
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J98.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("J98.101") for x in 所有其他诊断编码 if x is not None))),#518
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J45.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("J45.9") for x in 所有其他诊断编码 if x is not None))),#519
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J45.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("J45.9") for x in 所有其他诊断编码 if x is not None))),#520
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and(("J40.x00" in str(主要诊断编码) if 主要诊断编码 is not None else False) or any("J40.x00" in str(x) for x in 所有其他诊断编码 if x is not None))),#521
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and(("J40.x00" in str(主要诊断编码) if 主要诊断编码 is not None else False) or any("J40.x00" in str(x) for x in 所有其他诊断编码 if x is not None))),#522
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J47") if 主要诊断编码 is not None else False) or any(str(x).startswith("J47") for x in 所有其他诊断编码 if x is not None))),#523
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J47") if 主要诊断编码 is not None else False) or any(str(x).startswith("J47") for x in 所有其他诊断编码 if x is not None))),#524
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I63") if 主要诊断编码 is not None else False) or any(str(x).startswith("I63") for x in 所有其他诊断编码 if x is not None))),#525
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I63") if 主要诊断编码 is not None else False) or any(str(x).startswith("I63") for x in 所有其他诊断编码 if x is not None))),#526
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I61") if 主要诊断编码 is not None else False) or any(str(x).startswith("I61") for x in 所有其他诊断编码 if x is not None))),#527
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I61") if 主要诊断编码 is not None else False) or any(str(x).startswith("I61") for x in 所有其他诊断编码 if x is not None))),#528
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M06.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("M06.9") for x in 所有其他诊断编码 if x is not None))),#529
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M06.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("M06.9") for x in 所有其他诊断编码 if x is not None))),#530
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I45.6") if 主要诊断编码 is not None else False) or any(str(x).startswith("I45.6") for x in 所有其他诊断编码 if x is not None))),#531
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I45.6") if 主要诊断编码 is not None else False) or any(str(x).startswith("I45.6") for x in 所有其他诊断编码 if x is not None))),#532
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I44.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("I44.700") for x in 所有其他诊断编码 if x is not None))),#533
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I44.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("I44.700") for x in 所有其他诊断编码 if x is not None))),#534
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I45.103") if 主要诊断编码 is not None else False) or any(str(x).startswith("I45.103") for x in 所有其他诊断编码 if x is not None))),#535
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I45.103") if 主要诊断编码 is not None else False) or any(str(x).startswith("I45.103") for x in 所有其他诊断编码 if x is not None))),#536
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I50") if 主要诊断编码 is not None else False) or any(str(x).startswith("I50") for x in 所有其他诊断编码 if x is not None))),#537
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I50") if 主要诊断编码 is not None else False) or any(str(x).startswith("I50") for x in 所有其他诊断编码 if x is not None))),#538
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I24.800x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I24.800x001") for x in 所有其他诊断编码 if x is not None))),#539
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I24.800x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I24.800x001") for x in 所有其他诊断编码 if x is not None))),#540
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I21") if 主要诊断编码 is not None else False) or any(str(x).startswith("I21") for x in 所有其他诊断编码 if x is not None))),#541
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I21") if 主要诊断编码 is not None else False) or any(str(x).startswith("I21") for x in 所有其他诊断编码 if x is not None))),#542
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I22") if 主要诊断编码 is not None else False) or any(str(x).startswith("I22") for x in 所有其他诊断编码 if x is not None))),#543
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I22") if 主要诊断编码 is not None else False) or any(str(x).startswith("I22") for x in 所有其他诊断编码 if x is not None))),#544
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I27") if 主要诊断编码 is not None else False) or any(str(x).startswith("I27") for x in 所有其他诊断编码 if x is not None))),#545
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I27") if 主要诊断编码 is not None else False) or any(str(x).startswith("I27") for x in 所有其他诊断编码 if x is not None))),#546
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and(("F" in str(主要诊断编码) if 主要诊断编码 is not None else False) or any("F" in str(x) for x in 所有其他诊断编码 if x is not None))),#547
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and(("F" in str(主要诊断编码) if 主要诊断编码 is not None else False) or any("F" in str(x) for x in 所有其他诊断编码 if x is not None))),#548
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G80") if 主要诊断编码 is not None else False) or any(str(x).startswith("G80") for x in 所有其他诊断编码 if x is not None))),#549
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G80") if 主要诊断编码 is not None else False) or any(str(x).startswith("G80") for x in 所有其他诊断编码 if x is not None))),#550
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G82") if 主要诊断编码 is not None else False) or any(str(x).startswith("G82") for x in 所有其他诊断编码 if x is not None))),#551
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G82") if 主要诊断编码 is not None else False) or any(str(x).startswith("G82") for x in 所有其他诊断编码 if x is not None))),#552
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G40") if 主要诊断编码 is not None else False) or any(str(x).startswith("G40") for x in 所有其他诊断编码 if x is not None))),#553
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G40") if 主要诊断编码 is not None else False) or any(str(x).startswith("G40") for x in 所有其他诊断编码 if x is not None))),#554
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G41") if 主要诊断编码 is not None else False) or any(str(x).startswith("G41") for x in 所有其他诊断编码 if x is not None))),#555
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G41") if 主要诊断编码 is not None else False) or any(str(x).startswith("G41") for x in 所有其他诊断编码 if x is not None))),#556
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E26") if 主要诊断编码 is not None else False) or any(str(x).startswith("E26") for x in 所有其他诊断编码 if x is not None))),#557
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E26") if 主要诊断编码 is not None else False) or any(str(x).startswith("E26") for x in 所有其他诊断编码 if x is not None))),#558
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E27.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("E27.100") for x in 所有其他诊断编码 if x is not None))),#559
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E27.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("E27.100") for x in 所有其他诊断编码 if x is not None))),#560
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E23.2,") if 主要诊断编码 is not None else False) or any(str(x).startswith("E23.2,") for x in 所有其他诊断编码 if x is not None))),#561
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E23.2,") if 主要诊断编码 is not None else False) or any(str(x).startswith("E23.2,") for x in 所有其他诊断编码 if x is not None))),#562
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E24,") if 主要诊断编码 is not None else False) or any(str(x).startswith("E24,") for x in 所有其他诊断编码 if x is not None))),#563
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E24,") if 主要诊断编码 is not None else False) or any(str(x).startswith("E24,") for x in 所有其他诊断编码 if x is not None))),#564
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E04") if 主要诊断编码 is not None else False) or any(str(x).startswith("E04") for x in 所有其他诊断编码 if x is not None))),#565
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E04") if 主要诊断编码 is not None else False) or any(str(x).startswith("E04") for x in 所有其他诊断编码 if x is not None))),#566
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E06") if 主要诊断编码 is not None else False) or any(str(x).startswith("E06") for x in 所有其他诊断编码 if x is not None))),#567
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E06") if 主要诊断编码 is not None else False) or any(str(x).startswith("E06") for x in 所有其他诊断编码 if x is not None))),#568
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E66.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("E66.9") for x in 所有其他诊断编码 if x is not None))),#569
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E66.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("E66.9") for x in 所有其他诊断编码 if x is not None))),#570
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E23.000x010") if 主要诊断编码 is not None else False) or any(str(x).startswith("E23.000x010") for x in 所有其他诊断编码 if x is not None))),#571
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E23.000x010") if 主要诊断编码 is not None else False) or any(str(x).startswith("E23.000x010") for x in 所有其他诊断编码 if x is not None))),#572
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E14.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.1") for x in 所有其他诊断编码 if x is not None))),#573
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E14.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.1") for x in 所有其他诊断编码 if x is not None))),#574
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E11.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.1") for x in 所有其他诊断编码 if x is not None))),#575
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E11.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.1") for x in 所有其他诊断编码 if x is not None))),#576
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E10.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.1") for x in 所有其他诊断编码 if x is not None))),#577
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E10.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.1") for x in 所有其他诊断编码 if x is not None))),#578
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("D64") if 主要诊断编码 is not None else False) or any(str(x).startswith("D64") for x in 所有其他诊断编码 if x is not None))),#579
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("D64") if 主要诊断编码 is not None else False) or any(str(x).startswith("D64") for x in 所有其他诊断编码 if x is not None))),#580
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("A49") if 主要诊断编码 is not None else False) or any(str(x).startswith("A49") for x in 所有其他诊断编码 if x is not None))),#581
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("A49") if 主要诊断编码 is not None else False) or any(str(x).startswith("A49") for x in 所有其他诊断编码 if x is not None))),#582
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("A41") if 主要诊断编码 is not None else False) or any(str(x).startswith("A41") for x in 所有其他诊断编码 if x is not None))),#583
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("A41") if 主要诊断编码 is not None else False) or any(str(x).startswith("A41") for x in 所有其他诊断编码 if x is not None))),#584
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B16.904") if 主要诊断编码 is not None else False) or any(str(x).startswith("B16.904") for x in 所有其他诊断编码 if x is not None))),#585
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B16.904") if 主要诊断编码 is not None else False) or any(str(x).startswith("B16.904") for x in 所有其他诊断编码 if x is not None))),#586
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B15") if 主要诊断编码 is not None else False) or any(str(x).startswith("B15") for x in 所有其他诊断编码 if x is not None))),#587
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B15") if 主要诊断编码 is not None else False) or any(str(x).startswith("B15") for x in 所有其他诊断编码 if x is not None))),#588
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B18.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("B18.2") for x in 所有其他诊断编码 if x is not None))),#589
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B18.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("B18.2") for x in 所有其他诊断编码 if x is not None))),#590
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N83.100x005") if 主要诊断编码 is not None else False) or any(str(x).startswith("N83.100x005") for x in 所有其他诊断编码 if x is not None))),#591
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N83.100x005") if 主要诊断编码 is not None else False) or any(str(x).startswith("N83.100x005") for x in 所有其他诊断编码 if x is not None))),#592
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N83.502") if 主要诊断编码 is not None else False) or any(str(x).startswith("N83.502") for x in 所有其他诊断编码 if x is not None))),#593
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N83.502") if 主要诊断编码 is not None else False) or any(str(x).startswith("N83.502") for x in 所有其他诊断编码 if x is not None))),#594
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N83.500x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("N83.500x003") for x in 所有其他诊断编码 if x is not None))),#595
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N83.500x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("N83.500x003") for x in 所有其他诊断编码 if x is not None))),#596
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N83.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("N83.2") for x in 所有其他诊断编码 if x is not None))),#597
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N83.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("N83.2") for x in 所有其他诊断编码 if x is not None))),#598
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N80.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("N80.1") for x in 所有其他诊断编码 if x is not None))),#599
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N80.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("N80.1") for x in 所有其他诊断编码 if x is not None))),#600
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N83.800x006") if 主要诊断编码 is not None else False) or any(str(x).startswith("N83.800x006") for x in 所有其他诊断编码 if x is not None))),#601
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N83.800x006") if 主要诊断编码 is not None else False) or any(str(x).startswith("N83.800x006") for x in 所有其他诊断编码 if x is not None))),#602
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N83.809") if 主要诊断编码 is not None else False) or any(str(x).startswith("N83.809") for x in 所有其他诊断编码 if x is not None))),#603
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N83.809") if 主要诊断编码 is not None else False) or any(str(x).startswith("N83.809") for x in 所有其他诊断编码 if x is not None))),#604
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N70.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("N70.1") for x in 所有其他诊断编码 if x is not None))),#605
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N70.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("N70.1") for x in 所有其他诊断编码 if x is not None))),#606
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("D39.706") if 主要诊断编码 is not None else False) or any(str(x).startswith("D39.706") for x in 所有其他诊断编码 if x is not None))),#607
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("D39.706") if 主要诊断编码 is not None else False) or any(str(x).startswith("D39.706") for x in 所有其他诊断编码 if x is not None))),#608
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N80.100x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("N80.100x003") for x in 所有其他诊断编码 if x is not None))),#609
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N80.100x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("N80.100x003") for x in 所有其他诊断编码 if x is not None))),#610
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("D39.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("D39.101") for x in 所有其他诊断编码 if x is not None))),#611
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("D39.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("D39.101") for x in 所有其他诊断编码 if x is not None))),#612
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E87.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("E87.600") for x in 所有其他诊断编码 if x is not None))),#613
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E87.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("E87.600") for x in 所有其他诊断编码 if x is not None))),#614
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E87.102") if 主要诊断编码 is not None else False) or any(str(x).startswith("E87.102") for x in 所有其他诊断编码 if x is not None))),#615
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E87.102") if 主要诊断编码 is not None else False) or any(str(x).startswith("E87.102") for x in 所有其他诊断编码 if x is not None))),#616
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.800x081") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.800x081") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S62.311") if 主要诊断编码 is not None else False) or any(str(x).startswith("S62.311") for x in 所有其他诊断编码 if x is not None))),#617
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.800x081") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.800x081") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S62.301") if 主要诊断编码 is not None else False) or any(str(x).startswith("S62.301") for x in 所有其他诊断编码 if x is not None))),#618
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S51.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("S51.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S52.81") if 主要诊断编码 is not None else False) or any(str(x).startswith("S52.81") for x in 所有其他诊断编码 if x is not None))),#619
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S51.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("S51.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S52.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("S52.4") for x in 所有其他诊断编码 if x is not None))),#620
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S37.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("S37.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N35.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("N35.900") for x in 所有其他诊断编码 if x is not None))),#621
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S05.601") if 主要诊断编码 is not None else False) or any(str(x).startswith("S05.601") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S05.206") if 主要诊断编码 is not None else False) or any(str(x).startswith("S05.206") for x in 所有其他诊断编码 if x is not None))),#622
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S05.601") if 主要诊断编码 is not None else False) or any(str(x).startswith("S05.601") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S05.205") if 主要诊断编码 is not None else False) or any(str(x).startswith("S05.205") for x in 所有其他诊断编码 if x is not None))),#623
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S05.601") if 主要诊断编码 is not None else False) or any(str(x).startswith("S05.601") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S05.200x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("S05.200x001") for x in 所有其他诊断编码 if x is not None))),#624
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S05.601") if 主要诊断编码 is not None else False) or any(str(x).startswith("S05.601") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S05.200x005") if 主要诊断编码 is not None else False) or any(str(x).startswith("S05.200x005") for x in 所有其他诊断编码 if x is not None))),#625
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("O21.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("O21.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E87.8") if 主要诊断编码 is not None else False) or any(str(x).startswith("E87.8") for x in 所有其他诊断编码 if x is not None))),#626
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N13.203") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.203") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N10") if 主要诊断编码 is not None else False) or any(str(x).startswith("N10") for x in 所有其他诊断编码 if x is not None))),#627
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N13.201") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.201") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N10") if 主要诊断编码 is not None else False) or any(str(x).startswith("N10") for x in 所有其他诊断编码 if x is not None))),#628
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N13.202") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.202") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N10") if 主要诊断编码 is not None else False) or any(str(x).startswith("N10") for x in 所有其他诊断编码 if x is not None))),#629
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N20.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.000") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N13.301") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.301") for x in 所有其他诊断编码 if x is not None))),#630
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N20.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.100") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N13.301") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.301") for x in 所有其他诊断编码 if x is not None))),#631
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N20.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.200") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N13.301") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.301") for x in 所有其他诊断编码 if x is not None))),#632
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M50.201") if 主要诊断编码 is not None else False) or any(str(x).startswith("M50.201") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G95.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("G95.900") for x in 所有其他诊断编码 if x is not None))),#633
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M50.201") if 主要诊断编码 is not None else False) or any(str(x).startswith("M50.201") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G96.900x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("G96.900x003") for x in 所有其他诊断编码 if x is not None))),#634
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M51.201") if 主要诊断编码 is not None else False) or any(str(x).startswith("M51.201") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G95.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("G95.900") for x in 所有其他诊断编码 if x is not None))),#635
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M51.203") if 主要诊断编码 is not None else False) or any(str(x).startswith("M51.203") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G95.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("G95.900") for x in 所有其他诊断编码 if x is not None))),#636
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M51.201") if 主要诊断编码 is not None else False) or any(str(x).startswith("M51.201") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G96.900x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("G96.900x003") for x in 所有其他诊断编码 if x is not None))),#637
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M51.202") if 主要诊断编码 is not None else False) or any(str(x).startswith("M51.202") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G95.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("G95.900") for x in 所有其他诊断编码 if x is not None))),#638
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M51.204") if 主要诊断编码 is not None else False) or any(str(x).startswith("M51.204") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G95.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("G95.900") for x in 所有其他诊断编码 if x is not None))),#639
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M51.202") if 主要诊断编码 is not None else False) or any(str(x).startswith("M51.202") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G96.900x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("G96.900x003") for x in 所有其他诊断编码 if x is not None))),#640
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M51.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("M51.2") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M54.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("M54.300") for x in 所有其他诊断编码 if x is not None))),#641
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M54.502") if 主要诊断编码 is not None else False) or any(str(x).startswith("M54.502") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("M54.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("M54.300") for x in 所有其他诊断编码 if x is not None))),#642
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K57.303") if 主要诊断编码 is not None else False) or any(str(x).startswith("K57.303") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.900") for x in 所有其他诊断编码 if x is not None))),#643
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K46.900x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K46.900x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K56.700x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K56.700x003") for x in 所有其他诊断编码 if x is not None))),#644
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K25.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K31.814") if 主要诊断编码 is not None else False) or any(str(x).startswith("K31.814") for x in 所有其他诊断编码 if x is not None))),#645
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K25.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.201") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.201") for x in 所有其他诊断编码 if x is not None))),#646
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K25.300x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.300x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.2") for x in 所有其他诊断编码 if x is not None))),#647
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K25.300x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.300x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K27.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.5") for x in 所有其他诊断编码 if x is not None))),#648
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K25.300x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.300x001") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K27.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.5") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("k92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("k92.2") for x in 所有其他诊断编码 if x is not None))),#649
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.900x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.900x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.2") for x in 所有其他诊断编码 if x is not None))),#650
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.900x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.900x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K27.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.5") for x in 所有其他诊断编码 if x is not None))),#651
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.900x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.900x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K27.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.5") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("k92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("k92.2") for x in 所有其他诊断编码 if x is not None))),#652
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K28.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K28.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K28.500x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("K28.500x011") for x in 所有其他诊断编码 if x is not None))),#653
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K28.600x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("K28.600x011") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K91.800x108") if 主要诊断编码 is not None else False) or any(str(x).startswith("K91.800x108") for x in 所有其他诊断编码 if x is not None))),#654
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.900x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.900x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K31.102") if 主要诊断编码 is not None else False) or any(str(x).startswith("K31.102") for x in 所有其他诊断编码 if x is not None))),#655
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K27.902") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.902") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.2") for x in 所有其他诊断编码 if x is not None))),#656
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K27.902") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.902") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K27.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.5") for x in 所有其他诊断编码 if x is not None))),#657
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K27.700x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.700x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K27.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.5") for x in 所有其他诊断编码 if x is not None)) and (("k92.2" in str(主要诊断编码) if 主要诊断编码 is not None else False) or any("k92.2" in str(x) for x in 所有其他诊断编码 if x is not None))),#658
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K27.700x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.700x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K27.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.5") for x in 所有其他诊断编码 if x is not None))),#659
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K27.700x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.700x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.2") for x in 所有其他诊断编码 if x is not None))),#660
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J44.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("J44.9") for x in 所有其他诊断编码 if x is not None)) and(("J22.x00" in str(主要诊断编码) if 主要诊断编码 is not None else False) or any("J22.x00" in str(x) for x in 所有其他诊断编码 if x is not None))),#661
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J44.802") if 主要诊断编码 is not None else False) or any(str(x).startswith("J44.802") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J43.904") if 主要诊断编码 is not None else False) or any(str(x).startswith("J43.904") for x in 所有其他诊断编码 if x is not None))),#662
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J10.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("J10.101") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I40.001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I40.001") for x in 所有其他诊断编码 if x is not None))),#663
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J10.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("J10.101") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G93.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("G93.1") for x in 所有其他诊断编码 if x is not None))),#664
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J10.100x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("J10.100x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I40.001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I40.001") for x in 所有其他诊断编码 if x is not None))),#665
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J10.100x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("J10.100x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G93.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("G93.1") for x in 所有其他诊断编码 if x is not None))),#666
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J10.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("J10.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("A09.001") if 主要诊断编码 is not None else False) or any(str(x).startswith("A09.001") for x in 所有其他诊断编码 if x is not None))),#667
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("H02.003") if 主要诊断编码 is not None else False) or any(str(x).startswith("H02.003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("H02.004") if 主要诊断编码 is not None else False) or any(str(x).startswith("H02.004") for x in 所有其他诊断编码 if x is not None))),#668
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C79.300x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("C79.300x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("C79.301") if 主要诊断编码 is not None else False) or any(str(x).startswith("C79.301") for x in 所有其他诊断编码 if x is not None))),#669
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C17.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("C17.000") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.203") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.203") for x in 所有其他诊断编码 if x is not None))),#670
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C16") if 主要诊断编码 is not None else False) or any(str(x).startswith("C16") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.201") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.201") for x in 所有其他诊断编码 if x is not None))),#671
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C15") if 主要诊断编码 is not None else False) or any(str(x).startswith("C15") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.208") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.208") for x in 所有其他诊断编码 if x is not None))),#672
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C16.903") if 主要诊断编码 is not None else False) or any(str(x).startswith("C16.903") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.202") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.202") for x in 所有其他诊断编码 if x is not None))),#673
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C18") if 主要诊断编码 is not None else False) or any(str(x).startswith("C18") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.206") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.206") for x in 所有其他诊断编码 if x is not None))),#674
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C22") if 主要诊断编码 is not None else False) or any(str(x).startswith("C22") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K76.803") if 主要诊断编码 is not None else False) or any(str(x).startswith("K76.803") for x in 所有其他诊断编码 if x is not None))),#675
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C21") if 主要诊断编码 is not None else False) or any(str(x).startswith("C21") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.200x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.200x001") for x in 所有其他诊断编码 if x is not None))),#676
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C23.x00") if 主要诊断编码 is not None else False) or any(str(x).startswith("C23.x00") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K82.800x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K82.800x002") for x in 所有其他诊断编码 if x is not None))),#677
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C24") if 主要诊断编码 is not None else False) or any(str(x).startswith("C24") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.809") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.809") for x in 所有其他诊断编码 if x is not None))),#678
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C15.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("C15.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K22.804") if 主要诊断编码 is not None else False) or any(str(x).startswith("K22.804") for x in 所有其他诊断编码 if x is not None))),#679
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B49.x00x007") if 主要诊断编码 is not None else False) or any(str(x).startswith("B49.x00x007") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K20.x00") if 主要诊断编码 is not None else False) or any(str(x).startswith("K20.x00") for x in 所有其他诊断编码 if x is not None))),#680
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B49.x00x007") if 主要诊断编码 is not None else False) or any(str(x).startswith("B49.x00x007") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J32.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("J32.900") for x in 所有其他诊断编码 if x is not None))),#681
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B49.x00x007") if 主要诊断编码 is not None else False) or any(str(x).startswith("B49.x00x007") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J32.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("J32.000") for x in 所有其他诊断编码 if x is not None))),#682
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B49.x00x007") if 主要诊断编码 is not None else False) or any(str(x).startswith("B49.x00x007") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J18.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("J18.9") for x in 所有其他诊断编码 if x is not None))),#683
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B49.x00x007") if 主要诊断编码 is not None else False) or any(str(x).startswith("B49.x00x007") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J98.402") if 主要诊断编码 is not None else False) or any(str(x).startswith("J98.402") for x in 所有其他诊断编码 if x is not None))),#684
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B34.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("B34.101") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J20.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("J20.900") for x in 所有其他诊断编码 if x is not None))),#685
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I86.400x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.400x001") for x in 所有其他诊断编码 if x is not None))),#686
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I86.400x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.400x011") for x in 所有其他诊断编码 if x is not None))),#687
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I86.800x014") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.800x014") for x in 所有其他诊断编码 if x is not None))),#688
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I85.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I85.900x001") for x in 所有其他诊断编码 if x is not None))),#689
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I85.000x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I85.000x001") for x in 所有其他诊断编码 if x is not None))),#690
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B24.x01") if 主要诊断编码 is not None else False) or any(str(x).startswith("B24.x01") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B02.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("B02.900x001") for x in 所有其他诊断编码 if x is not None))),#691
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B24.x01") if 主要诊断编码 is not None else False) or any(str(x).startswith("B24.x01") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("A16.500x004") if 主要诊断编码 is not None else False) or any(str(x).startswith("A16.500x004") for x in 所有其他诊断编码 if x is not None))),#692
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B24.x01") if 主要诊断编码 is not None else False) or any(str(x).startswith("B24.x01") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("A15.000x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("A15.000x001") for x in 所有其他诊断编码 if x is not None))),#693
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("A54.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("A54.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N76.000x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("N76.000x003") for x in 所有其他诊断编码 if x is not None))),#694
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("A54.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("A54.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N34.200x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("N34.200x002") for x in 所有其他诊断编码 if x is not None))),#695
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z35.401") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z35.401") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J45.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("J45.900") for x in 所有其他诊断编码 if x is not None))),#696
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N76.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("N76.000") for x in 所有其他诊断编码 if x is not None))),#697
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J45.005") if 主要诊断编码 is not None else False) or any(str(x).startswith("J45.005") for x in 所有其他诊断编码 if x is not None))),#698
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z34.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z34.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("D56.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("D56.100") for x in 所有其他诊断编码 if x is not None))),#699
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33.x00x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33.x00x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N75.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("N75.000") for x in 所有其他诊断编码 if x is not None))),#700
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33.x00x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33.x00x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N20.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.000") for x in 所有其他诊断编码 if x is not None))),#701
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33.x00x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33.x00x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("L50.801") if 主要诊断编码 is not None else False) or any(str(x).startswith("L50.801") for x in 所有其他诊断编码 if x is not None))),#702
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33.x00x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33.x00x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K82.802") if 主要诊断编码 is not None else False) or any(str(x).startswith("K82.802") for x in 所有其他诊断编码 if x is not None))),#703
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33.x00x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33.x00x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K64.809") if 主要诊断编码 is not None else False) or any(str(x).startswith("K64.809") for x in 所有其他诊断编码 if x is not None))),#704
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33.x00x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33.x00x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I10.x03") if 主要诊断编码 is not None else False) or any(str(x).startswith("I10.x03") for x in 所有其他诊断编码 if x is not None))),#705
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33.x00x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33.x00x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E87.102") if 主要诊断编码 is not None else False) or any(str(x).startswith("E87.102") for x in 所有其他诊断编码 if x is not None))),#706
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33.x00x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33.x00x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E14.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.900x001") for x in 所有其他诊断编码 if x is not None))),#707
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33.x00x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33.x00x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("D69.400x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("D69.400x002") for x in 所有其他诊断编码 if x is not None))),#708
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33.x00x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33.x00x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("D56.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("D56.000") for x in 所有其他诊断编码 if x is not None))),#709
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33.x00x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33.x00x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("D39.100x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("D39.100x003") for x in 所有其他诊断编码 if x is not None))),#710
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33.x00x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33.x00x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B18.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("B18.000") for x in 所有其他诊断编码 if x is not None))),#711
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z33.x00x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z33.x00x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B01.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("B01.900x001") for x in 所有其他诊断编码 if x is not None))),#712
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z32.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z32.100") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J40.x00") if 主要诊断编码 is not None else False) or any(str(x).startswith("J40.x00") for x in 所有其他诊断编码 if x is not None))),#713
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("Z32.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("Z32.100") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E78.500") if 主要诊断编码 is not None else False) or any(str(x).startswith("E78.500") for x in 所有其他诊断编码 if x is not None))),#714
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T25.300x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("T25.300x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("T25.300x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("T25.300x003") for x in 所有其他诊断编码 if x is not None))),#715
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T25.200x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("T25.200x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("T25.200x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("T25.200x003") for x in 所有其他诊断编码 if x is not None))),#716
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T25.100x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("T25.100x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("T25.100x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("T25.100x003") for x in 所有其他诊断编码 if x is not None))),#717
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T23.100x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("T23.100x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("T23.100x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("T23.100x003") for x in 所有其他诊断编码 if x is not None))),#718
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T23.200x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("T23.200x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("T23.200x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("T23.200x003") for x in 所有其他诊断编码 if x is not None))),#719
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T23.300x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("T23.300x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("T23.300x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("T23.300x003") for x in 所有其他诊断编码 if x is not None))),#720
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T22.100x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("T22.100x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("T22.100x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("T22.100x003") for x in 所有其他诊断编码 if x is not None))),#721
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T22.200x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("T22.200x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("T22.200x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("T22.200x003") for x in 所有其他诊断编码 if x is not None))),#722
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T22.300x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("T22.300x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("T22.300x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("T22.300x003") for x in 所有其他诊断编码 if x is not None))),#723
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T24.100x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("T24.100x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("T24.100x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("T24.100x003") for x in 所有其他诊断编码 if x is not None))),#724
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T24.200x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("T24.200x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("T24.200x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("T24.200x003") for x in 所有其他诊断编码 if x is not None))),#725
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T24.300x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("T24.300x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("T24.300x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("T24.300x003") for x in 所有其他诊断编码 if x is not None))),#726
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T26.100x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("T26.100x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("T26.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("T26.101") for x in 所有其他诊断编码 if x is not None))),#727
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T26.400x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("T26.400x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S05.300x004") if 主要诊断编码 is not None else False) or any(str(x).startswith("S05.300x004") for x in 所有其他诊断编码 if x is not None))),#728
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T26.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("T26.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S05.300x004") if 主要诊断编码 is not None else False) or any(str(x).startswith("S05.300x004") for x in 所有其他诊断编码 if x is not None))),#729
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("T27.000x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("T27.000x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("T27.000x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("T27.000x003") for x in 所有其他诊断编码 if x is not None))),#730
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S71.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("S71.100") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S72.910") if 主要诊断编码 is not None else False) or any(str(x).startswith("S72.910") for x in 所有其他诊断编码 if x is not None))),#731
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S71.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("S71.100") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S72.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("S72.900") for x in 所有其他诊断编码 if x is not None))),#732
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S63.100x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("S63.100x011") for x in 所有其他诊断编码 if x is not None))),#733
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S63.100x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("S63.100x001") for x in 所有其他诊断编码 if x is not None))),#734
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S62.311") if 主要诊断编码 is not None else False) or any(str(x).startswith("S62.311") for x in 所有其他诊断编码 if x is not None))),#735
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S62.301") if 主要诊断编码 is not None else False) or any(str(x).startswith("S62.301") for x in 所有其他诊断编码 if x is not None))),#736
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S62.210") if 主要诊断编码 is not None else False) or any(str(x).startswith("S62.210") for x in 所有其他诊断编码 if x is not None))),#737
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("S61.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("S61.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("S62.200x041") if 主要诊断编码 is not None else False) or any(str(x).startswith("S62.200x041") for x in 所有其他诊断编码 if x is not None))),#738
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N70.900x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("N70.900x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N70.902") if 主要诊断编码 is not None else False) or any(str(x).startswith("N70.902") for x in 所有其他诊断编码 if x is not None))),#739
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N20.200x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.200x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N13.301") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.301") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("N11.000x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("N11.000x001") for x in 所有其他诊断编码 if x is not None))),#740
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N13.504") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.504") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N28.834") if 主要诊断编码 is not None else False) or any(str(x).startswith("N28.834") for x in 所有其他诊断编码 if x is not None))),#741
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N13.301") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.301") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N20.000x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.000x001") for x in 所有其他诊断编码 if x is not None))),#742
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N13.301") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.301") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N20.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.100") for x in 所有其他诊断编码 if x is not None))),#743
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N13.301") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.301") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N20.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.200") for x in 所有其他诊断编码 if x is not None))),#744
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("N13.301") if 主要诊断编码 is not None else False) or any(str(x).startswith("N13.301") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("N20.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("N20.901") for x in 所有其他诊断编码 if x is not None))),#745
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M05.900x093") if 主要诊断编码 is not None else False) or any(str(x).startswith("M05.900x093") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J84.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("J84.101") for x in 所有其他诊断编码 if x is not None))),#746
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.504") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.504") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.005") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.005") for x in 所有其他诊断编码 if x is not None))),#747
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.501") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.501") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.003") for x in 所有其他诊断编码 if x is not None))),#748
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.5") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K80.3") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.3") for x in 所有其他诊断编码 if x is not None))),#749
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.5") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K80.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.4") for x in 所有其他诊断编码 if x is not None))),#750
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.1") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K80.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.2") for x in 所有其他诊断编码 if x is not None))),#751
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K80.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.2") for x in 所有其他诊断编码 if x is not None))),#752
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.504") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.504") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.100") for x in 所有其他诊断编码 if x is not None))),#753
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.503") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.503") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K81.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("K81.100") for x in 所有其他诊断编码 if x is not None))),#754
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.503") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.503") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.0") for x in 所有其他诊断编码 if x is not None))),#755
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K80.501") if 主要诊断编码 is not None else False) or any(str(x).startswith("K80.501") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K83.000x007") if 主要诊断编码 is not None else False) or any(str(x).startswith("K83.000x007") for x in 所有其他诊断编码 if x is not None))),#756
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M06.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("M06.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I31.902") if 主要诊断编码 is not None else False) or any(str(x).startswith("I31.902") for x in 所有其他诊断编码 if x is not None))),#757
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("M05.900x093") if 主要诊断编码 is not None else False) or any(str(x).startswith("M05.900x093") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J84.101") if 主要诊断编码 is not None else False) or any(str(x).startswith("J84.101") for x in 所有其他诊断编码 if x is not None))),#758
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K71.601") if 主要诊断编码 is not None else False) or any(str(x).startswith("K71.601") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K72.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("K72.100") for x in 所有其他诊断编码 if x is not None))),#759
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K71.601") if 主要诊断编码 is not None else False) or any(str(x).startswith("K71.601") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K72.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K72.900") for x in 所有其他诊断编码 if x is not None))),#760
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K71.601") if 主要诊断编码 is not None else False) or any(str(x).startswith("K71.601") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K72.003") if 主要诊断编码 is not None else False) or any(str(x).startswith("K72.003") for x in 所有其他诊断编码 if x is not None))),#761
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K71.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("K71.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K72.000x014") if 主要诊断编码 is not None else False) or any(str(x).startswith("K72.000x014") for x in 所有其他诊断编码 if x is not None))),#762
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K71.901") if 主要诊断编码 is not None else False) or any(str(x).startswith("K71.901") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K72.001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K72.001") for x in 所有其他诊断编码 if x is not None))),#763
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K70.402") if 主要诊断编码 is not None else False) or any(str(x).startswith("K70.402") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K72.903") if 主要诊断编码 is not None else False) or any(str(x).startswith("K72.903") for x in 所有其他诊断编码 if x is not None))),#764
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K70.401") if 主要诊断编码 is not None else False) or any(str(x).startswith("K70.401") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K72.903") if 主要诊断编码 is not None else False) or any(str(x).startswith("K72.903") for x in 所有其他诊断编码 if x is not None))),#765
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K70.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K70.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I86.800x014") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.800x014") for x in 所有其他诊断编码 if x is not None))),#766
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I86.400x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("I86.400x011") for x in 所有其他诊断编码 if x is not None))),#767
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K74.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K74.300") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I85.000x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("I85.000x001") for x in 所有其他诊断编码 if x is not None))),#768
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K29.701") if 主要诊断编码 is not None else False) or any(str(x).startswith("K29.701") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.202") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.202") for x in 所有其他诊断编码 if x is not None))),#769
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.2") for x in 所有其他诊断编码 if x is not None))),#770
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.900x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.900x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.2") for x in 所有其他诊断编码 if x is not None))),#771
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K66.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K66.000") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K56.701") if 主要诊断编码 is not None else False) or any(str(x).startswith("K56.701") for x in 所有其他诊断编码 if x is not None))),#772
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K58.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K58.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K52.916") if 主要诊断编码 is not None else False) or any(str(x).startswith("K52.916") for x in 所有其他诊断编码 if x is not None))),#773
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K57.100x005") if 主要诊断编码 is not None else False) or any(str(x).startswith("K57.100x005") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.000") for x in 所有其他诊断编码 if x is not None))),#774
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K35.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K35.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K65.001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K65.001") for x in 所有其他诊断编码 if x is not None))),#775
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K29.701") if 主要诊断编码 is not None else False) or any(str(x).startswith("K29.701") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.202") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.202") for x in 所有其他诊断编码 if x is not None))),#776
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.203") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.203") for x in 所有其他诊断编码 if x is not None))),#777
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.203") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.203") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K31.800x806") if 主要诊断编码 is not None else False) or any(str(x).startswith("K31.800x806") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K25.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.700") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.207") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.207") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K27.503") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.503") for x in 所有其他诊断编码 if x is not None))),
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K25.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.900x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K27.503") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.503") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("K92.201") if 主要诊断编码 is not None else False) or any(str(x).startswith("K92.201") for x in 所有其他诊断编码 if x is not None))),#780
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J30.400") if 主要诊断编码 is not None else False) or any(str(x).startswith("J30.400") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("J45.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("J45.9") for x in 所有其他诊断编码 if x is not None))), #781
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("H33.500") if 主要诊断编码 is not None else False) or any(str(x).startswith("H33.500") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("H33.304") if 主要诊断编码 is not None else False) or any(str(x).startswith("H33.304") for x in 所有其他诊断编码 if x is not None))), #782
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("H33.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("H33.200") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("H33.304") if 主要诊断编码 is not None else False) or any(str(x).startswith("H33.304") for x in 所有其他诊断编码 if x is not None))), #783
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("D59.500x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("D59.500x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("D61.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("D61.900") for x in 所有其他诊断编码 if x is not None))), #784
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("D34.x00") if 主要诊断编码 is not None else False) or any(str(x).startswith("D34.x00") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E05.900x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("E05.900x001") for x in 所有其他诊断编码 if x is not None))), #785
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C18.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("C18.200") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("C18.400") if 主要诊断编码 is not None else False) or any(str(x).startswith("C18.400") for x in 所有其他诊断编码 if x is not None))), #786
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C18.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("C18.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("C20.x00") if 主要诊断编码 is not None else False) or any(str(x).startswith("C20.x00") for x in 所有其他诊断编码 if x is not None))), #787
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C18.400") if 主要诊断编码 is not None else False) or any(str(x).startswith("C18.400") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("C18.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("C18.600") for x in 所有其他诊断编码 if x is not None))), #788
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C18.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("C18.200") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("C18.400") if 主要诊断编码 is not None else False) or any(str(x).startswith("C18.400") for x in 所有其他诊断编码 if x is not None))), #789
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("C18.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("C18.700") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("C18.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("C18.600") for x in 所有其他诊断编码 if x is not None))), #790
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B15.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("B15.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B15.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("B15.0") for x in 所有其他诊断编码 if x is not None))), #791
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B16.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("B16.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B16.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("B16.1") for x in 所有其他诊断编码 if x is not None))), #792
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B16.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("B16.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B16.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("B16.9") for x in 所有其他诊断编码 if x is not None))), #793
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B16.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("B16.2") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B16.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("B16.9") for x in 所有其他诊断编码 if x is not None))), #794
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B16.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("B16.2") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B16.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("B16.1") for x in 所有其他诊断编码 if x is not None))), #795
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B18.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("B18.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B18.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("B18.1") for x in 所有其他诊断编码 if x is not None))), #796
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B19.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("B19.0") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B19.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("B19.9") for x in 所有其他诊断编码 if x is not None))), #797
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B26.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("B26.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B26.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("B26.0") for x in 所有其他诊断编码 if x is not None))), #798
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B26.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("B26.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B26.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("B26.1") for x in 所有其他诊断编码 if x is not None))), #799
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B26.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("B26.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B26.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("B26.2") for x in 所有其他诊断编码 if x is not None))), #800
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B26.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("B26.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B26.3") if 主要诊断编码 is not None else False) or any(str(x).startswith("B26.3") for x in 所有其他诊断编码 if x is not None))), #801
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("B26.9") if 主要诊断编码 is not None else False) or any(str(x).startswith("B26.9") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("B26.8") if 主要诊断编码 is not None else False) or any(str(x).startswith("B26.8") for x in 所有其他诊断编码 if x is not None))), #802
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I83.903") if 主要诊断编码 is not None else False) or any(str(x).startswith("I83.903") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("L97.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("L97.000") for x in 所有其他诊断编码 if x is not None))), #803
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E10.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E10.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.0") for x in 所有其他诊断编码 if x is not None))), #804
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E10.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E10.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.1") for x in 所有其他诊断编码 if x is not None))), #805
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E10.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E10.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.2") for x in 所有其他诊断编码 if x is not None))), #806
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E10.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E10.3") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.3") for x in 所有其他诊断编码 if x is not None))), #807
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E10.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E10.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.4") for x in 所有其他诊断编码 if x is not None))), #808
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E10.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E10.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.5") for x in 所有其他诊断编码 if x is not None))), #809
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E10.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E10.6") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.6") for x in 所有其他诊断编码 if x is not None))), #810
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E10.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E10.7") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.7") for x in 所有其他诊断编码 if x is not None))), #811
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E10.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E10.8") if 主要诊断编码 is not None else False) or any(str(x).startswith("E10.8") for x in 所有其他诊断编码 if x is not None))), #812
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E11.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E11.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.0") for x in 所有其他诊断编码 if x is not None))), #813
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E11.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E11.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.1") for x in 所有其他诊断编码 if x is not None))), #814
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E11.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E11.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.2") for x in 所有其他诊断编码 if x is not None))), #815
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E11.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E11.3") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.3") for x in 所有其他诊断编码 if x is not None))), #816
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E11.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E11.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.4") for x in 所有其他诊断编码 if x is not None))), #817
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E11.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E11.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.5") for x in 所有其他诊断编码 if x is not None))), #818
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E11.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E11.6") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.6") for x in 所有其他诊断编码 if x is not None))), #819
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E11.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E11.7") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.7") for x in 所有其他诊断编码 if x is not None))), #820
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E11.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E11.8") if 主要诊断编码 is not None else False) or any(str(x).startswith("E11.8") for x in 所有其他诊断编码 if x is not None))), #821
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E14.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E14.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.0") for x in 所有其他诊断编码 if x is not None))), #822
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E14.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E14.1") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.1") for x in 所有其他诊断编码 if x is not None))), #823
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E14.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E14.2") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.2") for x in 所有其他诊断编码 if x is not None))), #824
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E14.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E14.3") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.3") for x in 所有其他诊断编码 if x is not None))), #825
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E14.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E14.4") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.4") for x in 所有其他诊断编码 if x is not None))), #826
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E14.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E14.5") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.5") for x in 所有其他诊断编码 if x is not None))), #827
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E14.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E14.6") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.6") for x in 所有其他诊断编码 if x is not None))), #828
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E14.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E14.7") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.7") for x in 所有其他诊断编码 if x is not None))), #829
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E14.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.900") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E14.8") if 主要诊断编码 is not None else False) or any(str(x).startswith("E14.8") for x in 所有其他诊断编码 if x is not None))), #830
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("E15") if 主要诊断编码 is not None else False) or any(str(x).startswith("E15") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("E16.0") if 主要诊断编码 is not None else False) or any(str(x).startswith("E16.0") for x in 所有其他诊断编码 if x is not None))), #831
    lambda 性别, 门急诊诊断编码, 主要诊断编码, 所有其他诊断编码: not iter_iter(grange("E89.5"), [主要诊断编码] + (所有其他诊断编码 if isinstance(所有其他诊断编码, list) else [所有其他诊断编码]) + [门急诊诊断编码]) or str(性别) != "2",
    lambda 性别, 门急诊诊断编码, 主要诊断编码, 所有其他诊断编码: not iter_iter(grange("E89.4"), [主要诊断编码] + (所有其他诊断编码 if isinstance(所有其他诊断编码, list) else [所有其他诊断编码]) + [门急诊诊断编码]) or str(性别) != "1",
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F23.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("F23.000") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F23.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("F23.100") for x in 所有其他诊断编码 if x is not None))),#834
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F23.000x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("F23.000x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F23.000x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("F23.000x011") for x in 所有其他诊断编码 if x is not None))),#835
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F23.100x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("F23.100x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F23.100x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("F23.100x011") for x in 所有其他诊断编码 if x is not None))),#836
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F23.200x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("F23.200x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F23.200x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("F23.200x011") for x in 所有其他诊断编码 if x is not None))),#837
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F23.300x004") if 主要诊断编码 is not None else False) or any(str(x).startswith("F23.300x004") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F23.300x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("F23.300x011") for x in 所有其他诊断编码 if x is not None))),#838
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F30.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("F30.100") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F30.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("F30.200") for x in 所有其他诊断编码 if x is not None))),#839
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F30.100x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("F30.100x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F30.200x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("F30.200x001") for x in 所有其他诊断编码 if x is not None))),#840
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F31.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("F31.100") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F31.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("F31.200") for x in 所有其他诊断编码 if x is not None))),#841
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F31.100x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("F31.100x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F31.200x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("F31.200x001") for x in 所有其他诊断编码 if x is not None))),#842
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F31.300x003") if 主要诊断编码 is not None else False) or any(str(x).startswith("F31.300x003") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F31.300x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("F31.300x011") for x in 所有其他诊断编码 if x is not None))),#843
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F31.300x005") if 主要诊断编码 is not None else False) or any(str(x).startswith("F31.300x005") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F31.300x012") if 主要诊断编码 is not None else False) or any(str(x).startswith("F31.300x012") for x in 所有其他诊断编码 if x is not None))),#844
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F31.400") if 主要诊断编码 is not None else False) or any(str(x).startswith("F31.400") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F31.500") if 主要诊断编码 is not None else False) or any(str(x).startswith("F31.500") for x in 所有其他诊断编码 if x is not None))),#845
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F31.400x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("F31.400x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F31.500x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("F31.500x001") for x in 所有其他诊断编码 if x is not None))),#846
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F32.000x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("F32.000x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F32.000x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("F32.000x011") for x in 所有其他诊断编码 if x is not None))),#847
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F32.100x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("F32.100x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F32.100x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("F32.100x011") for x in 所有其他诊断编码 if x is not None))),#848
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F32.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("F32.200") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F32.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("F32.300") for x in 所有其他诊断编码 if x is not None))),#849
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F33.000x002") if 主要诊断编码 is not None else False) or any(str(x).startswith("F33.000x002") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F33.000x011") if 主要诊断编码 is not None else False) or any(str(x).startswith("F33.000x011") for x in 所有其他诊断编码 if x is not None))),#850
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F33.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("F33.200") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F33.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("F33.300") for x in 所有其他诊断编码 if x is not None))),#851
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("F33.200x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("F33.200x001") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("F33.300x001") if 主要诊断编码 is not None else False) or any(str(x).startswith("F33.300x001") for x in 所有其他诊断编码 if x is not None))),#852
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("G40.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("G40.600") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G40.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("G40.700") for x in 所有其他诊断编码 if x is not None))),#853
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("G43.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("G43.000") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G43.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("G43.100") for x in 所有其他诊断编码 if x is not None))),#854
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("G44.200x005") if 主要诊断编码 is not None else False) or any(str(x).startswith("G44.200x005") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("G44.208") if 主要诊断编码 is not None else False) or any(str(x).startswith("G44.208") for x in 所有其他诊断编码 if x is not None))),#855
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I34") if 主要诊断编码 is not None else False) or any(str(x).startswith("I34") for x in 所有其他诊断编码 if x is not None))),#856
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I35") if 主要诊断编码 is not None else False) or any(str(x).startswith("I35") for x in 所有其他诊断编码 if x is not None))),#857
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I05") if 主要诊断编码 is not None else False) or any(str(x).startswith("I05") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I36") if 主要诊断编码 is not None else False) or any(str(x).startswith("I36") for x in 所有其他诊断编码 if x is not None))),#858
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I06") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I34") if 主要诊断编码 is not None else False) or any(str(x).startswith("I34") for x in 所有其他诊断编码 if x is not None))),#859
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I06") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I35") if 主要诊断编码 is not None else False) or any(str(x).startswith("I35") for x in 所有其他诊断编码 if x is not None))),#860
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I06") if 主要诊断编码 is not None else False) or any(str(x).startswith("I06") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I36") if 主要诊断编码 is not None else False) or any(str(x).startswith("I36") for x in 所有其他诊断编码 if x is not None))),#861
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I07") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I34") if 主要诊断编码 is not None else False) or any(str(x).startswith("I34") for x in 所有其他诊断编码 if x is not None))),#862
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I07") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I35") if 主要诊断编码 is not None else False) or any(str(x).startswith("I35") for x in 所有其他诊断编码 if x is not None))),#863
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I07") if 主要诊断编码 is not None else False) or any(str(x).startswith("I07") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I36") if 主要诊断编码 is not None else False) or any(str(x).startswith("I36") for x in 所有其他诊断编码 if x is not None))),#864
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I08") if 主要诊断编码 is not None else False) or any(str(x).startswith("I08") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I34") if 主要诊断编码 is not None else False) or any(str(x).startswith("I34") for x in 所有其他诊断编码 if x is not None))),#865
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I08") if 主要诊断编码 is not None else False) or any(str(x).startswith("I08") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I35") if 主要诊断编码 is not None else False) or any(str(x).startswith("I35") for x in 所有其他诊断编码 if x is not None))),#866
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I08") if 主要诊断编码 is not None else False) or any(str(x).startswith("I08") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I36") if 主要诊断编码 is not None else False) or any(str(x).startswith("I36") for x in 所有其他诊断编码 if x is not None))),#867
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I09") if 主要诊断编码 is not None else False) or any(str(x).startswith("I09") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I34") if 主要诊断编码 is not None else False) or any(str(x).startswith("I34") for x in 所有其他诊断编码 if x is not None))),#868
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I09") if 主要诊断编码 is not None else False) or any(str(x).startswith("I09") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I35") if 主要诊断编码 is not None else False) or any(str(x).startswith("I35") for x in 所有其他诊断编码 if x is not None))),#869
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("I09") if 主要诊断编码 is not None else False) or any(str(x).startswith("I09") for x in 所有其他诊断编码 if x is not None)) and((str(主要诊断编码).startswith("I36") if 主要诊断编码 is not None else False) or any(str(x).startswith("I36") for x in 所有其他诊断编码 if x is not None))),#870
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("J35.100") if 主要诊断编码 is not None else False) or any(str(x).startswith("J35.100") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("J35.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("J35.200") for x in 所有其他诊断编码 if x is not None))),#871
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K04.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("K04.600") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K04.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("K04.700") for x in 所有其他诊断编码 if x is not None))),#872
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K21.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K21.000") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K21.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K21.900") for x in 所有其他诊断编码 if x is not None))),#873
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K25.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.200") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K25.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.300") for x in 所有其他诊断编码 if x is not None))),#874
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K25.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.600") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K25.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.700") for x in 所有其他诊断编码 if x is not None))),#875
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K25.400") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.400") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K25.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.900") for x in 所有其他诊断编码 if x is not None))),#876
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K25.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.000") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K25.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K25.900") for x in 所有其他诊断编码 if x is not None))),#877
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.200") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K26.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.300") for x in 所有其他诊断编码 if x is not None))),#878
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.600") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K26.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.700") for x in 所有其他诊断编码 if x is not None))),#879
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.000") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K26.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.900") for x in 所有其他诊断编码 if x is not None))),#880
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K26.400") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.400") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K26.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K26.900") for x in 所有其他诊断编码 if x is not None))),#881
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K27.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.200") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K27.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.300") for x in 所有其他诊断编码 if x is not None))),#882
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K27.600") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.600") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K27.700") if 主要诊断编码 is not None else False) or any(str(x).startswith("K27.700") for x in 所有其他诊断编码 if x is not None))),#883
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K28.200") if 主要诊断编码 is not None else False) or any(str(x).startswith("K28.200") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K28.300") if 主要诊断编码 is not None else False) or any(str(x).startswith("K28.300") for x in 所有其他诊断编码 if x is not None))),#884
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K28.000") if 主要诊断编码 is not None else False) or any(str(x).startswith("K28.000") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K28.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K28.900") for x in 所有其他诊断编码 if x is not None))),#885
    lambda 主要诊断编码, 所有其他诊断编码: not (((str(主要诊断编码).startswith("K28.400") if 主要诊断编码 is not None else False) or any(str(x).startswith("K28.400") for x in 所有其他诊断编码 if x is not None)) and ((str(主要诊断编码).startswith("K28.900") if 主要诊断编码 is not None else False) or any(str(x).startswith("K28.900") for x in 所有其他诊断编码 if x is not None))),#886
    lambda 主要诊断编码, 离院方式: not (include(grange("C00-C97") + grange("D00-D48"), 主要诊断编码) and str(离院方式) == "5"),

    ]




b = [
    '【组织机构代码】必填，不可填"-"',
    '【总费用, 各项费用】应>0,且≥各项费用之和',
    '【手术治疗费】应是麻醉费与手术费之和',
    '【住址邮编】应为6位数字',
    '【住院医师】必填,不可填"-"',
    '【主治医师】必填,不可填"-"',
    '【质控医师】必填,不可填"-"',
    '【病案质量】必填,不可填"-"',
    '【质控护师】必填,不可填"-"',
    '【质控日期】必填,不可填"-"',
    '【ABO血型】必填,不可填"-"接口标准',
    '【血费大于0】输血反应必填,必须是"无或有,血费是0,输血反应必须是未输"',
    '【血费大于0】红细胞,白细胞,血小板,血浆,全血,其他至少填一项',
    '【血费】>0,ABO血型必须填',
    '【血型】与【RH】填写不符(血型填写则RH必填;血型"A\B\O\AB",RH只能"阴\阳";血型"不祥",RH只能"不祥";血型"未查",RH只能"未查")',
    '【主要诊断其他诊断编码】为S00-S99、T00-T98的【损伤、中毒的外部原因】必填不可为空或-',  #15
    '【主要诊断编码】为"D37-D48",【病理诊断编码】必须是包含"/1"(动态未肯定或未知的肿瘤)',
    '【主要诊断编码】为"D10-D36",【病理诊断编码】必须是包含"/0"(良性肿瘤)',
    '【主要诊断编码】为"D00-D09",【病理诊断编码】必须是包含"/2"(原位肿瘤)',
    '【主要诊断编码】为"C80-C97",【病理诊断编码】必须是包含"/3"(恶性肿瘤，被陈述或定为原发性)',
    '【主要诊断编码】为"C77-C79",【病理诊断编码】必须是包含"/6"(恶性肿瘤，被陈述或定为继发性)',
    '【主要诊断编码】为"C00-C76",【病理诊断编码】必须是包含"/3"(恶性肿瘤，被陈述或定为原发性)',
    '【主要诊断编码】包含"C00-C97/D00-D48"，【病理诊断编码】必填',
    '【主要诊断编码】及所有【其他诊断编码】不允许重复',
    '【主要诊断】或【其他诊断】编码出现"O80-O84",【其他诊断编码】必须有分娩结局编码Z37', #24
    '【主要诊断】不允许为T96-T97中毒和有害效应的后遗症',
    '【主要诊断】不允许为"Z98"(其他手术后状态)',
    '【主要诊断】不允许为"Z96-Z97"(具有其他功能性植入物和其他装置)',
    '【主要诊断】不允许为"Z95"(具有心脏和血管植入物和移植物)',
    '【主要诊断】不允许为"Z94"(器官和组织移植状态)',
    '【主要诊断】不允许为"Z93"(人工造口状态)',
    '【主要诊断】不允许为"Z92"(医疗个人史)',
    '【主要诊断】不允许为"Z91"(危险因素个人史，不可归类在他处者)',
    '【主要诊断】不允许为"Z88"(对药物、药剂和生物制品过敏个人史)',
    '【主要诊断】不允许为"Z86-Z87"(其他疾病的个人史)',
    '【主要诊断】不允许为"Z85"(恶性肿瘤个人史)',
    '【主要诊断】不允许为"Z37-Z38"(分娩的结局)',
    '【主要诊断】不允许为"Z33"(妊娠状态)',
    '【主要诊断】不允许为"U80-U85"(对抗生素产生耐药性的菌株)',
    '【主要诊断】不允许为"T31"(根据体表累及范围分类的烧伤)的诊断',
    '【主要诊断】不允许为"I69"(脑血管病后遗症)',
    '【主要诊断】不允许为"I50、I51"主要诊断编码',
    '【主要诊断】不允许为"B95-B97"(细菌、病毒和其他传染性病原体的诊断)',
    '【主要诊断】【其他诊断】不允许为"O06"(未特指的流产)',
    '【主任（副主任）医师】必填，不可填"-"',
    '【质控日期】应>【出院时间】,且在出院后24小时内',
    '【职业】必填，可填"-"参照接口标准',
    '【责任护士】必填，不可填"-"',
    '【医疗机构】必填，不可填"-"',
    '【医疗付费方式】为"城镇职工基本医疗保险"时,【工作单位】【工作单位电话】【工作邮编】必填',
    '【医疗付费方式】必填，不可填"-"',
    '【药物过敏】为"有"时，【过敏药物】必填',
    '【姓名】必填，不可填"-"',
    '【性别】为女，【手术和操作编码】中不应出现以"60-64"开头的编码',
    '【性别】为男，【手术和操作编码】中不应出现以"65-75"开头的编码',
    '【性别】为"女",【门、出院诊断】中不应出现男性化疾病诊断编码("C60-C63,N40-N51,E29")',
    '【性别】为"男",【门、出院诊断】中不应出现女性化疾病诊断编码("C51-C58,N70-N77,N80-N98,O00-O99,E28")',
    '【性别】男性【年龄】<22岁,【性别】女性【年龄】<20岁,【婚姻】只允许填写"未婚"',
    '【性别】必填，不可填"-"参照接口标准',
    '【新生儿入院体重】只允许为10的整数倍',
    '【新生儿入院体重】填写范围在"100-9999"之间(单位:克)',
    '【新生儿入院体重】>0时,患者【年龄】应=0,且【年龄不足1周岁的年龄(天)】≤28天',
    '【新生儿出生体重】只允许为10的整数倍',
    '【新生儿出生体重】填写范围在"100-9999"之间(单位:克)',
    '【新生儿出生体重】>0时,患者应为产妇(主要诊断或其他诊断包含”Z37.0,Z37.2,Z37.3,Z37.5,Z37.6”)或新生儿(年龄为0,年龄不足1周岁的年龄(天)<29天)',
    '【现住址邮编】必填，可填"-"',
    '【损伤中毒的外部原因】编码必须以V、W、X、Y开头',
    '【现住址电话】应为11位数字(手机号码),或3到4位(区号位)+7到8位(电话位),区号位与电话为之间用"-"连接',
    '【现住址电话】必填，可填"-"',
    '【现住址（省、市、县、街道）】必填，可填"-"',
    '【手术日期6】应≥【入院时间】且<【出院时间】',
    '【手术日期5】应≥【入院时间】且<【出院时间】',
    '【手术日期4】应≥【入院时间】且<【出院时间】',
    '【手术日期3】应≥【入院时间】且<【出院时间】',
    '【手术日期2】应≥【入院时间】且<【出院时间】',
    '【手术日期1】应≥【入院时间】且<【出院时间】',
    '【手术日期7】应≥【入院时间】且<【出院时间】',
    '【手术日期8】应≥【入院时间】且<【出院时间】',
    '【手术日期9】应≥【入院时间】且<【出院时间】',
    '【手术日期10】应≥【入院时间】且<【出院时间】',
    '【手术日期11】应≥【入院时间】且<【出院时间】',
    '【手术日期12】应≥【入院时间】且<【出院时间】',
    '【手术日期13】应≥【入院时间】且<【出院时间】',
    '【手术日期14】应≥【入院时间】且<【出院时间】',
    '【手术日期15】应≥【入院时间】且<【出院时间】',
    '【手术日期16】应≥【入院时间】且<【出院时间】',
    '【手术日期17】应≥【入院时间】且<【出院时间】',
    '【手术日期18】应≥【入院时间】且<【出院时间】',
    '【手术日期19】应≥【入院时间】且<【出院时间】',
    '【手术日期20】应≥【入院时间】且<【出院时间】',
    '【是否有31天再入院计划】填写"有"时，【目的】必填',
    '【是否有31天内再次入院计划】必填,不可填"-"',
    '【实际住院天数】应>0且<8000的整数',
    '【实际住院天数】必填，不可填"-"',
    '【实际住院天数】=【出院时间】-【入院时间】',
    '【身份证号】与患者实际性别不符(身份证号第"17"位为奇数表示"男",为偶数表示"女")',
    '【身份证号】必填，可填"-"',
    '【身份证】录入内容与公安部身份证验证规则不符(外国国籍可填”-”)',
    '【入院途径】为"其他医疗机构转入",【转诊机构】必填不可填"-"',
    '【入院途径】为"门诊"时，【主要诊断】不允许为"急诊疾病编码"',
    '【入院途径】必填，不可填"-"参照接口标准',
    '【入院时间】应<【出院时间】',
    '【入院时间】必填，不可填"-"',
    '【入院时间-出生日期】的换算年龄>0,【不足一周岁患者年龄】必须填"0"',
    '【入院科别】必填，不可填"-"',
    '【主要诊断】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断9】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断8】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断7】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断6】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断5】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断4】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断3】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断22】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断21】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断20】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断2】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断19】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断18】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断17】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断16】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断15】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断14】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断13】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断12】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断11】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断10】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    #'【其他诊断1】(诊断名称\诊断编码和入院病情出院情况需同时填写)',
    '【年龄不足1周岁的年龄(天)】不为空且≤28天时,【年龄】应为"0"',
    '【年龄】在"3-14"岁之间，【职业】只能填写"学生"',
    '【年龄】与【入院时间-出生日期】的换算年龄不符(误差≤1岁)',
    '【年龄】不足28天,【新生儿入院体重】必填',
    '【年龄】不足28天,【新生儿出生体重】必填',
    '【年龄】不足14周岁【婚姻】不允许为”已婚\离婚\丧偶”',
    '【年龄】不足14周岁,【联系人关系】不允许为”夫\妻\配偶\子\女\孙子\孙女”',
    '【年龄】不足14岁,【职业】不允许为”国家公务员\现役军人\退(离)休人员”',
    '【年龄】不能为空(不满1岁填"0")',
    '【年龄】不能超过"200"',
    '【年龄】必填，不可填"-"',
    '【年龄】>0时,【年龄不足1周岁的年龄(天)】应为"0"或空',
    '【年龄】<16岁,【医疗付费方式】不能选择"城镇职工基本医疗保险"',
    '【年龄】<12岁,【出院诊断】不允许为"C50-C63"(乳房、女性及男性生殖器恶性肿瘤)',
    '【年龄(天)】填写范围"0-365"(不包含临界值)',
    '【民族】必填，可填"-"',
    '【门急诊诊断编码】必填，不可填"-"',
    '【门急诊诊断】必填，不可填"-"',
    '【门(急)诊诊断】【主要诊断】【其他诊断】中不能出现"V01-Y98"区段的编码',
    '【门(急)诊诊断】【出院诊断编码】中不能出现"M"开头且中间带"/"的ICD编码',
    '【麻醉费】>0,【麻醉医师】必填',
    '【麻醉费】>0,【麻醉方式】必填',
    '【联系人姓名】与患者【姓名】不能为相同',
    '【联系人姓名】必填，可填"-"',
    '【联系人姓名】【关系】【住址】【电话】应同时填写',
    '【联系人关系】为"配偶"时，【婚姻】不可填写"未婚"',
    '【联系人关系】必填，且不能填"本人"',
    '【联系人关系】必填，可填"-"参照接口标准',
    '【联系人电话】长度只允许为11位或7位',
    '【联系人电话】必填，可填"-"',
    '【联系人地址】必填，可填"-"',
    '【离院方式】为"医嘱转院"时，【转院，拟接收医疗机构名称】必填',
    '【离院方式】为"医嘱转社区卫生服务机构/乡镇卫生院"时，【转社区，拟接收医疗机构名称】必填',
    '【离院方式】为"死亡"时，【死亡患者尸检】必填',
    '【离院方式】必填，不可填"-"参照接口标准',
    '【科主任】必填，不可填"-"',
    '【进重症监护室时间1】应在住院期间内',
    '【进重症监护室时间2】应在住院期间内',
    '【进重症监护室时间3】应在住院期间内',
    '【进重症监护室时间4】应在住院期间内',
    '【进重症监护室时间5】应在住院期间内',
    '【籍贯】必填，可填"-"',
    '【婚姻状况】为"丧偶"时，【联系人关系】不可填写"夫\妻\配偶"',
    '【婚姻】必填，可填"-"参照接口标准',
    '【户口邮编】应为6位数字',
    '【户口地址邮编】必填，可填"-"',
    '【户口地址（省、市、县、街道）】必填，可填"-"',
    '【国籍】为"中国"时，【民族】不允许为"其他"',
    '【国籍】必填，可填"-"',
    '【工作邮编】应为6位数字',
    '【工作单位邮编】必填，可填"-"',
    '【工作单位及地址】必填，可填"-"',
    '【工作单位电话】必填，可填"-"',
    '【第次住院】必填，不可填"-"',
    '【单位电话】长度只允许为11位或7位',
    '【出院诊断编码】为"S06"开头，则【颅脑损失患者昏迷时间】必填',
    '【其他诊断编码】包含"Z37.则【新生儿出生体重】不可为空',
    '【出院时间】必填，不可填"-"',
    '【出院科别】必填，不可填"-"',
    '【出生日期】与【身份证】信息不符',
    '【出生日期】必填，不可填"-"',
    '【出生地（省、市、县）】必填，可填"-"',
    '【病理诊断费】>0时,【病理号】必填',
    '【病理诊断编码】中只允许填写以"M"开头且中间带"/"的ICD诊断',
    '【病理号】不为空,【病理诊断】必填',
    '【病案号】必填，不可填"-"',
    '【编码员】必填，不可填"-"',
    '【手术费用大于0】手术编码,手术名称手术日期必填，不可填"-"',
    '【（主要手术）名称】有名称，主要手术级别、主要手术操作切口等级必填，可填"-"',
    '【（主要手术）麻醉方式】有麻醉方式必填麻醉医师，不可填"-"',
    '【（主要出院诊断）入院病情】必填，不可填"-"',
    '【（主要出院诊断）名称】必填，不可填"-"',
    '【（主要出院诊断）编码】必填，不可填"-"',
    '【（入院）病房】必填，不可填"-"',
    '【（出院）病房】必填，不可填"-"',
    '【(不足一周岁)年龄】小于28天新生儿,(诊断编码列应为P开头)',
    '【总费用】=【分费用合】<1.2',
    '【总费用】必须>=【自付金额】',
    '【诊断编码】存在J42.-、J43.-时,存在合并编码J44.-',
    '【年龄小于15岁】疾病编码不能为J40.-',
    '【主要诊断其他诊断】如出现A40.-,A41.-则其他诊断不能出现A49.-',
    '【诊断编码】存在N13.2、N39.0,存在合并编码N13.6',#185
    '【诊断编码】存在N13.3、N20.2,存在合并编码N13.2',
    '【诊断编码】存在S52.2、S52.3,存在合并编码S52.4',
    '【诊断编码】存在S52.5、S52.8,存在合并编码S52.6',#188
    '【诊断编码】存在M51.2、M54.3,存在合并编码M51.1',
    '【诊断编码】存在K35.-、K65.-,可能存在合并编码',#190
    '【诊断编码】存在K80.2、K81.0,存在合并编码K80.0',#191
    '【诊断编码】存在K80.2、K81,存在合并编码K80.1',#192
    '【诊断编码】存在I11.9、I50,存在合并编码I11.0',#193
    '【诊断编码】存在J44.9、存在下呼吸道感染可疑存在合并编码J44.0',#194
    '【诊断编码】存在J42、J43.904,存在合并编码J44.-',#195
    '【诊断编码】R09.1、J94.8,存在合并编码J90',#196
    '【诊断编码】I15.102与诊断编码I11.901、存在联合诊断编码I13.9',#197
    '【诊断编码】I11.0与诊断编码I12.0、存在联合诊断编码I13.2',#198
    '【诊断编码】I63.9与诊断编码I65、可能存在联合诊断编码I63.-',
    '【诊断编码】I63.9与诊断编码I66、可能存在联合诊断编码I63.-',#200
    '【诊断编码】I05.9与诊断编码I06.9、存在联合诊断编码I08.000x002',#201
    '【诊断编码】I05.0与诊断编码I06.0、存在联合诊断编码I08.000x011',#202
    '【诊断编码】I05.0与诊断编码I06.1、存在联合诊断编码I08.001',
    '【诊断编码】I05.1与诊断编码I06.0、存在联合诊断编码I08.002',
    '【诊断编码】I05.1与诊断编码I06.2、存在联合诊断编码I08.003',#205
    '【诊断编码】I05.0与诊断编码I06.2、存在联合诊断编码I08.004',
    '【诊断编码】I05.2与诊断编码I06.1、存在联合诊断编码I08.005',
    '【诊断编码】I05.1与诊断编码I06.1、存在联合诊断编码I08.006',#208
    '【诊断编码】I05.2与诊断编码I06.2、存在联合诊断编码I08.007',
    '【诊断编码】I05.2与诊断编码I06.0、存在联合诊断编码I08.009',
    '【诊断编码】I05.9与诊断编码I07.9、存在联合诊断编码I08.100x001(2)',#211
    '【诊断编码】I05.0与诊断编码I07.1、存在联合诊断编码I08.101',
    '【诊断编码】I05.2与诊断编码I07.1、存在联合诊断编码I08.102',#213
    '【诊断编码】I05.1与诊断编码I07.1、存在联合诊断编码I08.103',
    '【诊断编码】I05.0与诊断编码I07.0、存在联合诊断编码I08.104',#215
    '【诊断编码】I06.9与诊断编码I07.9、存在联合诊断编码I08.200-',
    '【诊断编码】I06.1与诊断编码I07.1、存在联合诊断编码I08.201',#217
    '【诊断编码】I05.9、I06.9与诊断编码I07.9、存在联合诊断编码I08.300-',
    '【诊断编码】I05.2、I06.1与诊断编码I07.1、存在联合诊断编码I08.301',#219
    '【诊断编码】I05.0、I06.1与诊断编码I07.1、存在联合诊断编码I08.302',
    '【诊断编码】I05.1、I06.1与诊断编码I07.1、存在联合诊断编码I08.303',#221
    '【诊断编码】I05.2、I06.2与诊断编码I07.2、存在联合诊断编码I08.304',
    '【诊断编码】I05.2、I06.0与诊断编码I07.0、存在联合诊断编码I08.305',#223
    '【诊断编码】I05.2、I06.2与诊断编码I07.1、存在联合诊断编码I08.306',
    '【诊断编码】I05.2与诊断编码I09.802、存在联合诊断编码I08.801',#225
    '【诊断编码】I50.1与诊断编码J81、存在联合诊断编码I50.103',
    '【诊断编码】I50与诊断编码N18.8、存在联合诊断编码N18.800x020',#227
    '【诊断编码】D64与诊断编码N18.001、可能存在联合诊断编码N18.002+D63.8*',
    '【诊断编码】D64与诊断编码N18.-、可能存在联合诊断编码N18.810+D63.8*',#229
    '【诊断编码】E16.800x901与诊断编码I10、存在联合诊断编码E16.800x102',
    '【诊断编码】E16.800x901与诊断编码I10、E66,存在联合诊断编码E16.800x101',#231
    '【诊断编码】E66与诊断编码E10,I10、存在联合诊断编码E10.700x023',#232
    '【诊断编码】E66与诊断编码E11,I10、存在联合诊断编码E11.700x023',
    '【诊断编码】E66与诊断编码E14,I10、存在联合诊断编码E14.700x023',#234
    '【诊断编码】K31.9与诊断编码K92.2、可能存在联合诊断编码K25.-',
    '【诊断编码】K25.9与诊断编码K92.2、可能存在联合诊断编码K25.-',#236
    '【诊断编码】K26.9与诊断编码K92.2、可能存在联合诊断编码K26.-',
    '【诊断编码】K27.9与诊断编码K92.2、可能存在联合诊断编码K27.-',#238
    '【诊断编码】K28.9与诊断编码K92.2、可能存在联合诊断编码K28.-',
    '【诊断编码】J35.100与诊断编码J35.200、存在联合诊断编码J35.300',#240
    '【诊断编码】M06.900与诊断编码J84.101、存在联合诊断编码M05.102+J99.0*',
    '【诊断编码】M06.900与诊断编码J84.105、存在联合诊断编码M05.101+J99.0*',#242
    '【诊断编码】I50.101与诊断编码J81、存在联合诊断编码I50.103',
    '【诊断编码】B49.x00x007与诊断编码J18.9、存在联合诊断编码B49.x00x020',#244
    '【诊断编码】N20.0与诊断编码N20.1、存在联合诊断编码N20.2',
    '【诊断编码】N20.100与诊断编码N13.4、N10.x02存在联合诊断编码N13.602',#246
    '【诊断编码】J18.9与诊断编码A49,可能存在联合诊断编码J15.-',
    '【诊断编码】N13.301与诊断编码N13.504,存在联合诊断编码N13.100x001',#248
    '【诊断编码】S82.300x081与诊断编码S82.4,存在联合诊断编码S82.300x011',
    '【诊断编码】S82.800x082与诊断编码S82.4,存在联合诊断编码S82.600x001',#250
    '【诊断编码】K74.100与诊断编码I85.900x001,存在联合诊断编码K74.616+I98.2*',
    '【诊断编码】K70.300与诊断编码I85.900x001,存在联合诊断编码K70.301+I98.2*',
    '【诊断编码】K70.300与诊断编码I85.000x001,存在联合诊断编码K70.302+I98.3*',#253
    '【诊断编码】K70.300与诊断编码I86.400x001,存在联合诊断编码K70.300x004+I98.2*',
    '【诊断编码】K70.300与诊断编码I86.400x011,存在联合诊断编码K70.300x005+I98.3*',
    '【诊断编码】K70.300与诊断编码I85.900x001,I86.400x001,存在联合诊断编码K70.300x006+I98.2*',#256
    '【诊断编码】K70.300与诊断编码I86.800x014,存在联合诊断编码K70.300x007+I98.3*',
    '【诊断编码】K74.300与诊断编码I85.900x001,存在联合诊断编码K74.301+I98.2*',
    '【诊断编码】K74.300与诊断编码I85.000x001,存在联合诊断编码K74.302+I98.3*',
    '【诊断编码】K74.300与诊断编码I86.400x001,存在联合诊断编码K74.300x005+I98.2*',#260
    '【诊断编码】K74.300与诊断编码I86.400x011,存在联合诊断编码K74.300x006+I98.3*',
    '【诊断编码】K74.300与诊断编码I85.900x001,I86.400x001,存在联合诊断编码K74.300x007+I98.2*',
    '【诊断编码】K74.300与诊断编码I86.800x014,存在联合诊断编码K74.300x008+I98.3*',#263
    '【诊断编码】K74.600x003与诊断编码I85.900x001,存在联合诊断编码K74.616+I98.2*',
    '【诊断编码】K74.600x003与诊断编码I85.000x001,存在联合诊断编码K74.615+I98.3*',
    '【诊断编码】K74.600x003与诊断编码I86.400x001,存在联合诊断编码K74.620+I98.2*',#266
    '【诊断编码】K74.600x003与诊断编码I86.400x011,存在联合诊断编码K74.618+I98.3*',
    '【诊断编码】K74.600x003与诊断编码I85.900x001,I86.400x001,存在联合诊断编码K74.619+I98.2*',
    '【诊断编码】K74.600x003与诊断编码I86.800x014,存在联合诊断编码K74.617+I98.3*',#269
    '【诊断编码】K80.5与诊断编码K83.0,存在联合诊断编码K80.3',
    '【诊断编码】J44.806与诊断编码J43.9,存在联合诊断编码J44.801',
    '【诊断编码】J47.x00与诊断编码R04.200存在联合诊断编码J47.x01',
    '【诊断编码】K80.5与诊断编码K81,存在联合诊断编码K80.4',
    '【诊断编码】K26.9与诊断编码K31.800x806,存在联合诊断编码K26.5',#274
    '【诊断编码】K26.9与诊断编码K92.2,存在联合诊断编码K26.4',
    '【诊断编码】K26.9与诊断编码K92.2,K31.800x806,存在联合诊断编码K26.2',
    '【诊断编码】B24.x01与诊断编码B38.200,存在联合诊断编码B20.600',#277
    '【诊断编码】B24.x01与诊断编码A16.200x002,存在联合诊断编码B20.003',
    '【诊断编码】B24.x01与诊断编码C46,存在联合诊断编码B21.000',#279
    '【诊断编码】S82.100x087与诊断编码S82.4,存在联合诊断编码S82.200x012',
    '【诊断编码】I35.0与诊断编码I35.1,存在联合诊断编码I35.2',#281
    '【诊断编码】I34.0与诊断编码I34.2,存在联合诊断编码I34.800x012',
    '【诊断编码】I34.0与诊断编码I35.1,存在联合诊断编码I34.000x012',#283
    '【诊断编码】I36.0与诊断编码I36.1,存在联合诊断编码I36.2',
    '【诊断编码】I37.0与诊断编码I37.1,存在联合诊断编码I37.2',#285
    '【诊断编码】Z33.x与诊断编码B18,存在联合诊断编码O98.4',
    '【诊断编码】K26.900x002与诊断编码K92.208,K27.5,存在联合诊断编码K26.200x002',#287
    '【诊断编码】E16.800x901与诊断编码E16.801,存在联合诊断编码E16.800x104',
    '【诊断编码】E10.900与诊断编码E16.801,存在联合诊断编码E10.700x021',#289
    '【诊断编码】E11.900与诊断编码E16.801,存在联合诊断编码E11.700x021',
    '【诊断编码】E14.900与诊断编码E16.801,存在联合诊断编码E14.700x021',#291
    '【诊断编码】B02.9与诊断编码M79.207,存在联合诊断编码B02.202+G53.0*',
    '【诊断编码】I20与诊断编码I25.2,存在联合诊断编码I20.005',#293
    '【诊断编码】S82.100x087与诊断编码S82.100x085,存在联合诊断编码S82.100x088',
    '【诊断编码】S82.4与诊断编码S82.600,存在联合诊断编码S82.601',
    '【诊断编码】S83.201与诊断编码S83.5,存在联合诊断编码S83.700x005',#296
    '【诊断编码】S83.202与诊断编码S83.5,存在联合诊断编码S83.700x002',
    '【诊断编码】S82.4与诊断编码S82.800x081,存在联合诊断编码S82.601',
    '【诊断编码】S82.4与诊断编码S82.800x082,存在联合诊断编码S82.601',#299
    '【诊断编码】S82.600与诊断编码S82.500,存在联合诊断编码S82.802',
    '【诊断编码】S82.600与诊断编码S82.500,S82.300x081,存在联合诊断编码S82.801',  # 301
    '【诊断编码】M54.502与诊断编码M54.300,存在联合诊断编码M54.400',  # 302
    '【诊断编码】B02.9与诊断编码G58.001,存在联合诊断编码B02.206+G53.0*',  # 303
    '【诊断编码】S61.702与诊断编码S62.61,存在联合诊断编码S61.800x012',  # 304
    '【诊断编码】S61.701与诊断编码S62.11,存在联合诊断编码S61.800x013',  # 305
    '【诊断编码】S61.900与诊断编码S62.810,存在联合诊断编码S61.800x011',  # 306
    '【诊断编码】S61.900与诊断编码S63.2,存在联合诊断编码S61.800x021',  # 307
    '【诊断编码】S61.900与诊断编码S63.0,存在联合诊断编码S61.800x021',
    '【诊断编码】S61.900与诊断编码S63.1,存在联合诊断编码S61.800x021',
    '【诊断编码】S61.702与诊断编码S63.1,存在联合诊断编码S61.800x022',  # 310
    '【诊断编码】S61.701与诊断编码S63.0,存在联合诊断编码S61.800x023',  # 311
    '【诊断编码】K46.900x002与诊断编码K56.7,存在联合诊断编码K46.000x002',  # 312
    '【诊断编码】G45.801与诊断编码I77.102,存在联合诊断编码G45.800x004',  # 313
    '【诊断编码】G45.801与诊断编码I74.801,存在联合诊断编码G45.800x003',  # 314
    '【诊断编码】B49.x00x007与诊断编码A09.900x003,存在联合诊断编码B49.x16',  # 315
    '【诊断编码】K80.200x003与诊断编码K81.000,存在联合诊断编码K80.0',  # 316
    '【诊断编码】K56.700与诊断编码K55.004,存在联合诊断编码K56.603',
    '【诊断编码】K55.010与诊断编码K55.004,存在联合诊断编码K55.007',  # 318
    '【诊断编码】K55.006与诊断编码K55.004,存在联合诊断编码K55.009',
    '【诊断编码】K40.901与诊断编码K40.902,存在联合诊断编码K40.900x005',  # 320
    '【诊断编码】K40.900x003与诊断编码K40.900x004,存在联合诊断编码K40.204',
    '【诊断编码】K35.900与诊断编码K65.000,存在联合诊断编码K35.000',  # 322
    '【诊断编码】K35.003与诊断编码K65.0,存在联合诊断编码K35.000x009',
    '【诊断编码】K35.004与诊断编码K65.0,存在联合诊断编码K35.000x010',
    '【诊断编码】K35.003与诊断编码K65.901,存在联合诊断编码K35.000x012',  # 325
    '【诊断编码】K35.004与诊断编码K65.901,存在联合诊断编码K35.000x013',  # 326
    '【诊断编码】K35.002与诊断编码K65.901,存在联合诊断编码K35.001',  # 327
    '【诊断编码】K35.901与诊断编码K65.0,存在联合诊断编码K35.000x006',  # 328
    '【诊断编码】K35.903与诊断编码K65.0,存在联合诊断编码K35.000x007',  # 329
    '【诊断编码】K35.002与诊断编码K65.0,存在联合诊断编码K35.000x008',  # 330
    '【诊断编码】K35.001与诊断编码K65.901,存在联合诊断编码K35.000x004',  # 331
    '【诊断编码】K35.901与诊断编码K35.101,存在联合诊断编码K35.104',  # 332
    '【诊断编码】K36.x02与诊断编码K35.101,存在联合诊断编码K35.100x012',  # 333
    '【诊断编码】K35.901与诊断编码K35.103,存在联合诊断编码K35.104',  # 334
    '【诊断编码】K35.900与诊断编码K65.901,存在联合诊断编码K35.904',  # 335
    '【诊断编码】K35.900与诊断编码K65.900,存在联合诊断编码K35.902',  # 336
    '【诊断编码】K35.901与诊断编码K37.x00x002,存在联合诊断编码K35.905',  # 337
    '【诊断编码】K35.903与诊断编码K37.x00x002,存在联合诊断编码K35.906',  # 338
    '【诊断编码】K37.x00与诊断编码K35.101,存在联合诊断编码K35.100x011',  # 339
    '【诊断编码】K35.900与诊断编码K65.005,存在联合诊断编码K35.100x004',  # 340
    '【诊断编码】K35.900与诊断编码K65.004,存在联合诊断编码K35.100',  # 341
    '【诊断编码】J30.400与诊断编码J45.900,存在联合诊断编码J45.004',  # 342
    '【诊断编码】D59.500x001与诊断编码D61.9,存在联合诊断编码D59.501',  # 343
    '【诊断编码】B49.x00x007与诊断编码J20.900,存在联合诊断编码B49.x13',  # 344
    '【诊断编码】B02.700与诊断编码M79.207,存在联合诊断编码B02.202+G53.0*',
    '【诊断编码】B02.9与诊断编码M79.207,存在联合诊断编码B02.202+G53.0*',
    '【诊断编码】B02.700与诊断编码G62.900,存在联合诊断编码B02.200x008+G63.0*',
    '【诊断编码】B02.9与诊断编码G62.900,存在联合诊断编码B02.200x008+G63.0*', #348
    '【诊断编码】B02.700与诊断编码G58.001,存在联合诊断编码B02.206+G53.0*',
    '【诊断编码】B02.9与诊断编码G50.000,存在联合诊断编码B02.207+G53.0*', #350
    '【诊断编码】B02.700与诊断编码G50.000,存在联合诊断编码B02.207+G53.0*',
    '【诊断编码】S82.202与诊断编码S82.500,存在联合诊断编码S82.501', #352
    '【诊断编码】S82.4与诊断编码S82.500,存在联合诊断编码S82.601',
    '【诊断编码】S82.2与诊断编码S82.8,存在联合诊断编码S82.501', #354
    '【诊断编码】S82.4与诊断编码S82.8,存在联合诊断编码S82.601',
    '【诊断编码】S82.202与诊断编码S82.600,存在联合诊断编码S82.501', #356
    '【诊断编码】S82.4与诊断编码S82.600,存在联合诊断编码S82.601',
    '【诊断编码】N13.201与诊断编码N10.x02,存在联合诊断编码N13.601',#358
    '【诊断编码】E05.900x001与诊断编码E05.003,存在联合诊断编码E05.000x001',
    '【诊断编码】E05.900x001与诊断编码E04.902,存在联合诊断编码E05.200x002',
    '【诊断编码】N70.900x007与诊断编码N70.902,存在联合诊断编码N70.905',#361
    '【诊断编码】M34.900与诊断编码J84.101,存在联合诊断编码M34.801+J99.1*',
    '【诊断编码】M34.900x001与诊断编码J84.101,存在联合诊断编码M34.800x003+J99.1*',#363
    '【诊断编码】M35.000与诊断编码J84.101,存在联合诊断编码M35.002+J99.1*',
    '【诊断编码】M35.000与诊断编码K76.900x002,存在联合诊断编码M35.003+K77.8*',#365
    '【诊断编码】M35.000与诊断编码H16.200,存在联合诊断编码M35.005+H19.3*',
    '【诊断编码】M35.000与诊断编码N12.x00x007,存在联合诊断编码M35.006+N16.4*',
    '【诊断编码】M35.000与诊断编码N12.x02,存在联合诊断编码M35.007+N16.4*',#368
    '【诊断编码】M35.101与诊断编码N18.900,存在联合诊断编码M35.102',
    '【诊断编码】M06.900与诊断编码I51.400,存在联合诊断编码M05.306+I41.8*',#370
    '【诊断编码】M06.900与诊断编码I31.902,存在联合诊断编码M05.305+I32.8*',
    '【诊断编码】M06.900与诊断编码I38.x00x006,存在联合诊断编码M05.307+I39.8*',#372
    '【诊断编码】K57.303与诊断编码K65.9,存在联合诊断编码K57.202',
    '【诊断编码】I11.901与诊断编码I12.000x001,I50.900,存在联合诊断编码I13.200',#374
    '【诊断编码】A53.900与诊断编码K74.100,存在联合诊断编码A52.705+K77.0*',
    '【诊断编码】S83.201与诊断编码S83.400x003,存在联合诊断编码S83.700x004',#376
    '【诊断编码】S83.202与诊断编码S83.400x003,存在联合诊断编码S83.700x001',
    '【诊断编码】S83.202与诊断编码S83.500x003,存在联合诊断编码S83.700x002',#378
    '【诊断编码】S83.201与诊断编码S83.500x003,存在联合诊断编码S83.700x005',
    '【诊断编码】S61.800x081与诊断编码S62.210,存在联合诊断编码S61.800x012',#380
    '【诊断编码】S61.900x002与诊断编码S62.111,存在联合诊断编码S61.800x013',
    '【诊断编码】S61.900与诊断编码S62.810,存在联合诊断编码S61.800x011',#382
    '【诊断编码】N13.301与诊断编码N13.501,存在联合诊断编码N13.000',
    '【诊断编码】M34.900x001与诊断编码J84.101,存在联合诊断编码M34.801+J99.1*',#384
    '【诊断编码】K80.200x003与诊断编码K81.006,存在联合诊断编码K80.000x004',
    '【诊断编码】K80.200x003与诊断编码K81.003,存在联合诊断编码K80.001',#386
    '【诊断编码】K80.200x003与诊断编码K81.002,存在联合诊断编码K80.002',
    '【诊断编码】K80.200x003与诊断编码K81.000,存在联合诊断编码K80.000',#388
    '【诊断编码】K80.200x003与诊断编码K81.900,存在联合诊断编码K80.100x001',
    '【诊断编码】K80.200x003与诊断编码K81.100,存在联合诊断编码K80.101',#390
    '【诊断编码】K80.501与诊断编码K81.000,存在联合诊断编码K80.402',
    '【诊断编码】K80.501与诊断编码K81.100,存在联合诊断编码K80.404',#392
    '【诊断编码】K80.500x002与诊断编码K83.000,存在联合诊断编码K80.300',
    '【诊断编码】K80.501与诊断编码K83.003,存在联合诊断编码K80.300x005',#394
    '【诊断编码】K80.501与诊断编码K83.000x007,存在联合诊断编码K80.301',
    '【诊断编码】K80.501与诊断编码K83.000,存在联合诊断编码K80.302',#396
    '【诊断编码】K80.504与诊断编码K83.000,存在联合诊断编码K80.303',
    '【诊断编码】K80.501与诊断编码K83.001,存在联合诊断编码K80.304',#398
    '【诊断编码】K80.503与诊断编码K83.000,存在联合诊断编码K80.305',
    '【诊断编码】K80.503与诊断编码K81.100,存在联合诊断编码K80.400x004',#400
    '【诊断编码】K80.501与诊断编码K81.000,存在联合诊断编码K80.401',
    '【诊断编码】K80.501与诊断编码K81.100,存在联合诊断编码K80.403',#402
    '【诊断编码】K80.501与诊断编码K81.900,存在联合诊断编码K80.405',
    '【诊断编码】K80.507与诊断编码K81.100,存在联合诊断编码K80.406',#404
    '【诊断编码】K46.9与诊断编码K56.6,存在联合诊断编码k46.1',
    '【诊断编码】J47.x00与诊断编码R04.200,存在联合诊断编码J47.x01',#406
    '【诊断编码】B16.905与诊断编码K72.903,存在联合诊断编码B16.204',
    '【诊断编码】B16.901与诊断编码K72.903,存在联合诊断编码B16.206',#408
    '【诊断编码】Z34与诊断编码E78.500,存在联合诊断编码O99.212',
    '【诊断编码】Z33与诊断编码E78.500,存在联合诊断编码O99.212',#410
    '【诊断编码】Z33与诊断编码H33,存在联合诊断编码O99.814',
    '【诊断编码】Z34与诊断编码H33,存在联合诊断编码O99.814',#412
    '【诊断编码】Z33与诊断编码M79.0,存在联合诊断编码O99.813',
    '【诊断编码】Z34与诊断编码M79.0,存在联合诊断编码O99.813',#414
    '【诊断编码】Z33与诊断编码M35.0,存在联合诊断编码O99.812',
    '【诊断编码】Z34与诊断编码M35.0,存在联合诊断编码O99.812',#416
    '【诊断编码】Z33与诊断编码M32,存在联合诊断编码O99.811',
    '【诊断编码】Z34与诊断编码M32,存在联合诊断编码O99.811',#418
    '【诊断编码】Z33与诊断编码N20.0,存在联合诊断编码O99.810',
    '【诊断编码】Z34与诊断编码N20.0,存在联合诊断编码O99.810',#420
    '【诊断编码】Z33与诊断编码C9-,存在联合诊断编码O99.809',
    '【诊断编码】Z34与诊断编码C9-,存在联合诊断编码O99.809',#422
    '【诊断编码】Z33与诊断编码R73.0,存在联合诊断编码O99.800x511',
    '【诊断编码】Z34与诊断编码R73.0,存在联合诊断编码O99.800x511',#424
    '【诊断编码】Z33与诊断编码Q21,存在联合诊断编码O99.800x414',
    '【诊断编码】Z34与诊断编码Q21,存在联合诊断编码O99.800x414',#426
    '【诊断编码】Z33与诊断编码Q28.2,存在联合诊断编码O99.800x413',
    '【诊断编码】Z34与诊断编码Q28.2,存在联合诊断编码O99.800x413',#428
    '【诊断编码】Z33与诊断编码Q28.3,存在联合诊断编码O99.800x413',
    '【诊断编码】Z34与诊断编码Q28.3,存在联合诊断编码O99.800x413',#430
    '【诊断编码】Z33与诊断编码Q20,存在联合诊断编码O99.800x412',
    '【诊断编码】Z34与诊断编码Q20,存在联合诊断编码O99.800x412',#432
    '【诊断编码】Z33与诊断编码Q21,存在联合诊断编码O99.800x412',
    '【诊断编码】Z34与诊断编码Q21,存在联合诊断编码O99.800x412',#434
    '【诊断编码】Z33与诊断编码Q22,存在联合诊断编码O99.800x412',
    '【诊断编码】Z34与诊断编码Q22,存在联合诊断编码O99.800x412',#436
    '【诊断编码】Z33与诊断编码Q23,存在联合诊断编码O99.800x412',
    '【诊断编码】Z34与诊断编码Q23,存在联合诊断编码O99.800x412',#438
    '【诊断编码】Z33与诊断编码Q24,存在联合诊断编码O99.800x412',
    '【诊断编码】Z34与诊断编码Q24,存在联合诊断编码O99.800x412',#440
    '【诊断编码】Z33与诊断编码N75.000,存在联合诊断编码O99.800x319',
    '【诊断编码】Z34与诊断编码N75.000,存在联合诊断编码O99.800x319',#442
    '【诊断编码】Z33与诊断编码N98.100x001,存在联合诊断编码O99.800x318',
    '【诊断编码】Z34与诊断编码N98.100x001,存在联合诊断编码O99.800x318',#444
    '【诊断编码】Z33与诊断编码N20.1,存在联合诊断编码O99.800x316',
    '【诊断编码】Z34与诊断编码N20.1,存在联合诊断编码O99.800x316',#446
    '【诊断编码】Z33与诊断编码N25.8,存在联合诊断编码O99.800x315',
    '【诊断编码】Z34与诊断编码N25.8,存在联合诊断编码O99.800x315',#448
    '【诊断编码】Z33与诊断编码N21.100,存在联合诊断编码O99.800x314',
    '【诊断编码】Z34与诊断编码N21.100,存在联合诊断编码O99.800x314',#450
    '【诊断编码】Z33与诊断编码N90.400,存在联合诊断编码O99.800x312',
    '【诊断编码】Z34与诊断编码N90.400,存在联合诊断编码O99.800x312',#452
    '【诊断编码】Z33与诊断编码M45.x00,存在联合诊断编码O99.800x216',
    '【诊断编码】Z34与诊断编码M45.x00,存在联合诊断编码O99.800x216',#454
    '【诊断编码】Z33与诊断编码M51,存在联合诊断编码O99.800x215',
    '【诊断编码】Z34与诊断编码M51,存在联合诊断编码O99.800x215',#456
    '【诊断编码】Z33与诊断编码H35.304,存在联合诊断编码O99.800x115',
    '【诊断编码】Z34与诊断编码H35.304,存在联合诊断编码O99.800x115',#458
    '【诊断编码】Z33与诊断编码H35,存在联合诊断编码O99.800x114',
    '【诊断编码】Z34与诊断编码H35,存在联合诊断编码O99.800x114',#460
    '【诊断编码】Z33与诊断编码K31.814,存在联合诊断编码O99.624',
    '【诊断编码】Z34与诊断编码K31.814,存在联合诊断编码O99.624',#462
    '【诊断编码】Z33与诊断编码K40,存在联合诊断编码O99.623',
    '【诊断编码】Z34与诊断编码K40,存在联合诊断编码O99.623',#464
    '【诊断编码】Z33与诊断编码K41,存在联合诊断编码O99.623',
    '【诊断编码】Z34与诊断编码K41,存在联合诊断编码O99.623',#466
    '【诊断编码】Z33与诊断编码K60.3,存在联合诊断编码O99.622',
    '【诊断编码】Z34与诊断编码K60.3,存在联合诊断编码O99.622',#468
    '【诊断编码】Z33与诊断编码K85,存在联合诊断编码O99.619',
    '【诊断编码】Z34与诊断编码K85,存在联合诊断编码O99.619',#470
    '【诊断编码】Z33与诊断编码K83.012,存在联合诊断编码O99.618',
    '【诊断编码】Z34与诊断编码K83.012,存在联合诊断编码O99.618',#472
    '【诊断编码】Z33与诊断编码K81.900,存在联合诊断编码O99.617',
    '【诊断编码】Z34与诊断编码K81.900,存在联合诊断编码O99.617',#474
    '【诊断编码】Z33与诊断编码K82.802,存在联合诊断编码O99.616',
    '【诊断编码】Z34与诊断编码K82.802,存在联合诊断编码O99.616',#476
    '【诊断编码】Z33与诊断编码K80.200x003,存在联合诊断编码O99.615',
    '【诊断编码】Z34与诊断编码K80.200x003,存在联合诊断编码O99.615',#478
    '【诊断编码】Z33与诊断编码K76.600,存在联合诊断编码O99.614',
    '【诊断编码】Z34与诊断编码K76.600,存在联合诊断编码O99.614',#480
    '【诊断编码】Z33与诊断编码K35,存在联合诊断编码O99.611',
    '【诊断编码】Z34与诊断编码K35,存在联合诊断编码O99.611',#482
    '【诊断编码】Z33与诊断编码K46,存在联合诊断编码O99.609',
    '【诊断编码】Z34与诊断编码K46,存在联合诊断编码O99.609',#484
    '【诊断编码】Z33与诊断编码K56,存在联合诊断编码O99.607',
    '【诊断编码】Z34与诊断编码K56,存在联合诊断编码O99.607',#486
    '【诊断编码】Z33与诊断编码K29.700,存在联合诊断编码O99.604',
    '【诊断编码】Z34与诊断编码K29.700,存在联合诊断编码O99.604',#488
    '【诊断编码】Z33与诊断编码K29.1,存在联合诊断编码O99.603',
    '【诊断编码】Z34与诊断编码K29.1,存在联合诊断编码O99.603',#490
    '【诊断编码】Z33与诊断编码K92.208,存在联合诊断编码O99.601',
    '【诊断编码】Z34与诊断编码K92.208,存在联合诊断编码O99.601',#492
    '【诊断编码】Z33与诊断编码K52.916,存在联合诊断编码O99.600x010',
    '【诊断编码】Z34与诊断编码K52.916,存在联合诊断编码O99.600x010',#494
    '【诊断编码】Z33与诊断编码K52.915,存在联合诊断编码O99.600x007',
    '【诊断编码】Z34与诊断编码K52.915,存在联合诊断编码O99.600x007',#496
    '【诊断编码】Z33与诊断编码K52.905,存在联合诊断编码O99.600x006',
    '【诊断编码】Z34与诊断编码K52.905,存在联合诊断编码O99.600x006',#498
    '【诊断编码】Z33与诊断编码A09,存在联合诊断编码O99.600x006',
    '【诊断编码】Z34与诊断编码A09,存在联合诊断编码O99.600x006',#500
    '【诊断编码】Z33与诊断编码K35,存在联合诊断编码O99.600x001',
    '【诊断编码】Z34与诊断编码K35,存在联合诊断编码O99.600x001',#502
    '【诊断编码】Z33与诊断编码J80.x00x002,存在联合诊断编码O99.512',
    '【诊断编码】Z34与诊断编码J80.x00x002,存在联合诊断编码O99.512',#504
    '【诊断编码】Z33与诊断编码J90,存在联合诊断编码O99.511',
    '【诊断编码】Z34与诊断编码J90,存在联合诊断编码O99.511',#506
    '【诊断编码】Z33与诊断编码J06.900,存在联合诊断编码O99.510',
    '【诊断编码】Z34与诊断编码J06.900,存在联合诊断编码O99.510',#508
    '【诊断编码】Z33与诊断编码J96,存在联合诊断编码O99.509',
    '【诊断编码】Z34与诊断编码J96,存在联合诊断编码O99.509',#510
    '【诊断编码】Z33与诊断编码J45.0,存在联合诊断编码O99.508',
    '【诊断编码】Z34与诊断编码J45.0,存在联合诊断编码O99.508',#512
    '【诊断编码】Z33与诊断编码J81.x00,存在联合诊断编码O99.507',
    '【诊断编码】Z34与诊断编码J81.x00,存在联合诊断编码O99.507',#514
    '【诊断编码】Z33与诊断编码J98.414,存在联合诊断编码O99.506',
    '【诊断编码】Z34与诊断编码J98.414,存在联合诊断编码O99.506',#516
    '【诊断编码】Z33与诊断编码J98.101,存在联合诊断编码O99.505',
    '【诊断编码】Z34与诊断编码J98.101,存在联合诊断编码O99.505',#518
    '【诊断编码】Z33与诊断编码J45.9,存在联合诊断编码O99.504',
    '【诊断编码】Z34与诊断编码J45.9,存在联合诊断编码O99.504',#520
    '【诊断编码】Z33与诊断编码J40.x00,存在联合诊断编码O99.503',
    '【诊断编码】Z34与诊断编码J40.x00,存在联合诊断编码O99.503',#522
    '【诊断编码】Z33与诊断编码J47,存在联合诊断编码O99.502',
    '【诊断编码】Z34与诊断编码J47,存在联合诊断编码O99.502',#524
    '【诊断编码】Z33与诊断编码I63,存在联合诊断编码O99.431',
    '【诊断编码】Z34与诊断编码I63,存在联合诊断编码O99.431',#526
    '【诊断编码】Z33与诊断编码I61,存在联合诊断编码O99.430',
    '【诊断编码】Z34与诊断编码I61,存在联合诊断编码O99.430',#528
    '【诊断编码】Z33与诊断编码M06.9,存在联合诊断编码O99.425',
    '【诊断编码】Z34与诊断编码M06.9,存在联合诊断编码O99.425',#530
    '【诊断编码】Z33与诊断编码I45.6,存在联合诊断编码O99.423',
    '【诊断编码】Z34与诊断编码I45.6,存在联合诊断编码O99.423',#532
    '【诊断编码】Z33与诊断编码I44.700,存在联合诊断编码O99.424',
    '【诊断编码】Z34与诊断编码I44.700,存在联合诊断编码O99.424',#534
    '【诊断编码】Z33与诊断编码I45.103,存在联合诊断编码O99.422',
    '【诊断编码】Z34与诊断编码I45.103,存在联合诊断编码O99.422',#536
    '【诊断编码】Z33与诊断编码I50,存在联合诊断编码O99.414',
    '【诊断编码】Z34与诊断编码I50,存在联合诊断编码O99.414',#538
    '【诊断编码】Z33与诊断编码I24.800x001,存在联合诊断编码O99.409',
    '【诊断编码】Z34与诊断编码I24.800x001,存在联合诊断编码O99.409',#540
    '【诊断编码】Z33与诊断编码I21,存在联合诊断编码O99.400x033',
    '【诊断编码】Z34与诊断编码I21,存在联合诊断编码O99.400x033',#542
    '【诊断编码】Z33与诊断编码I22,存在联合诊断编码O99.400x033',
    '【诊断编码】Z34与诊断编码I22,存在联合诊断编码O99.400x033',#544
    '【诊断编码】Z33与诊断编码I27,存在联合诊断编码O99.400x004',
    '【诊断编码】Z34与诊断编码I27,存在联合诊断编码O99.400x004',#546
    '【诊断编码】Z33与诊断编码F,存在联合诊断编码O99.311',
    '【诊断编码】Z34与诊断编码F,存在联合诊断编码O99.311',#548
    '【诊断编码】Z33与诊断编码G80,存在联合诊断编码O99.309',
    '【诊断编码】Z34与诊断编码G80,存在联合诊断编码O99.309',#550
    '【诊断编码】Z33与诊断编码G82,存在联合诊断编码O99.308',
    '【诊断编码】Z34与诊断编码G82,存在联合诊断编码O99.308',#552
    '【诊断编码】Z33与诊断编码G40,存在联合诊断编码O99.306',
    '【诊断编码】Z34与诊断编码G40,存在联合诊断编码O99.306',#554
    '【诊断编码】Z33与诊断编码G41,存在联合诊断编码O99.306',
    '【诊断编码】Z34与诊断编码G41,存在联合诊断编码O99.306',#556
    '【诊断编码】Z33与诊断编码E26,存在联合诊断编码O99.225',
    '【诊断编码】Z34与诊断编码E26,存在联合诊断编码O99.225',#558
    '【诊断编码】Z33与诊断编码E27.100,存在联合诊断编码O99.222',
    '【诊断编码】Z34与诊断编码E27.100,存在联合诊断编码O99.222',#560
    '【诊断编码】Z33与诊断编码E23.2,存在联合诊断编码O99.221',
    '【诊断编码】Z34与诊断编码E23.2,存在联合诊断编码O99.221',#562
    '【诊断编码】Z33与诊断编码E24,存在联合诊断编码O99.220',
    '【诊断编码】Z34与诊断编码E24,存在联合诊断编码O99.220',#564
    '【诊断编码】Z33与诊断编码E04,存在联合诊断编码O99.219',
    '【诊断编码】Z34与诊断编码E04,存在联合诊断编码O99.219',#566
    '【诊断编码】Z33与诊断编码E06,存在联合诊断编码O99.218',
    '【诊断编码】Z34与诊断编码E06,存在联合诊断编码O99.218',#568
    '【诊断编码】Z33与诊断编码E66.9,存在联合诊断编码O99.207',
    '【诊断编码】Z34与诊断编码E66.9,存在联合诊断编码O99.207',#570
    '【诊断编码】Z33与诊断编码E23.000x010,存在联合诊断编码O99.200x003',
    '【诊断编码】Z34与诊断编码E23.000x010,存在联合诊断编码O99.200x003',#572
    '【诊断编码】Z33与诊断编码E14.1,存在联合诊断编码O99.200x007',
    '【诊断编码】Z34与诊断编码E14.1,存在联合诊断编码O99.200x007',#574
    '【诊断编码】Z33与诊断编码E11.1,存在联合诊断编码O99.200x007',
    '【诊断编码】Z34与诊断编码E11.1,存在联合诊断编码O99.200x007',#576
    '【诊断编码】Z33与诊断编码E10.1,存在联合诊断编码O99.200x007',
    '【诊断编码】Z34与诊断编码E10.1,存在联合诊断编码O99.200x007',#578
    '【诊断编码】Z33与诊断编码D64,存在联合诊断编码O99.000x0-',
    '【诊断编码】Z34与诊断编码D64,存在联合诊断编码O99.000x0-',#580
    '【诊断编码】Z33与诊断编码A49,存在联合诊断编码O98.804',
    '【诊断编码】Z34与诊断编码A49,存在联合诊断编码O98.804',#582
    '【诊断编码】Z33与诊断编码A41,存在联合诊断编码O98.801',
    '【诊断编码】Z34与诊断编码A41,存在联合诊断编码O98.801',#584
    '【诊断编码】Z33与诊断编码B16.904,存在联合诊断编码O98.402',
    '【诊断编码】Z34与诊断编码B16.904,存在联合诊断编码O98.402',#586
    '【诊断编码】Z33与诊断编码B15,存在联合诊断编码O98.401',
    '【诊断编码】Z34与诊断编码B15,存在联合诊断编码O98.401',#588
    '【诊断编码】Z33与诊断编码B18.2,存在联合诊断编码O98.403',
    '【诊断编码】Z34与诊断编码B18.2,存在联合诊断编码O98.403',#590
    '【诊断编码】Z33与诊断编码N83.100x005,存在联合诊断编码O34.800x018',
    '【诊断编码】Z34与诊断编码N83.100x005,存在联合诊断编码O34.800x018',#592
    '【诊断编码】Z33与诊断编码N83.502,存在联合诊断编码O34.806',
    '【诊断编码】Z34与诊断编码N83.502,存在联合诊断编码O34.806',#594
    '【诊断编码】Z33与诊断编码N83.500x003,存在联合诊断编码O34.800x014',
    '【诊断编码】Z34与诊断编码N83.500x003,存在联合诊断编码O34.800x014',#596
    '【诊断编码】Z33与诊断编码N83.2,存在联合诊断编码O34.802',
    '【诊断编码】Z34与诊断编码N83.2,存在联合诊断编码O34.802',#598
    '【诊断编码】Z33与诊断编码N80.1,存在联合诊断编码O34.800x031',
    '【诊断编码】Z34与诊断编码N80.1,存在联合诊断编码O34.800x031',#600
    '【诊断编码】Z33与诊断编码N83.800x006,存在联合诊断编码O34.800x022',
    '【诊断编码】Z34与诊断编码N83.800x006,存在联合诊断编码O34.800x022',#602
    '【诊断编码】Z33与诊断编码N83.809,存在联合诊断编码O34.800x021',
    '【诊断编码】Z34与诊断编码N83.809,存在联合诊断编码O34.800x021',#604
    '【诊断编码】Z33与诊断编码N70.1,存在联合诊断编码O34.800x017',
    '【诊断编码】Z34与诊断编码N70.1,存在联合诊断编码O34.800x017',#606
    '【诊断编码】Z33与诊断编码D39.706,存在联合诊断编码O34.800x013',
    '【诊断编码】Z34与诊断编码D39.706,存在联合诊断编码O34.800x013',#608
    '【诊断编码】Z33与诊断编码N80.100x003,存在联合诊断编码O34.800x009',
    '【诊断编码】Z34与诊断编码N80.100x003,存在联合诊断编码O34.800x009',#610
    '【诊断编码】Z33与诊断编码D39.101,存在联合诊断编码O34.800x006',
    '【诊断编码】Z34与诊断编码D39.101,存在联合诊断编码O34.800x006',#612
    '【诊断编码】Z33与诊断编码E87.600,存在联合诊断编码O99.205',
    '【诊断编码】Z34与诊断编码E87.600,存在联合诊断编码O99.205',#614
    '【诊断编码】Z33与诊断编码E87.102,存在联合诊断编码O99.200x017',
    '【诊断编码】Z34与诊断编码E87.102,存在联合诊断编码O99.200x017',#616
    '【诊断编码】S61.800x081与诊断编码S62.311,存在联合诊断编码S61.800x012',
    '【诊断编码】S61.800x081与诊断编码S62.301,存在联合诊断编码S61.800x012',#618
    '【诊断编码】S51.901与诊断编码S52.81,存在联合诊断编码S51.800x011',
    '【诊断编码】S51.901与诊断编码S52.4,存在联合诊断编码S51.800x011',#620
    '【诊断编码】S37.300与诊断编码N35.900,存在联合诊断编码S37.303',
    '【诊断编码】S05.601与诊断编码S05.206,存在联合诊断编码S05.201',#622
    '【诊断编码】S05.601与诊断编码S05.205,存在联合诊断编码S05.202',
    '【诊断编码】S05.601与诊断编码S05.200x001,存在联合诊断编码S05.204',#624
    '【诊断编码】S05.601与诊断编码S05.200x005,存在联合诊断编码S05.203',
    '【诊断编码】O21.9与诊断编码E87.8,存在联合诊断编码O21.100',#626
    '【诊断编码】N13.203与诊断编码N10,存在联合诊断编码N13.603',
    '【诊断编码】N13.201与诊断编码N10,存在联合诊断编码N13.601',#628
    '【诊断编码】N13.202与诊断编码N10,存在联合诊断编码N13.602',
    '【诊断编码】N20.000与诊断编码N13.301,存在联合诊断编码N13.201',#630
    '【诊断编码】N20.100与诊断编码N13.301,存在联合诊断编码N13.202',
    '【诊断编码】N20.200与诊断编码N13.301,存在联合诊断编码N13.203',#632
    '【诊断编码】M50.201与诊断编码G95.900,存在联合诊断编码M50.001+G99.2*',
    '【诊断编码】M50.201与诊断编码G96.900x003,存在联合诊断编码M50.101+G55.1*',
    '【诊断编码】M51.201与诊断编码G95.900,存在联合诊断编码M51.001+G99.2*',#635
    '【诊断编码】M51.203与诊断编码G95.900,存在联合诊断编码M51.002+G99.2*',
    '【诊断编码】M51.201与诊断编码G96.900x003,存在联合诊断编码M51.100x002+G55.1*',#637
    '【诊断编码】M51.202与诊断编码G95.900,存在联合诊断编码M51.003+G99.2*',
    '【诊断编码】M51.204与诊断编码G95.900,存在联合诊断编码M51.004+G99.2*',#639
    '【诊断编码】M51.202与诊断编码G96.900x003,存在联合诊断编码M51.100+G55.1*',
    '【诊断编码】M51.2与诊断编码M54.300,存在联合诊断编码M51.101+G55.1*',#641
    '【诊断编码】M54.502与诊断编码M54.300,存在联合诊断编码M54.400',
    '【诊断编码】K57.303与诊断编码K65.900,存在联合诊断编码K57.202',#643
    '【诊断编码】K46.900x002与诊断编码K56.700x003,存在联合诊断编码K46.000x002',
    '【诊断编码】K25.900x001与诊断编码K31.814,存在联合诊断编码K25.500x001',#645
    '【诊断编码】K25.900x001与诊断编码K92.201,存在联合诊断编码K25.400x001',
    '【诊断编码】K25.300x001与诊断编码K92.2,存在联合诊断编码K25.000',#647
    '【诊断编码】K25.300x001与诊断编码K27.5,存在联合诊断编码K25.100x001',
    '【诊断编码】K25.300x001与诊断编码K27.5、k92.2,存在联合诊断编码K25.200x001',#649
    '【诊断编码】K26.900x002与诊断编码k92.2,存在联合诊断编码K26.001',
    '【诊断编码】K26.900x002与诊断编码K27.5,存在联合诊断编码K26.100',#651
    '【诊断编码】K26.900x002与诊断编码K27.5、k92.2,存在联合诊断编码K26.200x002',
    '【诊断编码】K28.900x001与诊断编码K28.500x011,存在联合诊断编码K28.500x001',
    '【诊断编码】K28.600x011与诊断编码K91.800x108,存在联合诊断编码K28.900x002',#654
    '【诊断编码】K26.900x002与诊断编码K31.102,存在联合诊断编码K31.800x812',
    '【诊断编码】K27.902与诊断编码k92.2,存在联合诊断编码K27.401',#656
    '【诊断编码】K27.902与诊断编码K27.5,存在联合诊断编码K27.502',
    '【诊断编码】K27.700x001与诊断编码K27.5、k92.2,存在联合诊断编码K27.600',
    '【诊断编码】K27.700x001与诊断编码K27.5,存在联合诊断编码K27.500',#659
    '【诊断编码】K27.700x001与诊断编码k92.2,存在联合诊断编码K27.400',
    '【诊断编码】J44.9与诊断编码J22.x00存在联合诊断编码J44.000',#661
    '【诊断编码】J44.802与诊断编码J43.904存在联合诊断编码J44.801',
    '【诊断编码】J10.101与诊断编码I40.001存在联合诊断编码J10.802+I41.1*',#663
    '【诊断编码】J10.101与诊断编码G93.1存在联合诊断编码J10.801+G94.8*',
    '【诊断编码】J10.100x001与诊断编码I40.001存在联合诊断编码J10.800x003+I41.1*',#665
    '【诊断编码】J10.100x001与诊断编码G93.1存在联合诊断编码J10.800x001+G94.8*',
    '【诊断编码】J10.100x001与诊断编码A09.001存在联合诊断编码J10.800x002',#667
    '【诊断编码】H02.003与诊断编码H02.004存在联合诊断编码H02.000',
    '【诊断编码】C79.300x002与诊断编码C79.301存在联合诊断编码C79.300x005',#669
    '【诊断编码】C17.000与诊断编码K92.203存在联合诊断编码K92.800x011',
    '【诊断编码】C16与诊断编码K92.201存在联合诊断编码K92.800x002',#671
    '【诊断编码】C15与诊断编码K92.208存在联合诊断编码K92.800x001',
    '【诊断编码】C16.903与诊断编码K92.202存在联合诊断编码K92.800x003',#673
    '【诊断编码】C18与诊断编码K92.206存在联合诊断编码K92.800x005',
    '【诊断编码】C22与诊断编码K76.803存在联合诊断编码K92.800x006',#675
    '【诊断编码】C21与诊断编码K92.200x001存在联合诊断编码K92.800x007',
    '【诊断编码】C23.x00与诊断编码K82.800x002存在联合诊断编码K92.800x009',#677
    '【诊断编码】C24与诊断编码K83.809存在联合诊断编码K92.800x010',
    '【诊断编码】C15.900与诊断编码K22.804存在联合诊断编码K92.800x001',#679
    '【诊断编码】B49.x00x007与诊断编码K20.x00存在联合诊断编码B49.x12',
    '【诊断编码】B49.x00x007与诊断编码J32.900存在联合诊断编码B49.x05',
    '【诊断编码】B49.x00x007与诊断编码J32.000存在联合诊断编码B49.x09',
    '【诊断编码】B49.x00x007与诊断编码J18.9存在联合诊断编码B49.x00x020',
    '【诊断编码】B49.x00x007与诊断编码J98.402存在联合诊断编码B49.x14+J99.8*',#684
    '【诊断编码】B34.101与诊断编码J20.900存在联合诊断编码J20.300',
    '【诊断编码】K74.300与诊断编码I86.400x001存在联合诊断编码K74.300x005+I98.2*',#686
    '【诊断编码】K74.300与诊断编码I86.400x011存在联合诊断编码K74.300x006+I98.3*',
    '【诊断编码】K74.300与诊断编码I86.800x014存在联合诊断编码K74.300x008+I98.3*',#688
    '【诊断编码】K74.300与诊断编码I85.900x001存在联合诊断编码K74.300x012+I98.3',
    '【诊断编码】K74.300与诊断编码I85.000x001存在联合诊断编码K74.302+I98.3*',#690
    '【诊断编码】B24.x01与诊断编码B02.900x001存在联合诊断编码B20.301',
    '【诊断编码】B24.x01与诊断编码A16.500x004存在联合诊断编码B20.001',
    '【诊断编码】B24.x01与诊断编码A15.000x001存在联合诊断编码B20.003',#693
    '【诊断编码】A54.900x001与诊断编码N76.000x003存在联合诊断编码A54.004',
    '【诊断编码】A54.900x001与诊断编码N34.200x002存在联合诊断编码A54.002',
    '【诊断编码】Z35.401与诊断编码J45.900存在联合诊断编码O99.504',#696
    '【诊断编码】Z34.900x001与诊断编码N76.000存在联合诊断编码O23.506',
    '【诊断编码】Z34.900x001与诊断编码J45.005存在联合诊断编码O99.508',
    '【诊断编码】Z34.900x001与诊断编码D56.100存在联合诊断编码O99.004',#699
    '【诊断编码】Z33.x00x001与诊断编码N75.000存在联合诊断编码O99.800x319',
    '【诊断编码】Z33.x00x001与诊断编码N20.000存在联合诊断编码O99.810',
    '【诊断编码】Z33.x00x001与诊断编码L50.801存在联合诊断编码O99.709',#702
    '【诊断编码】Z33.x00x001与诊断编码K82.802存在联合诊断编码O99.616',
    '【诊断编码】Z33.x00x001与诊断编码K64.809存在联合诊断编码O22.400',
    '【诊断编码】Z33.x00x001与诊断编码I10.x03存在联合诊断编码O10.001',
    '【诊断编码】Z33.x00x001与诊断编码E87.102存在联合诊断编码O99.200x017',#706
    '【诊断编码】Z33.x00x001与诊断编码E14.900x001存在联合诊断编码O24.300x001',
    '【诊断编码】Z33.x00x001与诊断编码D69.400x002存在联合诊断编码O99.101',
    '【诊断编码】Z33.x00x001与诊断编码D56.000存在联合诊断编码O99.004',
    '【诊断编码】Z33.x00x001与诊断编码D39.100x003存在联合诊断编码O34.800x006',#710
    '【诊断编码】Z33.x00x001与诊断编码B18.000存在联合诊断编码O98.400x011',
    '【诊断编码】Z33.x00x001与诊断编码B01.900x001存在联合诊断编码O98.506',
    '【诊断编码】Z32.100与诊断编码J40.x00存在联合诊断编码O99.503',
    '【诊断编码】Z32.100与诊断编码E78.500存在联合诊断编码O99.212',#714
    '【诊断编码】T25.300x002与诊断编码T25.300x003存在联合诊断编码T25.300',
    '【诊断编码】T25.200x002与诊断编码T25.200x003存在联合诊断编码T25.200',
    '【诊断编码】T25.100x002与诊断编码T25.100x003存在联合诊断编码T25.100',
    '【诊断编码】T23.100x002与诊断编码T23.100x003存在联合诊断编码T23.100',#718
    '【诊断编码】T23.200x002与诊断编码T23.200x003存在联合诊断编码T23.200',
    '【诊断编码】T23.300x002与诊断编码T23.300x003存在联合诊断编码T23.300',
    '【诊断编码】T22.100x002与诊断编码T22.100x003存在联合诊断编码T22.100x001',
    '【诊断编码】T22.200x002与诊断编码T22.200x003存在联合诊断编码T22.200x001',#722
    '【诊断编码】T22.300x002与诊断编码T22.300x003存在联合诊断编码T22.300x001',
    '【诊断编码】T24.100x002与诊断编码T24.100x003存在联合诊断编码T24.100x001',
    '【诊断编码】T24.200x002与诊断编码T24.200x003存在联合诊断编码T24.200x001',
    '【诊断编码】T24.300x002与诊断编码T24.300x003存在联合诊断编码T24.300x001',#726
    '【诊断编码】T26.100x003与诊断编码T26.101存在联合诊断编码T26.100x001',
    '【诊断编码】T26.400x001与诊断编码S05.300x004存在联合诊断编码T26.200x001',
    '【诊断编码】T26.900x001与诊断编码S05.300x004存在联合诊断编码T26.700x001',
    '【诊断编码】T27.000x002与诊断编码T27.000x003存在联合诊断编码T27.000',#730
    '【诊断编码】S71.100与诊断编码S72.910存在联合诊断编码S71.800x012',
    '【诊断编码】S71.100与诊断编码S72.900存在联合诊断编码S71.800x012',
    '【诊断编码】S61.901与诊断编码S63.100x011存在联合诊断编码S61.800x022',#733
    '【诊断编码】S61.901与诊断编码S63.100x001存在联合诊断编码S61.800x022',
    '【诊断编码】S61.901与诊断编码S62.311存在联合诊断编码S61.800x012',
    '【诊断编码】S61.901与诊断编码S62.301存在联合诊断编码S61.800x012',
    '【诊断编码】S61.901与诊断编码S62.210存在联合诊断编码S61.800x012',
    '【诊断编码】S61.901与诊断编码S62.200x041存在联合诊断编码S61.800x012',#738
    '【诊断编码】N70.900x003与诊断编码N70.902存在联合诊断编码N70.905',
    '【诊断编码】N20.200x001与诊断编码N13.301、N11.000x001存在联合诊断编码N13.603',
    '【诊断编码】N13.504与诊断编码N28.834存在联合诊断编码N13.605',
    '【诊断编码】N13.301与诊断编码N20.000x001存在联合诊断编码N13.201',
    '【诊断编码】N13.301与诊断编码N20.100存在联合诊断编码N13.202',#743
    '【诊断编码】N13.301与诊断编码N20.200存在联合诊断编码N13.203',
    '【诊断编码】N13.301与诊断编码N20.901存在联合诊断编码N13.204',#745
    '【诊断编码】M05.900x093与诊断编码J84.101存在联合诊断编码M05.102+J99.0*',
    '【诊断编码】K80.504与诊断编码K83.005存在联合诊断编码K80.303',
    '【诊断编码】K80.501与诊断编码K83.003存在联合诊断编码K80.300x005',#748
    '【诊断编码】K80.5与诊断编码K80.3不能同时存在，是矛盾编码',
    '【诊断编码】K80.5与诊断编码K80.4不能同时存在，是矛盾编码',
    '【诊断编码】K80.1与诊断编码K80.2不能同时存在，是矛盾编码',
    '【诊断编码】K80.0与诊断编码K80.2不能同时存在，是矛盾编码',#752
    '【诊断编码】K80.504与诊断编码K81.100,存在联合诊断编码K80.400',
    '【诊断编码】K80.503与诊断编码K81.100,存在联合诊断编码K80.400x004',
    '【诊断编码】K80.503与诊断编码K83.0,存在联合诊断编码K80.305',
    '【诊断编码】K80.501与诊断编码K83.000x007,存在联合诊断编码K80.301',#756
    '【诊断编码】M06.900与诊断编码I31.902,存在联合诊断编码M05.305+I32.8*',
    '【诊断编码】M05.900x093与诊断编码J84.101,存在联合诊断编码M05.102+J99.0*',
    '【诊断编码】K71.601与诊断编码K72.100,存在联合诊断编码K71.100x003',
    '【诊断编码】K71.601与诊断编码K72.900,存在联合诊断编码K71.101',#760
    '【诊断编码】K71.601与诊断编码K72.003,存在联合诊断编码K71.102',
    '【诊断编码】K71.901与诊断编码K72.000x014,存在联合诊断编码K71.100x012',
    '【诊断编码】K71.901与诊断编码K72.001,存在联合诊断编码K71.104',
    '【诊断编码】K70.402与诊断编码K72.903,存在联合诊断编码K70.403',
    '【诊断编码】K70.401与诊断编码K72.903,存在联合诊断编码K70.403',#765
    '【诊断编码】K70.300与诊断编码I86.800x014,存在联合诊断编码K70.300x007+I98.2*',
    '【诊断编码】K74.300与诊断编码I86.400x011,存在联合诊断编码K70.300x005+I98.3*',
    '【诊断编码】K74.300与诊断编码I85.000x001,存在联合诊断编码K70.302+I98.3*',#768
    '【诊断编码】K29.701与诊断编码K92.202,存在联合诊断编码K91.800x102',
    '【诊断编码】K26.900x001与诊断编码K92.2,存在联合诊断编码K26.000x001',#770
    '【诊断编码】K26.900x002与诊断编码K92.2,存在联合诊断编码K26.001',
    '【诊断编码】K66.000与诊断编码K56.701,存在联合诊断编码K56.501',
    '【诊断编码】K58.900与诊断编码K52.916,矛盾编码应编码到K58.000',#773
    '【诊断编码】K57.100x005与诊断编码K65.000,矛盾编码应编码到K57.002',#774
    '【诊断编码】K35.900与诊断编码K65.001,矛盾编码应编码到K35.000',#775
    '【诊断编码】K29.701与诊断编码K92.202,矛盾编码应编码到K91.800x102',#776
    '【诊断编码】K26.900x001与诊断编码K92.203,存在联合诊断编码K26.200x001',#777
    '【诊断编码】K26.900x001与诊断编码K92.203,K31.800x806存在联合诊断编码K26.200x001',#778
    '【诊断编码】K25.700与诊断编码K92.207、K27.503存在联合诊断编码K25.600',#779
    '【诊断编码】K25.900x001与诊断编码K27.503、K92.201存在联合诊断编码K25.200x001',#780
    '【诊断编码】J30.400与诊断编码J45.9,存在联合诊断编码J45.004',#781
    '【诊断编码】H33.500与诊断编码H33.304,存在联合诊断编码H33.000',#782
    '【诊断编码】H33.200与诊断编码H33.304,存在联合诊断编码H33.000',#783
    '【诊断编码】D59.500x001与诊断编码D61.900,存在联合诊断编码D59.501',#784
    '【诊断编码】D34.x00与诊断编码E05.900x001,存在联合诊断编码E05.200x004',#785
    '【诊断编码】C18.200与诊断编码C18.400,存在联合诊断编码C18.802',#786
    '【诊断编码】C18.900与诊断编码C20.x00,存在联合诊断编码C19.x01',#787
    '【诊断编码】C18.400与诊断编码C18.600,存在联合诊断编码C18.803',#788
    '【诊断编码】C18.200与诊断编码C18.400,存在联合诊断编码C18.802',#789
    '【诊断编码】C18.700与诊断编码C18.600,存在联合诊断编码C18.801',#790
    '【诊断编码】B15.9与诊断编码B15.0,不能同时存在是矛盾编码',#791
    '【诊断编码】B16.0与诊断编码B16.1,不能同时存在是矛盾编码',#792
    '【诊断编码】B16.0与诊断编码B16.9,不能同时存在是矛盾编码',#793
    '【诊断编码】B16.2与诊断编码B16.9,不能同时存在是矛盾编码',#794
    '【诊断编码】B16.2与诊断编码B16.1,不能同时存在是矛盾编码',#795
    '【诊断编码】B18.0与诊断编码B18.1,不能同时存在是矛盾编码',#796
    '【诊断编码】B19.0与诊断编码B19.9,不能同时存在是矛盾编码',#797
    '【诊断编码】B26.9与诊断编码B26.0,不能同时存在是矛盾编码',#798
    '【诊断编码】B26.9与诊断编码B26.1,不能同时存在是矛盾编码',#799
    '【诊断编码】B26.9与诊断编码B26.2,不能同时存在是矛盾编码',#800
    '【诊断编码】B26.9与诊断编码B26.3,不能同时存在是矛盾编码',#801
    '【诊断编码】B26.9与诊断编码B26.8,不能同时存在是矛盾编码',#802
    '【诊断编码】I83.903与诊断编码L97.000,存在联合编码I83.001',#803
    '【诊断编码】E10.900与诊断编码E10.0,不能同时存在是矛盾编码',#804
    '【诊断编码】E10.900与诊断编码E10.1,不能同时存在是矛盾编码',#805
    '【诊断编码】E10.900与诊断编码E10.2,不能同时存在是矛盾编码',#806
    '【诊断编码】E10.900与诊断编码E10.3,不能同时存在是矛盾编码',#807
    '【诊断编码】E10.900与诊断编码E10.4,不能同时存在是矛盾编码',#808
    '【诊断编码】E10.900与诊断编码E10.5,不能同时存在是矛盾编码',#809
    '【诊断编码】E10.900与诊断编码E10.6,不能同时存在是矛盾编码',#810
    '【诊断编码】E10.900与诊断编码E10.7,不能同时存在是矛盾编码',#811
    '【诊断编码】E10.900与诊断编码E10.8,不能同时存在是矛盾编码',#812
    '【诊断编码】E11.900与诊断编码E11.0,不能同时存在是矛盾编码',#813
    '【诊断编码】E11.900与诊断编码E11.1,不能同时存在是矛盾编码',#814
    '【诊断编码】E11.900与诊断编码E11.2,不能同时存在是矛盾编码',#815
    '【诊断编码】E11.900与诊断编码E11.3,不能同时存在是矛盾编码',#816
    '【诊断编码】E11.900与诊断编码E11.4,不能同时存在是矛盾编码',#817
    '【诊断编码】E11.900与诊断编码E11.5,不能同时存在是矛盾编码',#818
    '【诊断编码】E11.900与诊断编码E11.6,不能同时存在是矛盾编码',#819
    '【诊断编码】E11.900与诊断编码E11.7,不能同时存在是矛盾编码',#820
    '【诊断编码】E11.900与诊断编码E11.8,不能同时存在是矛盾编码',#821
    '【诊断编码】E14.900与诊断编码E14.0,不能同时存在是矛盾编码',#822
    '【诊断编码】E14.900与诊断编码E14.1,不能同时存在是矛盾编码',#823
    '【诊断编码】E14.900与诊断编码E14.2,不能同时存在是矛盾编码',#824
    '【诊断编码】E14.900与诊断编码E14.3,不能同时存在是矛盾编码',#825
    '【诊断编码】E14.900与诊断编码E14.4,不能同时存在是矛盾编码',#826
    '【诊断编码】E14.900与诊断编码E14.5,不能同时存在是矛盾编码',#827
    '【诊断编码】E14.900与诊断编码E14.6,不能同时存在是矛盾编码',#828
    '【诊断编码】E14.900与诊断编码E14.7,不能同时存在是矛盾编码',#829
    '【诊断编码】E14.900与诊断编码E14.8,不能同时存在是矛盾编码',#830
    '【诊断编码】E15与诊断编码E16.0,不能同时存在是矛盾编码',#831
    '【性别】为"女",【出院诊断】中不应出现男性化疾病诊断编码("E89.5")',
    '【性别】为"男",【出院诊断】中不应出现女性化疾病诊断编码("E89.4")',#833
    '【诊断编码】F23.000-与诊断编码F23.100-,不能同时存在是矛盾编码',#834
    '【诊断编码】F23.000x002与诊断编码F23.000x011,不能同时存在是矛盾编码',#835
    '【诊断编码】F23.100x002与诊断编码F23.100x011,不能同时存在是矛盾编码',#836
    '【诊断编码】F23.200x003与诊断编码F23.200x011,不能同时存在是矛盾编码',#837
    '【诊断编码】F23.300x004与诊断编码F23.300x011,不能同时存在是矛盾编码',#838
    '【诊断编码】F30.100与诊断编码F30.200,不能同时存在是矛盾编码',#839
    '【诊断编码】F30.100x001与诊断编码F30.200x001,不能同时存在是矛盾编码',#840
    '【诊断编码】F31.100与诊断编码F31.200,不能同时存在是矛盾编码',#841
    '【诊断编码】F31.100x001与诊断编码F31.200x001,不能同时存在是矛盾编码',#842
    '【诊断编码】F31.300x003与诊断编码F31.300x011,不能同时存在是矛盾编码',#843
    '【诊断编码】F31.300x005与诊断编码F31.300x012,不能同时存在是矛盾编码',#844
    '【诊断编码】F31.400与诊断编码F31.500,不能同时存在是矛盾编码',#845
    '【诊断编码】F31.400x001与诊断编码F31.500x001,不能同时存在是矛盾编码',#846
    '【诊断编码】F32.000x002与诊断编码F32.000x011,不能同时存在是矛盾编码',#847
    '【诊断编码】F32.100x002与诊断编码F32.100x011,不能同时存在是矛盾编码',#848
    '【诊断编码】F32.200与诊断编码F32.300,不能同时存在是矛盾编码',#849
    '【诊断编码】F33.000x002与诊断编码F33.000x011,不能同时存在是矛盾编码',#850
    '【诊断编码】F33.200与诊断编码F33.300,不能同时存在是矛盾编码',#851
    '【诊断编码】F33.200x001与诊断编码F33.300x001,不能同时存在是矛盾编码',#852
    '【诊断编码】G40.600与诊断编码G40.700,不能同时存在是矛盾编码',#853
    '【诊断编码】G43.000与诊断编码G43.100,不能同时存在是矛盾编码',#854
    '【诊断编码】G44.200x005与诊断编码G44.208,不能同时存在是矛盾编码',#855
    '【诊断编码】I05与诊断编码I34,不能同时存在是矛盾编码',#856
    '【诊断编码】I05与诊断编码I35,不能同时存在是矛盾编码',#857
    '【诊断编码】I05与诊断编码I36,不能同时存在是矛盾编码',#858
    '【诊断编码】I06与诊断编码I34,不能同时存在是矛盾编码',#859
    '【诊断编码】I06与诊断编码I35,不能同时存在是矛盾编码',#860
    '【诊断编码】I06与诊断编码I36,不能同时存在是矛盾编码',#861
    '【诊断编码】I07与诊断编码I34,不能同时存在是矛盾编码',#862
    '【诊断编码】I07与诊断编码I35,不能同时存在是矛盾编码',#863
    '【诊断编码】I07与诊断编码I36,不能同时存在是矛盾编码',#864
    '【诊断编码】I08与诊断编码I34,不能同时存在是矛盾编码',#865
    '【诊断编码】I08与诊断编码I35,不能同时存在是矛盾编码',#866
    '【诊断编码】I08与诊断编码I36,不能同时存在是矛盾编码',#867
    '【诊断编码】I09与诊断编码I34,不能同时存在是矛盾编码',#868
    '【诊断编码】I09与诊断编码I35,不能同时存在是矛盾编码',#869
    '【诊断编码】I09与诊断编码I36,不能同时存在是矛盾编码',#870
    '【诊断编码】J35.100与诊断编码J35.200,存在联合编码J35.300',#871
    '【诊断编码】K04.600与诊断编码K04.700,不能同时存在是矛盾编码',#872
    '【诊断编码】K21.000与诊断编码K21.900,不能同时存在是矛盾编码',#873
    '【诊断编码】K25.200与诊断编码K25.300,不能同时存在是矛盾编码',#874
    '【诊断编码】K25.600与诊断编码K25.700,不能同时存在是矛盾编码',#875
    '【诊断编码】K25.400与诊断编码K25.900,不能同时存在是矛盾编码',#876
    '【诊断编码】K25.000与诊断编码K25.900,不能同时存在是矛盾编码',#877
    '【诊断编码】K26.200与诊断编码K26.300,不能同时存在是矛盾编码',#878
    '【诊断编码】K26.600与诊断编码K26.700,不能同时存在是矛盾编码',#879
    '【诊断编码】K26.000与诊断编码K26.900,不能同时存在是矛盾编码',#880
    '【诊断编码】K26.400与诊断编码K26.900,不能同时存在是矛盾编码',#881
    '【诊断编码】K27.200与诊断编码K27.300,不能同时存在是矛盾编码',#882
    '【诊断编码】K27.600与诊断编码K27.700,不能同时存在是矛盾编码',#883
    '【诊断编码】K28.200与诊断编码K28.300,不能同时存在是矛盾编码',#884
    '【诊断编码】K28.000与诊断编码K28.900,不能同时存在是矛盾编码',#885
    '【诊断编码】K28.400与诊断编码K28.900,不能同时存在是矛盾编码',#886
    '【主要诊断编码】是C00-C97/D00-D48,死亡病例主要诊断可能选择错误',#887









   ]
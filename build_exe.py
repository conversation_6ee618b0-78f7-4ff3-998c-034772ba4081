#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本 - 将Python程序打包成Windows可执行文件
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, cwd=None):
    """执行命令并返回结果"""
    print(f"执行命令: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print("✓ 命令执行成功")
            if result.stdout:
                print(result.stdout)
        else:
            print(f"✗ 命令执行失败: {result.stderr}")
            return False
        return True
    except Exception as e:
        print(f"✗ 执行命令时发生错误: {e}")
        return False

def clean_build():
    """清理之前的构建文件"""
    print("清理之前的构建文件...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"删除目录: {dir_name}")
    
    # 清理spec文件
    for spec_file in Path('.').glob('*.spec'):
        os.remove(spec_file)
        print(f"删除文件: {spec_file}")

def check_dependencies():
    """检查依赖项是否安装"""
    print("检查依赖项...")
    required_packages = ['openpyxl', 'Pillow', 'pyinstaller']
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_').lower())
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装，尝试安装...")
            if not run_command(f"pip install {package}"):
                print(f"安装 {package} 失败，请手动安装")
                return False
    return True

def create_icon():
    """创建图标文件"""
    print("准备图标文件...")
    icon_files = ['icon.ico', 'logo.ico']
    
    # 检查是否有现成的ico文件
    for icon_file in icon_files:
        if os.path.exists(icon_file):
            print(f"✓ 找到图标文件: {icon_file}")
            return icon_file
    
    # 尝试从PNG转换
    png_files = ['icon.png', 'logo.png', 'logo_small.png']
    for png_file in png_files:
        if os.path.exists(png_file):
            try:
                from PIL import Image
                img = Image.open(png_file)
                # 调整为图标大小
                sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
                icon_path = 'app_icon.ico'
                img.save(icon_path, format='ICO', sizes=sizes)
                print(f"✓ 从 {png_file} 创建图标: {icon_path}")
                return icon_path
            except Exception as e:
                print(f"从 {png_file} 创建图标失败: {e}")
    
    print("⚠ 未找到合适的图标文件")
    return None

def build_exe():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    # 获取图标
    icon_path = create_icon()
    
    # 构建PyInstaller命令
    cmd_parts = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # 无控制台窗口
        "--name=医疗数据校验工具",        # 可执行文件名称
        "--add-data=config.json;.",     # 添加配置文件
        "--add-data=license_database.json;.",  # 添加授权码数据库
    ]
    
    # 添加图片资源
    image_files = ['icon.png', 'logo.png', 'logo_large.png', 'logo_small.png']
    for img_file in image_files:
        if os.path.exists(img_file):
            cmd_parts.append(f"--add-data={img_file};.")
    
    # 添加授权管理相关的Python模块
    license_modules = ['license_manager', 'license_models', 'license_ui']
    for module in license_modules:
        cmd_parts.append(f"--hidden-import={module}")
    
    # 添加安全机制需要的系统模块
    system_modules = ['platform', 'uuid', 'hashlib', 'json', 'os']
    for module in system_modules:
        cmd_parts.append(f"--hidden-import={module}")
    
    # 添加图标
    if icon_path:
        cmd_parts.append(f"--icon={icon_path}")
    
    # 添加主文件
    cmd_parts.append("main.py")
    
    # 执行打包命令
    cmd = " ".join(cmd_parts)
    
    if run_command(cmd):
        print("✓ 打包完成!")
        
        # 检查输出文件
        exe_path = os.path.join("dist", "医疗数据校验工具.exe")
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"✓ 可执行文件已生成: {exe_path}")
            print(f"✓ 文件大小: {file_size:.2f} MB")
            return True
        else:
            print("✗ 未找到生成的可执行文件")
            return False
    else:
        print("✗ 打包失败")
        return False

def copy_resources():
    """复制必要的资源文件到输出目录"""
    print("复制资源文件...")
    
    if not os.path.exists("dist"):
        print("✗ dist目录不存在")
        return False
    
    # 需要复制的文件
    resource_files = [
        'config.json',
        'README.md'
    ]
    
    for file_name in resource_files:
        if os.path.exists(file_name):
            try:
                shutil.copy2(file_name, "dist")
                print(f"✓ 复制文件: {file_name}")
            except Exception as e:
                print(f"✗ 复制文件 {file_name} 失败: {e}")
    
    # 创建使用说明
    readme_content = """# 医疗数据校验工具 使用说明

## 使用方法
1. 双击运行 "医疗数据校验工具.exe"
2. 点击"导入Excel文件"按钮选择要处理的Excel文件
3. 程序会自动进行数据校验
4. 校验结果会保存在程序所在目录下

## 注意事项
- 确保Excel文件格式正确
- 处理大文件时请耐心等待
- 如遇到问题，请查看生成的日志文件

## 技术支持
© 2025 ZhiHui Medipro
"""
    
    try:
        with open(os.path.join("dist", "使用说明.txt"), 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✓ 创建使用说明文件")
    except Exception as e:
        print(f"✗ 创建使用说明失败: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("     医疗数据校验工具 - 打包脚本")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("✗ 需要Python 3.7或更高版本")
        return False
    
    print(f"✓ Python版本: {sys.version}")
    
    # 清理旧文件
    clean_build()
    
    # 检查依赖项
    if not check_dependencies():
        print("✗ 依赖项检查失败")
        return False
    
    # 构建可执行文件
    if not build_exe():
        print("✗ 构建失败")
        return False
    
    # 复制资源文件
    copy_resources()
    
    print("\n" + "=" * 50)
    print("✓ 打包完成!")
    print("可执行文件位置: dist/医疗数据校验工具.exe")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按Enter键退出...")
        else:
            input("\n打包失败，按Enter键退出...")
    except KeyboardInterrupt:
        print("\n用户取消操作")
    except Exception as e:
        print(f"\n发生错误: {e}")
        input("按Enter键退出...") 
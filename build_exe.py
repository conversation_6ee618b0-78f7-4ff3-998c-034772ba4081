#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的打包脚本 - 将Python程序打包成Windows可执行文件
支持体积优化、安全加固和性能优化
"""
import os
import sys
import subprocess
import shutil
import hashlib
import json
import time
import zipfile
import tempfile
from pathlib import Path
from typing import List, Dict, Optional

def run_command(cmd, cwd=None):
    """执行命令并返回结果"""
    print(f"执行命令: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print("✓ 命令执行成功")
            if result.stdout:
                print(result.stdout)
        else:
            print(f"✗ 命令执行失败: {result.stderr}")
            return False
        return True
    except Exception as e:
        print(f"✗ 执行命令时发生错误: {e}")
        return False

def analyze_dependencies():
    """分析项目依赖，识别不必要的模块"""
    print("分析项目依赖...")
    
    # 核心必需模块
    essential_modules = {
        'tkinter', 'os', 'sys', 'json', 'datetime', 'hashlib', 'uuid',
        'platform', 'threading', 'time', 'pathlib', 'subprocess',
        'openpyxl', 'PIL', 'Pillow'
    }
    
    # 可能的冗余模块（PyInstaller经常包含但不需要的）
    redundant_modules = {
        'matplotlib', 'numpy', 'scipy', 'pandas', 'jupyter', 'IPython',
        'pytest', 'setuptools', 'wheel', 'pip', 'distutils',
        'unittest', 'doctest', 'pdb', 'profile', 'cProfile',
        'sqlite3', 'xml', 'html', 'http', 'urllib', 'email',
        'multiprocessing', 'concurrent', 'asyncio', 'socket',
        'ssl', 'ftplib', 'poplib', 'imaplib', 'smtplib'
    }
    
    return essential_modules, redundant_modules

def create_optimized_spec_file():
    """创建优化的PyInstaller spec文件"""
    print("创建优化的spec文件...")
    
    essential_modules, redundant_modules = analyze_dependencies()
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 数据文件收集
datas = []

# 配置文件
if os.path.exists('config.json'):
    datas += [('config.json', '.')]

# 图片资源
for img_file in ['icon.png', 'logo.png', 'logo_large.png', 'logo_small.png']:
    if os.path.exists(img_file):
        datas += [(img_file, '.')]

# 隐藏导入的模块 - 修复tkinter.constants问题
hiddenimports = [
    # 核心应用程序模块
    'condition',
    'main',

    # tkinter模块 - 完整列表，修复constants问题
    'tkinter',
    'tkinter.constants',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'tkinter.font',
    'tkinter.scrolledtext',
    'tkinter.simpledialog',
    'tkinter.colorchooser',
    'tkinter.commondialog',
    'tkinter.dnd',
    '_tkinter',

    # 确保包含所有tkinter子模块
    'tkinter.dialog',
    'tkinter.test',
    'tkinter.test.test_tkinter',
    'tkinter.test.support',
    'tkinter.test.widget_tests',

    # 必要的第三方模块
    'openpyxl',
    'openpyxl.workbook',
    'openpyxl.worksheet',
    'openpyxl.styles',
    'openpyxl.utils',

    # PIL/Pillow模块
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL._tkinter_finder',

    # 系统模块
    'json',
    'datetime',
    'os',
    'sys',
    'pathlib',
]

# 排除不需要的模块以减小体积
excludes = [
    'matplotlib', 'numpy', 'scipy', 'pandas', 'jupyter', 'IPython',
    'pytest', 'setuptools', 'wheel', 'pip', 'distutils',
    'unittest', 'doctest', 'pdb', 'profile', 'cProfile',
    'sqlite3', 'html', 'http.server', 'urllib.request',
    'multiprocessing', 'concurrent.futures', 'asyncio',
    'ftplib', 'poplib', 'imaplib', 'smtplib',
    'test', '_pytest', 'py._pytest'
]

# 二进制文件排除
binaries = []

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 移除不需要的文件
def remove_unwanted_files(a):
    """移除不需要的文件以减小体积"""
    unwanted_patterns = [
        'test', 'tests', '_test', 'testing',
        'doc', 'docs', 'documentation',
        'example', 'examples', 'demo', 'demos',
        'benchmark', 'benchmarks',
        '.pyx', '.pxd', '.c', '.cpp', '.h',
        'README', 'LICENSE', 'CHANGELOG',
        '.md', '.rst', '.txt'
    ]
    
    filtered_pure = []
    for name, path, typecode in a.pure:
        should_exclude = False
        for pattern in unwanted_patterns:
            if pattern in name.lower() or pattern in path.lower():
                should_exclude = True
                break
        if not should_exclude:
            filtered_pure.append((name, path, typecode))
    
    a.pure = filtered_pure
    return a

a = remove_unwanted_files(a)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='医疗数据校验工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,  # 不去除调试信息，避免tkinter问题
    upx=True,    # 使用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app_icon.ico' if os.path.exists('app_icon.ico') else None,
)
'''
    
    try:
        with open('医疗数据校验工具.spec', 'w', encoding='utf-8') as f:
            f.write(spec_content)
        print("✓ 创建优化的spec文件")
        return True
    except Exception as e:
        print(f"✗ 创建spec文件失败: {e}")
        return False

def compress_resources():
    """压缩资源文件"""
    print("压缩资源文件...")
    
    # 压缩图片文件
    image_files = ['icon.png', 'logo.png', 'logo_large.png', 'logo_small.png']
    for img_file in image_files:
        if os.path.exists(img_file):
            try:
                from PIL import Image
                img = Image.open(img_file)
                # 优化图片质量和大小
                optimized_path = f"optimized_{img_file}"
                img.save(optimized_path, optimize=True, quality=85)
                
                # 检查压缩效果
                original_size = os.path.getsize(img_file)
                optimized_size = os.path.getsize(optimized_path)
                if optimized_size < original_size:
                    shutil.move(optimized_path, img_file)
                    print(f"✓ 压缩图片 {img_file}: {original_size} -> {optimized_size} bytes")
                else:
                    os.remove(optimized_path)
                    print(f"⚠ 图片 {img_file} 无需压缩")
            except Exception as e:
                print(f"✗ 压缩图片 {img_file} 失败: {e}")

def optimize_python_bytecode():
    """优化Python字节码"""
    print("优化Python字节码...")
    
    # 编译所有Python文件为优化的字节码
    python_files = []
    for root, dirs, files in os.walk('.'):
        # 跳过不需要的目录
        dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', 'build', 'dist', '.venv']]
        
        for file in files:
            if file.endswith('.py') and not file.startswith('test_'):
                python_files.append(os.path.join(root, file))
    
    for py_file in python_files:
        try:
            # 编译为优化字节码
            cmd = f'python -O -m py_compile "{py_file}"'
            subprocess.run(cmd, shell=True, capture_output=True)
        except Exception as e:
            print(f"⚠ 编译 {py_file} 失败: {e}")
    
    print(f"✓ 优化了 {len(python_files)} 个Python文件")

def implement_lazy_imports():
    """实现延迟导入机制"""
    print("实现延迟导入优化...")
    
    # 创建延迟导入包装器
    lazy_import_code = '''
"""
延迟导入模块 - 减少启动时间
"""
import importlib
from typing import Any, Dict

class LazyImporter:
    """延迟导入器"""
    
    def __init__(self):
        self._modules: Dict[str, Any] = {}
    
    def import_module(self, module_name: str):
        """延迟导入模块"""
        if module_name not in self._modules:
            try:
                self._modules[module_name] = importlib.import_module(module_name)
            except ImportError as e:
                print(f"延迟导入模块 {module_name} 失败: {e}")
                return None
        return self._modules[module_name]
    
    def get_attr(self, module_name: str, attr_name: str):
        """获取模块属性"""
        module = self.import_module(module_name)
        if module:
            return getattr(module, attr_name, None)
        return None

# 全局延迟导入器实例
lazy_importer = LazyImporter()

# 常用模块的延迟导入函数
def get_openpyxl():
    return lazy_importer.import_module('openpyxl')

def get_pil():
    return lazy_importer.import_module('PIL.Image')

def get_tkinter():
    return lazy_importer.import_module('tkinter')
'''
    
    try:
        with open('lazy_imports.py', 'w', encoding='utf-8') as f:
            f.write(lazy_import_code)
        print("✓ 创建延迟导入模块")
        return True
    except Exception as e:
        print(f"✗ 创建延迟导入模块失败: {e}")
        return False

def calculate_file_hash(file_path: str) -> str:
    """计算文件的SHA256哈希值"""
    sha256_hash = hashlib.sha256()
    try:
        with open(file_path, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        return sha256_hash.hexdigest()
    except Exception as e:
        print(f"计算文件哈希失败 {file_path}: {e}")
        return ""

def create_integrity_manifest():
    """创建完整性清单文件"""
    print("创建完整性清单...")
    
    manifest = {
        "version": "1.0",
        "created": time.strftime("%Y-%m-%d %H:%M:%S"),
        "files": {}
    }
    
    # 需要验证完整性的文件
    critical_files = [
        "dist/医疗数据校验工具.exe",
        "config.json",
        "license_database.json"
    ]
    
    for file_path in critical_files:
        if os.path.exists(file_path):
            file_hash = calculate_file_hash(file_path)
            if file_hash:
                manifest["files"][file_path] = {
                    "hash": file_hash,
                    "size": os.path.getsize(file_path),
                    "modified": time.strftime("%Y-%m-%d %H:%M:%S", 
                                            time.localtime(os.path.getmtime(file_path)))
                }
    
    try:
        manifest_path = "dist/integrity.json"
        with open(manifest_path, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2, ensure_ascii=False)
        print(f"✓ 创建完整性清单: {manifest_path}")
        return True
    except Exception as e:
        print(f"✗ 创建完整性清单失败: {e}")
        return False

def encrypt_config_files():
    """加密关键配置文件"""
    print("加密关键配置文件...")
    
    # 简单的XOR加密（用于演示，实际应用中应使用更强的加密）
    def simple_encrypt(data: bytes, key: bytes) -> bytes:
        """简单XOR加密"""
        return bytes(a ^ b for a, b in zip(data, key * (len(data) // len(key) + 1)))
    
    # 加密密钥（实际应用中应该更安全地生成和存储）
    encryption_key = b"MedicalDataValidator2025"
    
    config_files = ["config.json", "license_database.json"]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                # 读取原文件
                with open(config_file, 'rb') as f:
                    original_data = f.read()
                
                # 加密数据
                encrypted_data = simple_encrypt(original_data, encryption_key)
                
                # 保存加密文件
                encrypted_file = f"{config_file}.enc"
                with open(encrypted_file, 'wb') as f:
                    f.write(encrypted_data)
                
                print(f"✓ 加密配置文件: {config_file} -> {encrypted_file}")
                
            except Exception as e:
                print(f"✗ 加密文件 {config_file} 失败: {e}")

def create_runtime_integrity_checker():
    """创建运行时完整性检查器"""
    print("创建运行时完整性检查器...")
    
    integrity_checker_code = '''
"""
运行时完整性检查器
在程序启动时验证关键文件的完整性
"""
import os
import sys
import json
import hashlib
import time

class IntegrityChecker:
    """完整性检查器"""
    
    def __init__(self, manifest_path="integrity.json"):
        self.manifest_path = manifest_path
        self.manifest = None
        
    def load_manifest(self):
        """加载完整性清单"""
        try:
            if os.path.exists(self.manifest_path):
                with open(self.manifest_path, 'r', encoding='utf-8') as f:
                    self.manifest = json.load(f)
                return True
        except Exception as e:
            print(f"加载完整性清单失败: {e}")
        return False
    
    def calculate_file_hash(self, file_path):
        """计算文件哈希"""
        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        except:
            return None
    
    def verify_file_integrity(self, file_path):
        """验证单个文件完整性"""
        if not self.manifest or file_path not in self.manifest.get("files", {}):
            return True  # 如果没有清单或文件不在清单中，跳过检查
        
        expected_hash = self.manifest["files"][file_path]["hash"]
        actual_hash = self.calculate_file_hash(file_path)
        
        return actual_hash == expected_hash
    
    def verify_all_files(self):
        """验证所有文件完整性"""
        if not self.load_manifest():
            return True  # 如果无法加载清单，跳过检查
        
        failed_files = []
        for file_path in self.manifest.get("files", {}):
            if os.path.exists(file_path):
                if not self.verify_file_integrity(file_path):
                    failed_files.append(file_path)
            else:
                failed_files.append(f"{file_path} (文件不存在)")
        
        if failed_files:
            print("检测到文件完整性问题:")
            for file_path in failed_files:
                print(f"  - {file_path}")
            return False
        
        return True
    
    def check_runtime_environment(self):
        """检查运行时环境"""
        # 检查是否在调试器中运行
        if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
            print("检测到调试器，程序将退出")
            return False
        
        # 检查是否在虚拟机中运行（简单检查）
        vm_indicators = [
            'VMware', 'VirtualBox', 'QEMU', 'Xen', 'Hyper-V'
        ]
        
        try:
            import platform
            system_info = platform.platform().upper()
            for indicator in vm_indicators:
                if indicator.upper() in system_info:
                    print(f"检测到虚拟环境: {indicator}")
                    # 在实际应用中可能需要更严格的处理
                    break
        except:
            pass
        
        return True
    
    def perform_security_check(self):
        """执行安全检查"""
        print("执行安全检查...")
        
        # 文件完整性检查
        if not self.verify_all_files():
            print("文件完整性检查失败")
            return False
        
        # 运行环境检查
        if not self.check_runtime_environment():
            print("运行环境检查失败")
            return False
        
        print("安全检查通过")
        return True

# 全局完整性检查器实例
integrity_checker = IntegrityChecker()

def verify_startup_integrity():
    """启动时验证完整性"""
    return integrity_checker.perform_security_check()
'''
    
    try:
        with open('integrity_checker.py', 'w', encoding='utf-8') as f:
            f.write(integrity_checker_code)
        print("✓ 创建运行时完整性检查器")
        return True
    except Exception as e:
        print(f"✗ 创建完整性检查器失败: {e}")
        return False

def create_secure_exit_mechanism():
    """创建安全退出机制"""
    print("创建安全退出机制...")
    
    secure_exit_code = '''
"""
安全退出机制
在检测到安全威胁时安全地退出程序
"""
import os
import sys
import time
import tempfile

class SecureExit:
    """安全退出处理器"""
    
    def __init__(self):
        self.exit_triggered = False
        self.exit_reason = ""
    
    def trigger_secure_exit(self, reason="安全检查失败"):
        """触发安全退出"""
        if self.exit_triggered:
            return
        
        self.exit_triggered = True
        self.exit_reason = reason
        
        print(f"安全退出触发: {reason}")
        
        # 清理敏感数据
        self.cleanup_sensitive_data()
        
        # 记录退出事件
        self.log_exit_event()
        
        # 延迟退出以避免被轻易绕过
        time.sleep(2)
        
        # 强制退出
        os._exit(1)
    
    def cleanup_sensitive_data(self):
        """清理敏感数据"""
        try:
            # 清理临时文件
            temp_dir = tempfile.gettempdir()
            temp_files = [f for f in os.listdir(temp_dir) if 'medical' in f.lower()]
            for temp_file in temp_files[:10]:  # 限制清理数量
                try:
                    os.remove(os.path.join(temp_dir, temp_file))
                except:
                    pass
        except:
            pass
    
    def log_exit_event(self):
        """记录退出事件"""
        try:
            log_entry = f"{time.strftime('%Y-%m-%d %H:%M:%S')} - 安全退出: {self.exit_reason}\\n"
            with open("security_exit.log", "a", encoding="utf-8") as f:
                f.write(log_entry)
        except:
            pass

# 全局安全退出处理器
secure_exit = SecureExit()

def emergency_exit(reason="未知安全威胁"):
    """紧急安全退出"""
    secure_exit.trigger_secure_exit(reason)
'''
    
    try:
        with open('secure_exit.py', 'w', encoding='utf-8') as f:
            f.write(secure_exit_code)
        print("✓ 创建安全退出机制")
        return True
    except Exception as e:
        print(f"✗ 创建安全退出机制失败: {e}")
        return False

def add_digital_signature():
    """添加数字签名（模拟）"""
    print("准备数字签名...")
    
    # 注意：实际的数字签名需要有效的代码签名证书
    # 这里只是创建一个签名信息文件作为演示
    
    signature_info = {
        "signer": "ZhiHui Medipro",
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "algorithm": "SHA256withRSA",
        "certificate_thumbprint": "DEMO_CERTIFICATE_THUMBPRINT",
        "note": "此为演示签名，实际部署需要有效的代码签名证书"
    }
    
    try:
        with open("dist/signature.json", 'w', encoding='utf-8') as f:
            json.dump(signature_info, f, indent=2, ensure_ascii=False)
        print("✓ 创建签名信息文件")
        print("⚠ 注意：实际部署需要使用有效的代码签名证书")
        return True
    except Exception as e:
        print(f"✗ 创建签名信息失败: {e}")
        return False

def clean_build():
    """清理之前的构建文件"""
    print("清理之前的构建文件...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"删除目录: {dir_name}")
    
    # 清理spec文件
    for spec_file in Path('.').glob('*.spec'):
        os.remove(spec_file)
        print(f"删除文件: {spec_file}")

def check_dependencies():
    """检查依赖项是否安装"""
    print("检查依赖项...")
    required_packages = ['openpyxl', 'Pillow', 'pyinstaller']

    for package in required_packages:
        try:
            # 特殊处理包名映射
            import_name = package
            if package == 'Pillow':
                import_name = 'PIL'
            elif package == 'pyinstaller':
                import_name = 'PyInstaller'

            __import__(import_name)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装，尝试安装...")
            if not run_command(f"pip install {package}"):
                print(f"安装 {package} 失败，请手动安装")
                return False

    # 检查tkinter是否可用
    try:
        import tkinter
        import tkinter.constants
        print("✓ tkinter 和 tkinter.constants 可用")
    except ImportError as e:
        print(f"✗ tkinter 模块问题: {e}")
        return False

    return True

def create_icon():
    """创建图标文件"""
    print("准备图标文件...")
    icon_files = ['icon.ico', 'logo.ico']
    
    # 检查是否有现成的ico文件
    for icon_file in icon_files:
        if os.path.exists(icon_file):
            print(f"✓ 找到图标文件: {icon_file}")
            return icon_file
    
    # 尝试从PNG转换
    png_files = ['icon.png', 'logo.png', 'logo_small.png']
    for png_file in png_files:
        if os.path.exists(png_file):
            try:
                from PIL import Image
                img = Image.open(png_file)
                # 调整为图标大小
                sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
                icon_path = 'app_icon.ico'
                img.save(icon_path, format='ICO', sizes=sizes)
                print(f"✓ 从 {png_file} 创建图标: {icon_path}")
                return icon_path
            except Exception as e:
                print(f"从 {png_file} 创建图标失败: {e}")
    
    print("⚠ 未找到合适的图标文件")
    return None

def build_exe_optimized():
    """使用优化的spec文件构建可执行文件"""
    print("开始优化构建可执行文件...")
    
    # 创建优化的spec文件
    if not create_optimized_spec_file():
        return False
    
    # 执行优化构建
    spec_file = "医疗数据校验工具.spec"
    cmd = f"pyinstaller {spec_file}"
    
    if run_command(cmd):
        print("✓ 优化打包完成!")
        
        # 检查输出文件
        exe_path = os.path.join("dist", "医疗数据校验工具.exe")
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"✓ 可执行文件已生成: {exe_path}")
            print(f"✓ 文件大小: {file_size:.2f} MB")
            return True
        else:
            print("✗ 未找到生成的可执行文件")
            return False
    else:
        print("✗ 优化打包失败")
        return False

def build_exe():
    """构建可执行文件（保持向后兼容）"""
    return build_exe_optimized()

def copy_resources():
    """复制必要的资源文件到输出目录"""
    print("复制资源文件...")
    
    if not os.path.exists("dist"):
        print("✗ dist目录不存在")
        return False
    
    # 需要复制的文件
    resource_files = [
        'config.json',
        'README.md'
    ]
    
    for file_name in resource_files:
        if os.path.exists(file_name):
            try:
                shutil.copy2(file_name, "dist")
                print(f"✓ 复制文件: {file_name}")
            except Exception as e:
                print(f"✗ 复制文件 {file_name} 失败: {e}")
    
    # 创建使用说明
    readme_content = """# 医疗数据校验工具 使用说明

## 使用方法
1. 双击运行 "医疗数据校验工具.exe"
2. 点击"导入Excel文件"按钮选择要处理的Excel文件
3. 程序会自动进行数据校验
4. 校验结果会保存在程序所在目录下

## 注意事项
- 确保Excel文件格式正确
- 处理大文件时请耐心等待
- 如遇到问题，请查看生成的日志文件

## 技术支持
© 2025 ZhiHui Medipro
"""
    
    try:
        with open(os.path.join("dist", "使用说明.txt"), 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✓ 创建使用说明文件")
    except Exception as e:
        print(f"✗ 创建使用说明失败: {e}")

def test_tkinter_fix():
    """测试tkinter修复是否有效"""
    print("测试tkinter修复...")
    try:
        import tkinter
        import tkinter.constants
        import tkinter.ttk
        import tkinter.messagebox
        print("✓ 所有tkinter模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ tkinter模块导入失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("     医疗数据校验工具 - 修复版打包脚本")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("✗ 需要Python 3.7或更高版本")
        return False
    
    print(f"✓ Python版本: {sys.version}")

    # 测试tkinter修复
    if not test_tkinter_fix():
        print("✗ tkinter测试失败")
        return False

    # 清理旧文件
    clean_build()

    # 检查依赖项
    if not check_dependencies():
        print("✗ 依赖项检查失败")
        return False
    
    # 体积优化步骤
    print("\n--- 执行体积优化 ---")
    
    # 分析依赖
    analyze_dependencies()
    
    # 压缩资源文件
    compress_resources()
    
    # 优化Python字节码
    optimize_python_bytecode()
    
    # 实现延迟导入
    implement_lazy_imports()
    
    # 创建图标
    create_icon()
    
    # 构建可执行文件
    print("\n--- 开始构建 ---")
    if not build_exe():
        print("✗ 构建失败")
        return False
    
    # 复制资源文件
    copy_resources()
    
    # 安全加固步骤
    print("\n--- 执行安全加固 ---")
    
    # 创建完整性清单
    create_integrity_manifest()
    
    # 加密配置文件
    encrypt_config_files()
    
    # 创建运行时完整性检查器
    create_runtime_integrity_checker()
    
    # 创建安全退出机制
    create_secure_exit_mechanism()
    
    # 添加数字签名
    add_digital_signature()
    
    # 显示优化和安全结果
    exe_path = os.path.join("dist", "医疗数据校验工具.exe")
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"\n--- 优化和安全结果 ---")
        print(f"✓ 最终文件大小: {file_size:.2f} MB")
        print(f"✓ 已应用体积优化技术:")
        print("  - 移除不必要的依赖库和模块")
        print("  - 资源文件智能压缩")
        print("  - Python字节码优化")
        print("  - 延迟导入机制")
        print("  - UPX压缩")
        print(f"✓ 已应用安全加固技术:")
        print("  - 程序完整性验证")
        print("  - 配置文件加密")
        print("  - 运行时环境检查")
        print("  - 安全退出机制")
        print("  - 数字签名准备")
    
    print("\n" + "=" * 50)
    print("✓ 优化和安全加固打包完成!")
    print("可执行文件位置: dist/医疗数据校验工具.exe")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按Enter键退出...")
        else:
            input("\n打包失败，按Enter键退出...")
    except KeyboardInterrupt:
        print("\n用户取消操作")
    except Exception as e:
        print(f"\n发生错误: {e}")
        input("按Enter键退出...") 
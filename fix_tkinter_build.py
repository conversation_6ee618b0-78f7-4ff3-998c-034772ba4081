#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复tkinter问题的简化打包脚本
专门解决 ModuleNotFoundError: No module named 'tkinter.constants' 问题
"""
import os
import sys
import subprocess
import shutil

def create_fixed_spec_file():
    """创建修复tkinter问题的spec文件"""
    print("创建修复tkinter问题的spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os

# 数据文件
datas = []
if os.path.exists('config.json'):
    datas += [('config.json', '.')]

# 修复tkinter问题的关键配置
hiddenimports = [
    # 核心应用模块
    'condition',
    
    # tkinter完整模块列表 - 这是解决问题的关键
    'tkinter',
    'tkinter.constants',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'tkinter.font',
    'tkinter.scrolledtext',
    'tkinter.simpledialog',
    'tkinter.colorchooser',
    'tkinter.commondialog',
    '_tkinter',
    
    # 其他必要模块
    'openpyxl',
    'openpyxl.workbook',
    'openpyxl.worksheet',
    'openpyxl.styles',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
]

# 排除不需要的模块
excludes = [
    'matplotlib',
    'numpy', 
    'scipy',
    'pandas',
    'jupyter',
    'pytest',
    'unittest',
    'test',
    'tests',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='医疗数据校验工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app_icon.ico' if os.path.exists('app_icon.ico') else None,
)
'''
    
    try:
        with open('医疗数据校验工具_fixed.spec', 'w', encoding='utf-8') as f:
            f.write(spec_content)
        print("✓ 创建修复版spec文件成功")
        return True
    except Exception as e:
        print(f"✗ 创建spec文件失败: {e}")
        return False

def run_command(cmd):
    """执行命令"""
    print(f"执行命令: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print("✓ 命令执行成功")
            if result.stdout:
                print(result.stdout)
            return True
        else:
            print(f"✗ 命令执行失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ 执行命令时发生错误: {e}")
        return False

def clean_build():
    """清理构建文件"""
    print("清理之前的构建文件...")
    dirs_to_clean = ['build', 'dist']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"删除目录: {dir_name}")

def check_main_file():
    """检查main.py文件"""
    if not os.path.exists('main.py'):
        print("✗ 未找到main.py文件")
        return False
    
    print("✓ 找到main.py文件")
    return True

def install_dependencies():
    """安装必要的依赖"""
    print("检查并安装依赖...")
    
    required_packages = ['pyinstaller', 'openpyxl', 'Pillow']
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_').lower())
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"安装 {package}...")
            if not run_command(f"pip install {package}"):
                print(f"✗ 安装 {package} 失败")
                return False
    
    return True

def build_with_fixed_spec():
    """使用修复版spec文件构建"""
    print("开始构建...")
    
    spec_file = "医疗数据校验工具_fixed.spec"
    cmd = f"pyinstaller {spec_file}"
    
    if run_command(cmd):
        print("✓ 构建完成!")
        
        # 检查输出文件
        exe_path = os.path.join("dist", "医疗数据校验工具.exe")
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"✓ 可执行文件已生成: {exe_path}")
            print(f"✓ 文件大小: {file_size:.2f} MB")
            return True
        else:
            print("✗ 未找到生成的可执行文件")
            return False
    else:
        print("✗ 构建失败")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("     修复tkinter问题的打包脚本")
    print("=" * 50)
    
    # 检查main.py文件
    if not check_main_file():
        return False
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    # 清理旧文件
    clean_build()
    
    # 创建修复版spec文件
    if not create_fixed_spec_file():
        return False
    
    # 构建
    if not build_with_fixed_spec():
        return False
    
    print("\n" + "=" * 50)
    print("✓ 修复版打包完成!")
    print("可执行文件位置: dist/医疗数据校验工具.exe")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按Enter键退出...")
        else:
            input("\n打包失败，按Enter键退出...")
    except KeyboardInterrupt:
        print("\n用户取消操作")
    except Exception as e:
        print(f"\n发生错误: {e}")
        input("按Enter键退出...")

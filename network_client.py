#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络授权客户端 - 处理与授权服务器的通信
实现HTTPS通信、请求签名、响应验证、SSL证书验证等功能
"""

import json
import time
import hashlib
import hmac
import base64
import ssl
import urllib.request
import urllib.parse
import urllib.error
import socket
import os
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple, Union
import logging
import threading
from dataclasses import asdict
import uuid
import platform

from license_models import (
    LicenseInfo, OnlineVerificationResult, UsageStats, LicenseUpdate,
    NetworkValidationError, LicenseStatus
)


# 通信协议版本
PROTOCOL_VERSION = "1.0"

# 支持的加密算法
SUPPORTED_ALGORITHMS = ["HMAC-SHA256", "RSA-SHA256"]

# 请求超时配置
DEFAULT_TIMEOUT = 30
MAX_TIMEOUT = 120
MIN_TIMEOUT = 5

# 重试配置
DEFAULT_MAX_RETRIES = 3
MAX_RETRIES = 10

# SSL配置
SSL_VERIFY_MODE = ssl.CERT_REQUIRED
SSL_CHECK_HOSTNAME = True


class CommunicationProtocol:
    """授权服务器通信协议定义"""
    
    # 协议版本
    VERSION = PROTOCOL_VERSION
    
    # API端点定义
    ENDPOINTS = {
        'ping': '/api/v1/ping',
        'license_verify': '/api/v1/license/verify',
        'license_revocation': '/api/v1/license/revocation',
        'usage_submit': '/api/v1/usage/submit',
        'updates_check': '/api/v1/updates/check',
        'handshake': '/api/v1/handshake'
    }
    
    # 请求方法
    METHODS = {
        'GET': 'GET',
        'POST': 'POST',
        'PUT': 'PUT',
        'DELETE': 'DELETE'
    }
    
    # 响应状态码
    STATUS_CODES = {
        'SUCCESS': 200,
        'BAD_REQUEST': 400,
        'UNAUTHORIZED': 401,
        'FORBIDDEN': 403,
        'NOT_FOUND': 404,
        'SERVER_ERROR': 500,
        'SERVICE_UNAVAILABLE': 503
    }
    
    # 错误代码
    ERROR_CODES = {
        'INVALID_SIGNATURE': 'E001',
        'EXPIRED_TIMESTAMP': 'E002',
        'INVALID_LICENSE': 'E003',
        'REVOKED_LICENSE': 'E004',
        'RATE_LIMITED': 'E005',
        'SERVER_MAINTENANCE': 'E006'
    }


class NetworkClient:
    """网络授权客户端"""
    
    def __init__(self, server_url: str, api_key: str = "", logger: logging.Logger = None):
        """初始化网络客户端
        
        Args:
            server_url: 授权服务器URL
            api_key: API密钥
            logger: 日志记录器
        """
        self.server_url = server_url.rstrip('/')
        self.api_key = api_key or self._generate_default_api_key()
        self.logger = logger or logging.getLogger(__name__)
        self.session_id = self._generate_session_id()
        self.client_id = self._generate_client_id()
        self.protocol = CommunicationProtocol()
        
        # 超时和重试配置
        self.timeout = DEFAULT_TIMEOUT
        self.max_retries = DEFAULT_MAX_RETRIES
        self.connect_timeout = 10  # 连接超时
        self.read_timeout = 30     # 读取超时
        
        # 线程锁
        self._lock = threading.RLock()
        
        # SSL配置
        self._configure_ssl_context()
        
        # 服务器信息缓存
        self.server_info = {}
        self.last_handshake = None
        
        # 请求统计
        self.request_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0
        }
        
        self.logger.info(f"网络客户端初始化完成，服务器: {self.server_url}")
        self.logger.debug(f"客户端ID: {self.client_id}, 会话ID: {self.session_id}")
    
    def _configure_ssl_context(self):
        """配置SSL上下文"""
        self.ssl_context = ssl.create_default_context()
        self.ssl_context.check_hostname = SSL_CHECK_HOSTNAME
        self.ssl_context.verify_mode = SSL_VERIFY_MODE
        
        # 设置支持的协议版本
        self.ssl_context.minimum_version = ssl.TLSVersion.TLSv1_2
        
        # 设置密码套件
        self.ssl_context.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS')
        
        self.logger.debug("SSL上下文配置完成")
    
    def _generate_default_api_key(self) -> str:
        """生成默认API密钥"""
        machine_info = f"{platform.node()}{platform.machine()}{platform.processor()}"
        api_key = hashlib.sha256(machine_info.encode('utf-8')).hexdigest()[:32]
        return api_key
    
    def _generate_client_id(self) -> str:
        """生成客户端ID"""
        client_data = f"{platform.node()}|{platform.system()}|{platform.release()}|{uuid.getnode()}"
        client_hash = hashlib.sha256(client_data.encode('utf-8')).hexdigest()
        return client_hash[:24]
    
    def verify_online_license(self, license_info: LicenseInfo) -> OnlineVerificationResult:
        """在线验证授权
        
        Args:
            license_info: 授权信息
            
        Returns:
            OnlineVerificationResult: 验证结果
        """
        start_time = time.time()
        
        try:
            self.logger.info("开始在线授权验证")
            
            # 准备验证请求数据
            request_data = {
                'license_code': license_info.license_code,
                'machine_id': license_info.machine_id,
                'user_name': license_info.user_name,
                'company_name': license_info.company_name,
                'last_verified': license_info.last_verified.isoformat() if license_info.last_verified else None,
                'app_version': '1.0',
                'timestamp': datetime.now().isoformat()
            }
            
            # 发送验证请求
            response = self._make_signed_request(
                endpoint='/api/v1/license/verify',
                method='POST',
                data=request_data
            )
            
            response_time = time.time() - start_time
            
            if response.get('success'):
                result = OnlineVerificationResult(
                    success=True,
                    license_valid=response.get('license_valid', False),
                    server_time=self._parse_datetime(response.get('server_time')),
                    new_expiry_date=self._parse_datetime(response.get('new_expiry_date')),
                    revoked=response.get('revoked', False),
                    response_time=response_time
                )
                
                self.logger.info(f"在线验证成功，授权有效: {result.license_valid}")
                return result
            else:
                error_msg = response.get('error', '未知错误')
                self.logger.warning(f"在线验证失败: {error_msg}")
                return OnlineVerificationResult(
                    success=False,
                    error_message=error_msg,
                    response_time=response_time
                )
                
        except NetworkValidationError as e:
            self.logger.error(f"网络验证错误: {str(e)}")
            return OnlineVerificationResult(
                success=False,
                error_message=str(e),
                response_time=time.time() - start_time
            )
        except Exception as e:
            self.logger.error(f"在线验证异常: {str(e)}")
            return OnlineVerificationResult(
                success=False,
                error_message=f"验证过程中发生异常: {str(e)}",
                response_time=time.time() - start_time
            )
    
    def check_license_revocation(self, license_id: str) -> bool:
        """检查授权撤销状态
        
        Args:
            license_id: 授权ID
            
        Returns:
            bool: 是否被撤销
        """
        try:
            self.logger.info(f"检查授权撤销状态: {license_id}")
            
            request_data = {
                'license_id': license_id,
                'timestamp': datetime.now().isoformat()
            }
            
            response = self._make_signed_request(
                endpoint='/api/v1/license/revocation',
                method='POST',
                data=request_data
            )
            
            if response.get('success'):
                revoked = response.get('revoked', False)
                self.logger.info(f"撤销状态检查完成: {revoked}")
                return revoked
            else:
                self.logger.warning(f"撤销状态检查失败: {response.get('error')}")
                return False
                
        except Exception as e:
            self.logger.error(f"检查撤销状态异常: {str(e)}")
            return False
    
    def submit_usage_statistics(self, stats: UsageStats) -> bool:
        """提交使用统计
        
        Args:
            stats: 使用统计数据
            
        Returns:
            bool: 提交是否成功
        """
        try:
            self.logger.debug("提交使用统计数据")
            
            # 转换统计数据为字典
            stats_data = stats.to_dict()
            stats_data['timestamp'] = datetime.now().isoformat()
            
            response = self._make_signed_request(
                endpoint='/api/v1/usage/submit',
                method='POST',
                data=stats_data
            )
            
            success = response.get('success', False)
            if success:
                self.logger.debug("使用统计提交成功")
            else:
                self.logger.warning(f"使用统计提交失败: {response.get('error')}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"提交使用统计异常: {str(e)}")
            return False
    
    def download_license_updates(self) -> List[LicenseUpdate]:
        """下载授权更新
        
        Returns:
            List[LicenseUpdate]: 更新列表
        """
        try:
            self.logger.info("下载授权更新")
            
            request_data = {
                'client_version': '1.0',
                'last_update': datetime.now().isoformat(),
                'timestamp': datetime.now().isoformat()
            }
            
            response = self._make_signed_request(
                endpoint='/api/v1/updates/check',
                method='POST',
                data=request_data
            )
            
            updates = []
            if response.get('success') and response.get('updates'):
                for update_data in response['updates']:
                    update = LicenseUpdate(
                        version=update_data.get('version', ''),
                        update_type=update_data.get('type', ''),
                        data=update_data.get('data', {}),
                        timestamp=self._parse_datetime(update_data.get('timestamp')),
                        signature=update_data.get('signature', '')
                    )
                    updates.append(update)
                
                self.logger.info(f"下载到 {len(updates)} 个更新")
            else:
                self.logger.info("没有可用更新")
            
            return updates
            
        except Exception as e:
            self.logger.error(f"下载更新异常: {str(e)}")
            return []
    
    def handshake(self) -> Tuple[bool, Dict[str, Any]]:
        """与服务器进行握手，建立安全通信
        
        Returns:
            Tuple[bool, Dict[str, Any]]: 握手状态和服务器信息
        """
        try:
            self.logger.info("开始与服务器握手")
            
            # 准备握手数据
            handshake_data = {
                'client_id': self.client_id,
                'session_id': self.session_id,
                'protocol_version': self.protocol.VERSION,
                'supported_algorithms': SUPPORTED_ALGORITHMS,
                'client_info': {
                    'platform': platform.system(),
                    'version': platform.release(),
                    'architecture': platform.machine()
                },
                'timestamp': datetime.now().isoformat()
            }
            
            response = self._make_signed_request(
                endpoint=self.protocol.ENDPOINTS['handshake'],
                method=self.protocol.METHODS['POST'],
                data=handshake_data
            )
            
            if response.get('success'):
                # 缓存服务器信息
                self.server_info = {
                    'server_version': response.get('server_version', ''),
                    'protocol_version': response.get('protocol_version', ''),
                    'supported_algorithms': response.get('supported_algorithms', []),
                    'server_time': self._parse_datetime(response.get('server_time')),
                    'session_timeout': response.get('session_timeout', 3600),
                    'rate_limits': response.get('rate_limits', {})
                }
                self.last_handshake = datetime.now()
                
                self.logger.info(f"握手成功，服务器版本: {self.server_info.get('server_version')}")
                return True, self.server_info
            else:
                error_msg = response.get('error', '握手失败')
                error_code = response.get('error_code', '')
                self.logger.warning(f"握手失败: {error_msg} (代码: {error_code})")
                return False, {'error': error_msg, 'error_code': error_code}
                
        except Exception as e:
            error_msg = f"握手异常: {str(e)}"
            self.logger.error(error_msg)
            return False, {'error': error_msg}
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试服务器连接
        
        Returns:
            Tuple[bool, str]: 连接状态和消息
        """
        try:
            self.logger.info("测试服务器连接")
            
            request_data = {
                'client_id': self.client_id,
                'session_id': self.session_id,
                'timestamp': datetime.now().isoformat()
            }
            
            response = self._make_signed_request(
                endpoint=self.protocol.ENDPOINTS['ping'],
                method=self.protocol.METHODS['POST'],
                data=request_data
            )
            
            if response.get('success'):
                server_time = response.get('server_time', '')
                server_status = response.get('status', 'unknown')
                message = f"连接成功，服务器时间: {server_time}, 状态: {server_status}"
                self.logger.info(message)
                return True, message
            else:
                error_msg = response.get('error', '连接失败')
                error_code = response.get('error_code', '')
                full_error = f"{error_msg} (代码: {error_code})" if error_code else error_msg
                self.logger.warning(f"连接测试失败: {full_error}")
                return False, full_error
                
        except Exception as e:
            error_msg = f"连接测试异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def _make_signed_request(self, endpoint: str, method: str = 'GET', 
                           data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送签名请求
        
        Args:
            endpoint: API端点
            method: HTTP方法
            data: 请求数据
            
        Returns:
            Dict[str, Any]: 响应数据
            
        Raises:
            NetworkValidationError: 网络验证错误
        """
        with self._lock:
            start_time = time.time()
            url = f"{self.server_url}{endpoint}"
            
            # 更新请求统计
            self.request_stats['total_requests'] += 1
            
            # 准备请求数据
            if data is None:
                data = {}
            
            # 添加通用字段
            request_timestamp = datetime.now().isoformat()
            data.update({
                'client_id': self.client_id,
                'session_id': self.session_id,
                'api_version': self.protocol.VERSION,
                'timestamp': request_timestamp,
                'nonce': self._generate_nonce()
            })
            
            # 生成请求签名
            signature = self._generate_signature(endpoint, method, data)
            
            # 准备请求头
            headers = {
                'Content-Type': 'application/json; charset=utf-8',
                'User-Agent': f'LicenseClient/{self.protocol.VERSION}',
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip, deflate',
                'X-API-Key': self.api_key,
                'X-Signature': signature,
                'X-Timestamp': request_timestamp,
                'X-Client-ID': self.client_id,
                'X-Session-ID': self.session_id,
                'X-Protocol-Version': self.protocol.VERSION
            }
            
            # 发送请求（带重试机制）
            last_exception = None
            for attempt in range(self.max_retries):
                try:
                    # 构建请求
                    if method.upper() == 'GET':
                        # GET请求将数据作为查询参数
                        query_string = urllib.parse.urlencode(data)
                        full_url = f"{url}?{query_string}"
                        request = urllib.request.Request(full_url, headers=headers)
                    else:
                        # POST/PUT/DELETE请求将数据作为JSON body
                        json_data = json.dumps(data, ensure_ascii=False).encode('utf-8')
                        request = urllib.request.Request(url, data=json_data, headers=headers)
                        request.get_method = lambda: method.upper()
                    
                    # 发送请求
                    try:
                        with urllib.request.urlopen(request, timeout=self.timeout, 
                                                  context=self.ssl_context) as response:
                            # 读取响应数据
                            response_data = response.read()
                            
                            # 处理压缩响应
                            if response.info().get('Content-Encoding') == 'gzip':
                                import gzip
                                response_data = gzip.decompress(response_data)
                            
                            response_text = response_data.decode('utf-8')
                            response_json = json.loads(response_text)
                            
                            # 验证响应
                            if self._verify_response(response_json, response.headers):
                                # 更新统计信息
                                response_time = time.time() - start_time
                                self._update_request_stats(True, response_time)
                                
                                self.logger.debug(f"请求成功: {method} {endpoint} ({response_time:.3f}s)")
                                return response_json
                            else:
                                raise NetworkValidationError("响应验证失败")
                    
                    except socket.timeout:
                        raise NetworkValidationError(f"请求超时 ({self.timeout}秒)")
                    
                    except socket.gaierror as e:
                        raise NetworkValidationError(f"DNS解析失败: {str(e)}")
                    
                    except ConnectionResetError:
                        raise NetworkValidationError("连接被重置")
                    
                    except ConnectionRefusedError:
                        raise NetworkValidationError("连接被拒绝")
                
                except urllib.error.HTTPError as e:
                    last_exception = e
                    error_msg = f"HTTP错误 {e.code}: {e.reason}"
                    
                    # 读取错误响应体
                    try:
                        error_body = e.read().decode('utf-8')
                        error_json = json.loads(error_body)
                        error_code = error_json.get('error_code', '')
                        error_detail = error_json.get('error', error_msg)
                        error_msg = f"{error_detail} (代码: {error_code})" if error_code else error_detail
                    except:
                        pass
                    
                    # 某些HTTP错误不需要重试
                    if e.code in [400, 401, 403, 404]:
                        self._update_request_stats(False, time.time() - start_time)
                        raise NetworkValidationError(error_msg)
                    
                    if attempt == self.max_retries - 1:
                        self._update_request_stats(False, time.time() - start_time)
                        raise NetworkValidationError(error_msg)
                    else:
                        self.logger.warning(f"HTTP错误，重试 {attempt + 1}/{self.max_retries}: {error_msg}")
                        time.sleep(self._calculate_backoff_delay(attempt))
                
                except urllib.error.URLError as e:
                    last_exception = e
                    error_msg = f"网络错误: {str(e.reason)}"
                    
                    if attempt == self.max_retries - 1:
                        self._update_request_stats(False, time.time() - start_time)
                        raise NetworkValidationError(error_msg)
                    else:
                        self.logger.warning(f"网络错误，重试 {attempt + 1}/{self.max_retries}: {error_msg}")
                        time.sleep(self._calculate_backoff_delay(attempt))
                
                except ssl.SSLError as e:
                    last_exception = e
                    error_msg = f"SSL错误: {str(e)}"
                    
                    # SSL错误通常不需要重试
                    if "certificate verify failed" in str(e).lower():
                        self._update_request_stats(False, time.time() - start_time)
                        raise NetworkValidationError(f"SSL证书验证失败: {str(e)}")
                    
                    if attempt == self.max_retries - 1:
                        self._update_request_stats(False, time.time() - start_time)
                        raise NetworkValidationError(error_msg)
                    else:
                        self.logger.warning(f"SSL错误，重试 {attempt + 1}/{self.max_retries}: {error_msg}")
                        time.sleep(self._calculate_backoff_delay(attempt))
                
                except NetworkValidationError:
                    # 重新抛出网络验证错误
                    self._update_request_stats(False, time.time() - start_time)
                    raise
                
                except Exception as e:
                    last_exception = e
                    error_msg = f"请求异常: {str(e)}"
                    
                    if attempt == self.max_retries - 1:
                        self._update_request_stats(False, time.time() - start_time)
                        raise NetworkValidationError(error_msg)
                    else:
                        self.logger.warning(f"请求异常，重试 {attempt + 1}/{self.max_retries}: {error_msg}")
                        time.sleep(self._calculate_backoff_delay(attempt))
            
            # 如果所有重试都失败，抛出最后一个异常
            self._update_request_stats(False, time.time() - start_time)
            if last_exception:
                raise NetworkValidationError(f"所有重试尝试都失败，最后错误: {str(last_exception)}")
            else:
                raise NetworkValidationError("所有重试尝试都失败")
    
    def _generate_signature(self, endpoint: str, method: str, data: Dict[str, Any]) -> str:
        """生成请求签名
        
        Args:
            endpoint: API端点
            method: HTTP方法
            data: 请求数据
            
        Returns:
            str: 签名字符串
        """
        # 创建签名字符串
        # 格式: METHOD|ENDPOINT|TIMESTAMP|DATA_HASH
        timestamp = data.get('timestamp', datetime.now().isoformat())
        
        # 对数据进行排序和序列化
        sorted_data = json.dumps(data, sort_keys=True, ensure_ascii=False)
        data_hash = hashlib.sha256(sorted_data.encode('utf-8')).hexdigest()
        
        # 构建签名原文
        sign_string = f"{method.upper()}|{endpoint}|{timestamp}|{data_hash}"
        
        # 使用HMAC-SHA256生成签名
        signature = hmac.new(
            self.api_key.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    def _generate_session_id(self) -> str:
        """生成会话ID
        
        Returns:
            str: 会话ID
        """
        import uuid
        import platform
        
        # 使用时间戳、随机数和机器信息生成唯一会话ID
        session_data = f"{time.time()}|{uuid.uuid4()}|{platform.node()}"
        session_hash = hashlib.sha256(session_data.encode('utf-8')).hexdigest()
        return session_hash[:16]
    
    def _generate_nonce(self) -> str:
        """生成随机数
        
        Returns:
            str: 随机数字符串
        """
        import secrets
        return secrets.token_hex(16)
    
    def _verify_response(self, response_data: Dict[str, Any], headers: Any) -> bool:
        """验证响应数据
        
        Args:
            response_data: 响应数据
            headers: 响应头
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 检查响应格式
            if not isinstance(response_data, dict):
                self.logger.warning("响应数据格式无效")
                return False
            
            # 检查必需字段
            if 'timestamp' not in response_data:
                self.logger.warning("响应缺少时间戳")
                return False
            
            # 验证时间戳（防止重放攻击）
            response_timestamp = self._parse_datetime(response_data['timestamp'])
            if response_timestamp:
                time_diff = abs((datetime.now() - response_timestamp).total_seconds())
                if time_diff > 300:  # 5分钟内的响应才有效
                    self.logger.warning(f"响应时间戳过期: {time_diff}秒")
                    return False
            
            # 验证响应签名（如果存在）
            response_signature = headers.get('X-Response-Signature')
            if response_signature:
                expected_signature = self._generate_response_signature(response_data)
                if response_signature != expected_signature:
                    self.logger.warning("响应签名验证失败")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"响应验证异常: {str(e)}")
            return False
    
    def _generate_response_signature(self, response_data: Dict[str, Any]) -> str:
        """生成响应签名用于验证
        
        Args:
            response_data: 响应数据
            
        Returns:
            str: 响应签名
        """
        # 对响应数据进行排序和序列化
        sorted_data = json.dumps(response_data, sort_keys=True, ensure_ascii=False)
        data_hash = hashlib.sha256(sorted_data.encode('utf-8')).hexdigest()
        
        # 使用API密钥生成签名
        signature = hmac.new(
            self.api_key.encode('utf-8'),
            data_hash.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    def _update_request_stats(self, success: bool, response_time: float):
        """更新请求统计信息
        
        Args:
            success: 请求是否成功
            response_time: 响应时间
        """
        if success:
            self.request_stats['successful_requests'] += 1
        else:
            self.request_stats['failed_requests'] += 1
        
        # 更新平均响应时间
        total_requests = self.request_stats['total_requests']
        current_avg = self.request_stats['average_response_time']
        self.request_stats['average_response_time'] = (
            (current_avg * (total_requests - 1) + response_time) / total_requests
        )
    
    def _calculate_backoff_delay(self, attempt: int) -> float:
        """计算退避延迟时间
        
        Args:
            attempt: 当前重试次数
            
        Returns:
            float: 延迟时间(秒)
        """
        # 指数退避 + 随机抖动
        base_delay = 2 ** attempt
        jitter = base_delay * 0.1 * (0.5 - hash(str(time.time())) % 100 / 100)
        return min(base_delay + jitter, 60)  # 最大延迟60秒
    
    def _parse_datetime(self, date_str: Optional[str]) -> Optional[datetime]:
        """解析日期时间字符串
        
        Args:
            date_str: 日期时间字符串
            
        Returns:
            Optional[datetime]: 解析后的日期时间
        """
        if not date_str:
            return None
        
        try:
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except ValueError:
            try:
                return datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%S')
            except ValueError:
                self.logger.warning(f"无法解析日期时间: {date_str}")
                return None
    
    def set_timeout(self, timeout: int):
        """设置请求超时时间
        
        Args:
            timeout: 超时时间(秒)
        """
        self.timeout = max(5, timeout)  # 最小5秒
        self.logger.info(f"请求超时时间设置为: {self.timeout}秒")
    
    def set_max_retries(self, max_retries: int):
        """设置最大重试次数
        
        Args:
            max_retries: 最大重试次数
        """
        self.max_retries = max(1, max_retries)  # 最少重试1次
        self.logger.info(f"最大重试次数设置为: {self.max_retries}")
    
    def configure_ssl(self, verify_ssl: bool = True, ca_file: Optional[str] = None, 
                     cert_file: Optional[str] = None, key_file: Optional[str] = None):
        """配置SSL设置
        
        Args:
            verify_ssl: 是否验证SSL证书
            ca_file: CA证书文件路径
            cert_file: 客户端证书文件路径
            key_file: 客户端私钥文件路径
        """
        if verify_ssl:
            self.ssl_context.check_hostname = True
            self.ssl_context.verify_mode = ssl.CERT_REQUIRED
            
            # 加载CA证书
            if ca_file and os.path.exists(ca_file):
                self.ssl_context.load_verify_locations(ca_file)
                self.logger.info(f"已加载CA证书: {ca_file}")
            
            # 加载客户端证书和私钥（用于双向认证）
            if cert_file and key_file:
                if os.path.exists(cert_file) and os.path.exists(key_file):
                    self.ssl_context.load_cert_chain(cert_file, key_file)
                    self.logger.info(f"已加载客户端证书: {cert_file}")
                else:
                    self.logger.warning("客户端证书或私钥文件不存在")
        else:
            self.ssl_context.check_hostname = False
            self.ssl_context.verify_mode = ssl.CERT_NONE
            self.logger.warning("SSL证书验证已禁用，这可能存在安全风险")
        
        self.logger.info(f"SSL配置更新: 验证={verify_ssl}, CA文件={ca_file}")


class NetworkClientPool:
    """网络客户端连接池"""
    
    def __init__(self, server_url: str, api_key: str = "", pool_size: int = 3, 
                 logger: logging.Logger = None):
        """初始化连接池
        
        Args:
            server_url: 服务器URL
            api_key: API密钥
            pool_size: 连接池大小
            logger: 日志记录器
        """
        self.server_url = server_url
        self.api_key = api_key
        self.pool_size = pool_size
        self.logger = logger or logging.getLogger(__name__)
        self._pool = []
        self._lock = threading.RLock()
        
        # 初始化连接池
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        with self._lock:
            for i in range(self.pool_size):
                client = NetworkClient(self.server_url, self.api_key, self.logger)
                self._pool.append(client)
            
            self.logger.info(f"网络客户端连接池初始化完成，大小: {self.pool_size}")
    
    def get_client(self) -> NetworkClient:
        """获取客户端连接
        
        Returns:
            NetworkClient: 网络客户端
        """
        with self._lock:
            if self._pool:
                return self._pool.pop()
            else:
                # 如果池为空，创建新的客户端
                return NetworkClient(self.server_url, self.api_key, self.logger)
    
    def return_client(self, client: NetworkClient):
        """归还客户端连接
        
        Args:
            client: 网络客户端
        """
        with self._lock:
            if len(self._pool) < self.pool_size:
                self._pool.append(client)
    
    def cleanup(self):
        """清理连接池"""
        with self._lock:
            self._pool.clear()
            self.logger.info("网络客户端连接池已清理")


# 全局网络客户端实例
_global_client = None
_global_pool = None


def get_network_client(server_url: str = None, api_key: str = "", 
                      logger: logging.Logger = None) -> NetworkClient:
    """获取全局网络客户端实例
    
    Args:
        server_url: 服务器URL
        api_key: API密钥
        logger: 日志记录器
        
    Returns:
        NetworkClient: 网络客户端实例
    """
    global _global_client
    
    if _global_client is None and server_url:
        _global_client = NetworkClient(server_url, api_key, logger)
    
    return _global_client


def get_network_pool(server_url: str = None, api_key: str = "", 
                    pool_size: int = 3, logger: logging.Logger = None) -> NetworkClientPool:
    """获取全局网络客户端连接池
    
    Args:
        server_url: 服务器URL
        api_key: API密钥
        pool_size: 连接池大小
        logger: 日志记录器
        
    Returns:
        NetworkClientPool: 网络客户端连接池
    """
    global _global_pool
    
    if _global_pool is None and server_url:
        _global_pool = NetworkClientPool(server_url, api_key, pool_size, logger)
    
    return _global_pool
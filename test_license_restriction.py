#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试授权限制功能
"""
import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from license_manager import LicenseManager
from license_restriction import init_license_restriction, require_license, require_trial_or_license, limit_trial_usage
from license_models import LicenseStatus

class TestApp:
    def __init__(self, root):
        self.root = root
        self.root.title("授权限制测试")
        self.root.geometry("400x300")
        
        # 初始化授权管理器
        try:
            self.license_manager = LicenseManager()
            self.license_restriction = init_license_restriction(self.license_manager)
            print("授权管理器初始化成功")
        except Exception as e:
            print(f"授权管理器初始化失败: {e}")
            self.license_manager = None
            self.license_restriction = None
        
        self.create_widgets()
        self.update_status()
    
    def create_widgets(self):
        """创建测试界面"""
        # 状态显示
        self.status_label = tk.Label(self.root, text="状态检查中...", font=("Arial", 12))
        self.status_label.pack(pady=10)
        
        # 功能按钮
        tk.Button(self.root, text="需要正式授权的功能", 
                 command=self.licensed_function, width=20, height=2).pack(pady=5)
        
        tk.Button(self.root, text="需要试用期或授权的功能", 
                 command=self.trial_or_licensed_function, width=20, height=2).pack(pady=5)
        
        tk.Button(self.root, text="限制使用次数的功能", 
                 command=self.limited_function, width=20, height=2).pack(pady=5)
        
        tk.Button(self.root, text="普通功能（无限制）", 
                 command=self.normal_function, width=20, height=2).pack(pady=5)
        
        # 授权管理按钮
        tk.Button(self.root, text="输入授权码", 
                 command=self.show_license_input, width=20, height=1).pack(pady=10)
        
        # 刷新状态按钮
        tk.Button(self.root, text="刷新状态", 
                 command=self.update_status, width=20, height=1).pack(pady=5)
    
    @require_license
    def licensed_function(self):
        """需要正式授权才能使用的功能"""
        messagebox.showinfo("成功", "您已获得正式授权，可以使用此功能！")
    
    @require_trial_or_license
    def trial_or_licensed_function(self):
        """需要试用期或正式授权才能使用的功能"""
        messagebox.showinfo("成功", "您在试用期内或已获得正式授权，可以使用此功能！")
    
    @limit_trial_usage(max_uses=3)
    def limited_function(self):
        """限制使用次数的功能"""
        messagebox.showinfo("成功", "功能执行成功！（试用版限制3次使用）")
    
    def normal_function(self):
        """普通功能，无授权限制"""
        messagebox.showinfo("成功", "这是普通功能，无需授权即可使用！")
    
    def show_license_input(self):
        """显示授权码输入对话框"""
        if not self.license_manager:
            messagebox.showerror("错误", "授权管理器未初始化")
            return
        
        from license_ui import show_license_input
        license_code = show_license_input(self.root)
        if license_code:
            success, message = self.license_manager.activate_license(license_code)
            if success:
                messagebox.showinfo("成功", f"授权激活成功！\n{message}")
                self.update_status()
            else:
                messagebox.showerror("失败", f"授权激活失败：\n{message}")
    
    def update_status(self):
        """更新授权状态显示"""
        if not self.license_manager:
            self.status_label.config(text="状态: 授权管理器未初始化", fg="red")
            return
        
        try:
            status, message = self.license_manager.validate_license()
            
            status_text = {
                LicenseStatus.VALID: "状态: 已正式授权 ✓",
                LicenseStatus.TRIAL: "状态: 试用期 ⏰",
                LicenseStatus.EXPIRED: "状态: 授权已过期 ✗",
                LicenseStatus.TRIAL_EXPIRED: "状态: 试用期已过期 ✗",
                LicenseStatus.INVALID: "状态: 未授权 ⚠"
            }.get(status, "状态: 未知")
            
            color = {
                LicenseStatus.VALID: "green",
                LicenseStatus.TRIAL: "blue",
                LicenseStatus.EXPIRED: "red",
                LicenseStatus.TRIAL_EXPIRED: "red",
                LicenseStatus.INVALID: "orange"
            }.get(status, "black")
            
            # 添加详细信息
            if status == LicenseStatus.TRIAL:
                trial_status = self.license_manager.check_trial_period()
                status_text += f" (剩余{trial_status.days_remaining}天)"
            elif status == LicenseStatus.VALID:
                license_info = self.license_manager.get_license_info()
                if license_info.expiry_date:
                    days_left = license_info.days_until_expiry()
                    status_text += f" (剩余{days_left}天)"
            
            self.status_label.config(text=status_text, fg=color)
            
        except Exception as e:
            self.status_label.config(text=f"状态检查失败: {str(e)}", fg="red")

def main():
    """主函数"""
    print("启动授权限制测试程序...")
    
    root = tk.Tk()
    app = TestApp(root)
    
    print("测试程序已启动，可以测试以下功能：")
    print("1. 需要正式授权的功能 - 只有正式授权才能使用")
    print("2. 需要试用期或授权的功能 - 试用期内或正式授权都可以使用")
    print("3. 限制使用次数的功能 - 试用版限制3次使用")
    print("4. 普通功能 - 无需授权")
    print("5. 输入授权码 - 激活正式授权")
    
    root.mainloop()

if __name__ == "__main__":
    main()
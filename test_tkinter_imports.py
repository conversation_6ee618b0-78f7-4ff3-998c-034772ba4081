#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试tkinter模块导入 - 验证PyInstaller打包时包含所有必要的tkinter模块
"""
import sys

def test_tkinter_imports():
    """测试所有tkinter相关模块是否可以正常导入"""
    print("测试tkinter模块导入...")
    
    # 需要测试的tkinter模块列表
    tkinter_modules = [
        'tkinter',
        'tkinter.constants',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.font',
        'tkinter.scrolledtext',
        'tkinter.simpledialog',
        'tkinter.colorchooser',
        'tkinter.commondialog',
        'tkinter.dnd'
    ]
    
    success_count = 0
    failed_modules = []
    
    for module_name in tkinter_modules:
        try:
            __import__(module_name)
            print(f"✓ {module_name} 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"✗ {module_name} 导入失败: {e}")
            failed_modules.append(module_name)
        except Exception as e:
            print(f"⚠ {module_name} 导入异常: {e}")
            failed_modules.append(module_name)
    
    print(f"\n导入结果: {success_count}/{len(tkinter_modules)} 成功")
    
    if failed_modules:
        print("失败的模块:")
        for module in failed_modules:
            print(f"  - {module}")
        return False
    else:
        print("✓ 所有tkinter模块导入成功!")
        return True

def test_basic_tkinter_functionality():
    """测试基本的tkinter功能"""
    print("\n测试基本tkinter功能...")
    
    try:
        import tkinter as tk
        
        # 创建根窗口（不显示）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 测试基本组件创建
        label = tk.Label(root, text="测试标签")
        button = tk.Button(root, text="测试按钮")
        entry = tk.Entry(root)
        
        # 测试ttk组件
        import tkinter.ttk as ttk
        ttk_button = ttk.Button(root, text="TTK按钮")
        ttk_label = ttk.Label(root, text="TTK标签")
        
        # 清理
        root.destroy()
        
        print("✓ 基本tkinter功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 基本tkinter功能测试失败: {e}")
        return False

def test_build_script_tkinter_config():
    """测试构建脚本中的tkinter配置"""
    print("\n测试构建脚本tkinter配置...")
    
    import os
    
    try:
        from build_exe import create_optimized_spec_file
        
        # 创建spec文件
        result = create_optimized_spec_file()
        
        if not result:
            print("✗ spec文件创建失败")
            return False
        
        # 读取生成的spec文件
        spec_file = "医疗数据校验工具.spec"
        if not os.path.exists(spec_file):
            print("✗ spec文件不存在")
            return False
        
        with open(spec_file, 'r', encoding='utf-8') as f:
            spec_content = f.read()
        
        # 检查是否包含必要的tkinter模块
        required_tkinter_modules = [
            'tkinter',
            'tkinter.constants',
            'tkinter.ttk',
            'tkinter.messagebox',
            'tkinter.filedialog'
        ]
        
        missing_modules = []
        for module in required_tkinter_modules:
            if f"'{module}'" not in spec_content:
                missing_modules.append(module)
        
        if missing_modules:
            print("✗ spec文件中缺少以下tkinter模块:")
            for module in missing_modules:
                print(f"  - {module}")
            return False
        
        print("✓ 构建脚本tkinter配置正确")
        return True
        
    except Exception as e:
        print(f"✗ 构建脚本tkinter配置测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        import os
        if os.path.exists("医疗数据校验工具.spec"):
            os.remove("医疗数据校验工具.spec")

def main():
    """运行所有测试"""
    print("=" * 60)
    print("     tkinter模块导入和打包配置测试")
    print("=" * 60)
    
    tests = [
        test_tkinter_imports,
        test_basic_tkinter_functionality,
        test_build_script_tkinter_config
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ 测试 {test_func.__name__} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    if passed == total:
        print("✓ 所有tkinter相关测试通过!")
        print("✓ PyInstaller打包应该能正确处理tkinter依赖")
    else:
        print(f"✗ {total - passed} 个测试失败")
        print("⚠ 可能需要进一步调整PyInstaller配置")
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    import os
    success = main()
    sys.exit(0 if success else 1)
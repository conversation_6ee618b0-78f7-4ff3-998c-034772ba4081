
"""
安全退出机制
在检测到安全威胁时安全地退出程序
"""
import os
import sys
import time
import tempfile

class SecureExit:
    """安全退出处理器"""
    
    def __init__(self):
        self.exit_triggered = False
        self.exit_reason = ""
    
    def trigger_secure_exit(self, reason="安全检查失败"):
        """触发安全退出"""
        if self.exit_triggered:
            return
        
        self.exit_triggered = True
        self.exit_reason = reason
        
        print(f"安全退出触发: {reason}")
        
        # 清理敏感数据
        self.cleanup_sensitive_data()
        
        # 记录退出事件
        self.log_exit_event()
        
        # 延迟退出以避免被轻易绕过
        time.sleep(2)
        
        # 强制退出
        os._exit(1)
    
    def cleanup_sensitive_data(self):
        """清理敏感数据"""
        try:
            # 清理临时文件
            temp_dir = tempfile.gettempdir()
            temp_files = [f for f in os.listdir(temp_dir) if 'medical' in f.lower()]
            for temp_file in temp_files[:10]:  # 限制清理数量
                try:
                    os.remove(os.path.join(temp_dir, temp_file))
                except:
                    pass
        except:
            pass
    
    def log_exit_event(self):
        """记录退出事件"""
        try:
            log_entry = f"{time.strftime('%Y-%m-%d %H:%M:%S')} - 安全退出: {self.exit_reason}\n"
            with open("security_exit.log", "a", encoding="utf-8") as f:
                f.write(log_entry)
        except:
            pass

# 全局安全退出处理器
secure_exit = SecureExit()

def emergency_exit(reason="未知安全威胁"):
    """紧急安全退出"""
    secure_exit.trigger_secure_exit(reason)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试联系信息更新
"""

import tkinter as tk
from registration_window import show_registration_window

def test_contact_info():
    """测试联系信息显示"""
    print("=" * 50)
    print("测试注册窗口中的联系信息")
    print("=" * 50)
    
    print("启动注册窗口...")
    print("请检查底部的联系信息是否包含:")
    print("- 公司：智汇医疗有限责任公司 (ZhiHui Medipro)")
    print("- 电话：15903143106")
    print("- 邮箱：<EMAIL>")
    print("- 微信：15903143106")
    
    try:
        root = tk.Tk()
        root.withdraw()
        
        # 显示注册窗口
        result, license_code = show_registration_window(
            parent=None,
            license_manager=None,
            title="测试联系信息",
            trial_expired=False
        )
        
        print(f"\n窗口关闭，结果: {result}")
        root.destroy()
        
        print("✓ 联系信息测试完成")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")

if __name__ == "__main__":
    test_contact_info()
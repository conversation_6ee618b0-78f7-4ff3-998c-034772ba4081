#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线授权缓存测试模块
测试离线授权缓存机制的各项功能
"""

import os
import sys
import time
import json
import logging
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from performance_optimizer import OfflineLicenseCache
from license_models import LicenseInfo, LicenseType

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_offline_cache_basic():
    """测试离线缓存基本功能"""
    print("\n=== 测试离线缓存基本功能 ===")
    
    # 创建缓存实例
    cache = OfflineLicenseCache(
        cache_dir=".test_cache",
        max_age_hours=24,
        max_offline_days=7
    )
    
    # 创建测试授权信息
    license_info = LicenseInfo()
    license_info.license_code = "TEST-LICENSE-CODE-123"
    license_info.machine_id = "test-machine-001"
    license_info.license_type = LicenseType.STANDARD
    license_info.expiry_date = datetime.now() + timedelta(days=365)
    license_info.user_name = "Test User"
    license_info.company_name = "Test Company"
    
    # 测试存储验证结果
    verification_result = {
        'success': True,
        'license_valid': True,
        'server_time': datetime.now().isoformat(),
        'message': 'License verification successful'
    }
    
    print("存储授权验证结果...")
    cache.store_license_verification(license_info, verification_result)
    
    # 测试获取缓存的验证结果
    print("获取缓存的验证结果...")
    cached_info, cached_result, is_valid = cache.get_cached_license_verification(
        license_info.license_code, 
        license_info.machine_id
    )
    
    if is_valid:
        print("✓ 缓存验证结果获取成功")
        print(f"  - 授权码: {cached_info.get('license_code', 'N/A')}")
        print(f"  - 验证结果: {cached_result.get('license_valid', False)}")
    else:
        print("✗ 缓存验证结果获取失败")
    
    # 测试离线工作能力检查
    print("\n测试离线工作能力...")
    can_work = cache.can_work_offline(license_info.license_code, license_info.machine_id)
    print(f"可以离线工作: {can_work}")
    
    # 测试离线宽限期
    grace_period = cache.get_offline_grace_period(license_info.license_code, license_info.machine_id)
    print(f"离线宽限期剩余天数: {grace_period}")
    
    # 获取缓存统计
    stats = cache.get_cache_stats()
    print(f"\n缓存统计信息:")
    for key, value in stats.items():
        print(f"  - {key}: {value}")
    
    # 清理测试缓存
    cache.clear_all()
    print("\n测试缓存已清理")

def test_cache_encryption():
    """测试缓存加密功能"""
    print("\n=== 测试缓存加密功能 ===")
    
    cache = OfflineLicenseCache(cache_dir=".test_cache_encrypt")
    
    # 测试加密和解密
    test_data = b"This is test data for encryption"
    print(f"原始数据: {test_data}")
    
    encrypted = cache._encrypt_data(test_data)
    print(f"加密后数据长度: {len(encrypted)} bytes")
    
    decrypted = cache._decrypt_data(encrypted)
    print(f"解密后数据: {decrypted}")
    
    if test_data == decrypted:
        print("✓ 加密/解密测试通过")
    else:
        print("✗ 加密/解密测试失败")
    
    # 测试完整性验证
    test_dict = {
        'license_code': 'TEST-123',
        'timestamp': datetime.now().isoformat(),
        'data': {'key': 'value'}
    }
    
    hash1 = cache._calculate_integrity_hash(test_dict)
    hash2 = cache._calculate_integrity_hash(test_dict)
    
    if hash1 == hash2:
        print("✓ 完整性哈希计算一致")
    else:
        print("✗ 完整性哈希计算不一致")
    
    # 修改数据后重新计算哈希
    test_dict['data']['key'] = 'modified_value'
    hash3 = cache._calculate_integrity_hash(test_dict)
    
    if hash1 != hash3:
        print("✓ 数据修改后哈希值正确变化")
    else:
        print("✗ 数据修改后哈希值未变化")
    
    cache.clear_all()

def test_cache_expiration():
    """测试缓存过期功能"""
    print("\n=== 测试缓存过期功能 ===")
    
    # 创建短期缓存（1秒过期）
    cache = OfflineLicenseCache(
        cache_dir=".test_cache_expire",
        max_age_hours=1/3600,  # 1秒
        max_offline_days=1
    )
    
    license_info = LicenseInfo()
    license_info.license_code = "EXPIRE-TEST-123"
    license_info.machine_id = "expire-test-machine"
    
    verification_result = {'success': True, 'test': 'expiration'}
    
    # 存储数据
    print("存储测试数据...")
    cache.store_license_verification(license_info, verification_result)
    
    # 立即获取（应该成功）
    cached_info, cached_result, is_valid = cache.get_cached_license_verification(
        license_info.license_code, license_info.machine_id
    )
    
    if is_valid:
        print("✓ 立即获取缓存成功")
    else:
        print("✗ 立即获取缓存失败")
    
    # 等待过期
    print("等待缓存过期...")
    time.sleep(2)
    
    # 再次获取（应该失败）
    cached_info, cached_result, is_valid = cache.get_cached_license_verification(
        license_info.license_code, license_info.machine_id
    )
    
    if not is_valid:
        print("✓ 过期缓存正确失效")
    else:
        print("✗ 过期缓存未正确失效")
    
    cache.clear_all()

def test_cache_integrity():
    """测试缓存完整性验证"""
    print("\n=== 测试缓存完整性验证 ===")
    
    cache = OfflineLicenseCache(cache_dir=".test_cache_integrity")
    
    license_info = LicenseInfo()
    license_info.license_code = "INTEGRITY-TEST-123"
    license_info.machine_id = "integrity-test-machine"
    
    verification_result = {'success': True, 'integrity': 'test'}
    
    # 存储数据
    cache.store_license_verification(license_info, verification_result)
    
    # 正常获取
    cached_info, cached_result, is_valid = cache.get_cached_license_verification(
        license_info.license_code, license_info.machine_id
    )
    
    if is_valid:
        print("✓ 完整性验证通过")
    else:
        print("✗ 完整性验证失败")
    
    cache.clear_all()

def main():
    """主测试函数"""
    print("开始离线授权缓存测试...")
    
    try:
        test_offline_cache_basic()
        test_cache_encryption()
        test_cache_expiration()
        test_cache_integrity()
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试目录
        import shutil
        test_dirs = [".test_cache", ".test_cache_encrypt", ".test_cache_expire", ".test_cache_integrity"]
        for test_dir in test_dirs:
            if os.path.exists(test_dir):
                try:
                    shutil.rmtree(test_dir)
                    print(f"清理测试目录: {test_dir}")
                except:
                    pass

if __name__ == "__main__":
    main()
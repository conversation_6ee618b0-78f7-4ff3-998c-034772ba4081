#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试授权过期情况 - 模拟试用期过期后的注册窗口
"""

import os
import sys
import tkinter as tk
from datetime import datetime, timedelta
import json

def backup_current_license():
    """备份当前的授权文件"""
    files_to_backup = [
        'license.dat',
        'license_database.json',
        'time_jump_detector.dat'
    ]
    
    backup_files = []
    for file_name in files_to_backup:
        if os.path.exists(file_name):
            backup_name = f"{file_name}.backup"
            try:
                import shutil
                shutil.copy2(file_name, backup_name)
                backup_files.append((file_name, backup_name))
                print(f"✓ 备份文件: {file_name} -> {backup_name}")
            except Exception as e:
                print(f"✗ 备份文件失败 {file_name}: {e}")
    
    return backup_files

def restore_license_files(backup_files):
    """恢复授权文件"""
    for original_file, backup_file in backup_files:
        if os.path.exists(backup_file):
            try:
                import shutil
                shutil.copy2(backup_file, original_file)
                os.remove(backup_file)
                print(f"✓ 恢复文件: {backup_file} -> {original_file}")
            except Exception as e:
                print(f"✗ 恢复文件失败 {original_file}: {e}")

def simulate_trial_expired():
    """模拟试用期过期"""
    print("模拟试用期过期...")
    
    # 修改license.dat文件，设置过期日期为过去
    license_file = 'license.dat'
    if os.path.exists(license_file):
        try:
            # 读取当前内容
            with open(license_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 设置一个过去的日期（30天前）
            expired_date = datetime.now() - timedelta(days=30)
            expired_date_str = expired_date.strftime('%Y-%m-%d')
            
            # 创建过期的授权数据
            expired_content = f"TRIAL_EXPIRED:{expired_date_str}:EXPIRED"
            
            with open(license_file, 'w', encoding='utf-8') as f:
                f.write(expired_content)
            
            print(f"✓ 设置试用期过期日期: {expired_date_str}")
            return True
            
        except Exception as e:
            print(f"✗ 修改授权文件失败: {e}")
            return False
    else:
        # 创建一个过期的授权文件
        try:
            expired_date = datetime.now() - timedelta(days=30)
            expired_date_str = expired_date.strftime('%Y-%m-%d')
            expired_content = f"TRIAL_EXPIRED:{expired_date_str}:EXPIRED"
            
            with open(license_file, 'w', encoding='utf-8') as f:
                f.write(expired_content)
            
            print(f"✓ 创建过期的授权文件: {expired_date_str}")
            return True
            
        except Exception as e:
            print(f"✗ 创建过期授权文件失败: {e}")
            return False

def test_expired_registration_window():
    """测试过期后的注册窗口"""
    print("\n启动过期状态的注册窗口测试...")
    
    try:
        # 导入必要的模块
        from license_manager import LicenseManager
        from registration_window import show_registration_window
        
        # 创建授权管理器
        license_manager = LicenseManager()
        
        # 创建主窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        print("显示试用期过期的注册窗口...")
        print("请注意观察:")
        print("1. 窗口标题是否显示'试用期已到期'")
        print("2. 状态文本是否显示红色的过期提示")
        print("3. 说明文字是否强调必须注册")
        print("4. 是否只有'立即注册'、'联系客服'和'退出程序'按钮")
        print("5. 窗口是否设置为置顶和强制模态")
        
        # 显示过期状态的注册窗口
        result, license_code = show_registration_window(
            parent=None,
            license_manager=license_manager,
            title="软件注册 - 试用期已到期",
            trial_expired=True  # 关键参数：标记试用期已过期
        )
        
        print(f"\n注册窗口关闭，结果: {result}")
        if license_code:
            print(f"输入的注册码: {license_code}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 测试过期注册窗口失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_normal_registration_window():
    """测试正常状态的注册窗口（对比用）"""
    print("\n启动正常状态的注册窗口测试（对比用）...")
    
    try:
        from license_manager import LicenseManager
        from registration_window import show_registration_window
        
        license_manager = LicenseManager()
        
        root = tk.Tk()
        root.withdraw()
        
        print("显示正常状态的注册窗口...")
        print("请注意对比:")
        print("1. 窗口标题是否为普通的'软件注册'")
        print("2. 状态文本是否显示蓝色的试用期信息")
        print("3. 说明文字是否相对温和")
        print("4. 窗口行为是否不同")
        
        # 显示正常状态的注册窗口
        result, license_code = show_registration_window(
            parent=None,
            license_manager=license_manager,
            title="软件注册",
            trial_expired=False  # 正常状态
        )
        
        print(f"\n注册窗口关闭，结果: {result}")
        if license_code:
            print(f"输入的注册码: {license_code}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 测试正常注册窗口失败: {e}")
        return False

def show_expiry_differences():
    """显示过期和正常状态的区别"""
    print("\n" + "=" * 60)
    print("试用期过期后注册窗口的变化:")
    print("=" * 60)
    print("1. 窗口标题:")
    print("   正常: '软件注册'")
    print("   过期: '软件注册 - 试用期已到期'")
    print()
    print("2. 副标题:")
    print("   正常: '软件注册激活' (黑色)")
    print("   过期: '试用期已到期 - 请立即注册' (红色粗体)")
    print()
    print("3. 状态显示:")
    print("   正常: '当前状态: 试用期剩余 X 天' (蓝色)")
    print("   过期: '当前状态: 试用期已到期，需要注册才能继续使用' (红色)")
    print()
    print("4. 说明文字:")
    print("   正常: 温和的注册提示")
    print("   过期: 强调必须注册才能使用的紧急提示")
    print()
    print("5. 窗口行为:")
    print("   正常: 普通模态窗口")
    print("   过期: 强制置顶、强制模态，无法关闭")
    print()
    print("6. 按钮选项:")
    print("   正常: 立即注册、联系客服、退出程序")
    print("   过期: 立即注册、联系客服、退出程序 (无稍后选项)")
    print("=" * 60)

def main():
    """主函数"""
    print("=" * 60)
    print("测试试用期过期后的注册窗口")
    print("=" * 60)
    
    # 显示区别说明
    show_expiry_differences()
    
    choice = input("\n请选择测试方式:\n1. 模拟过期状态测试\n2. 对比测试(正常vs过期)\n3. 仅查看区别说明\n请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        print("\n开始模拟过期状态测试...")
        
        # 备份当前文件
        backup_files = backup_current_license()
        
        try:
            # 模拟过期
            if simulate_trial_expired():
                # 测试过期窗口
                test_expired_registration_window()
            else:
                print("✗ 模拟过期失败")
        finally:
            # 恢复文件
            restore_license_files(backup_files)
            print("✓ 已恢复原始授权文件")
    
    elif choice == "2":
        print("\n开始对比测试...")
        
        print("\n--- 第一步：显示正常状态注册窗口 ---")
        input("按Enter键继续...")
        test_normal_registration_window()
        
        print("\n--- 第二步：显示过期状态注册窗口 ---")
        input("按Enter键继续...")
        test_expired_registration_window()
        
    elif choice == "3":
        print("\n已显示区别说明，测试结束。")
    
    else:
        print("无效选择，退出。")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("现在您已经了解了试用期过期后注册窗口的样子。")
    print("=" * 60)

if __name__ == "__main__":
    main()
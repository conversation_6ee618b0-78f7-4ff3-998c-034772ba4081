#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化器 - 提供启动性能优化、缓存机制和内存管理功能
"""

import os
import sys
import time
import threading
import asyncio
import weakref
import gc
import json
import pickle
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Callable, List, Tuple
from concurrent.futures import ThreadPoolExecutor, Future
from functools import wraps, lru_cache
import logging


class StartupProfiler:
    """启动性能分析器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.checkpoints = []
        self.enabled = True
        
    def checkpoint(self, name: str, description: str = ""):
        """记录性能检查点"""
        if not self.enabled:
            return
            
        current_time = time.time()
        elapsed = current_time - self.start_time
        
        self.checkpoints.append({
            'name': name,
            'description': description,
            'timestamp': current_time,
            'elapsed': elapsed
        })
        
    def get_report(self) -> str:
        """获取性能报告"""
        if not self.checkpoints:
            return "无性能数据"
            
        report = ["启动性能报告:"]
        report.append(f"总启动时间: {self.checkpoints[-1]['elapsed']:.3f}秒")
        report.append("-" * 40)
        
        for i, checkpoint in enumerate(self.checkpoints):
            if i == 0:
                interval = checkpoint['elapsed']
            else:
                interval = checkpoint['elapsed'] - self.checkpoints[i-1]['elapsed']
                
            report.append(f"{checkpoint['name']}: {checkpoint['elapsed']:.3f}s (+{interval:.3f}s)")
            if checkpoint['description']:
                report.append(f"  {checkpoint['description']}")
                
        return "\n".join(report)
        
    def save_report(self, filepath: str = "startup_performance.log"):
        """保存性能报告到文件"""
        try:
            with open(filepath, 'a', encoding='utf-8') as f:
                f.write(f"\n[{datetime.now().isoformat()}]\n")
                f.write(self.get_report())
                f.write("\n" + "="*50 + "\n")
        except Exception as e:
            print(f"保存性能报告失败: {e}")


class LazyLoader:
    """延迟加载器 - 实现模块和组件的延迟加载"""
    
    def __init__(self):
        self._loaded_modules = {}
        self._loading_futures = {}
        self._executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="LazyLoader")
        
    def lazy_import(self, module_name: str, package: str = None):
        """延迟导入模块"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                if module_name not in self._loaded_modules:
                    try:
                        if package:
                            module = __import__(f"{package}.{module_name}", fromlist=[module_name])
                        else:
                            module = __import__(module_name)
                        self._loaded_modules[module_name] = module
                    except ImportError as e:
                        raise ImportError(f"延迟导入模块 {module_name} 失败: {e}")
                
                return func(self._loaded_modules[module_name], *args, **kwargs)
            return wrapper
        return decorator
        
    def preload_module(self, module_name: str, package: str = None) -> Future:
        """预加载模块（异步）"""
        if module_name in self._loaded_modules:
            # 已加载，返回完成的Future
            future = Future()
            future.set_result(self._loaded_modules[module_name])
            return future
            
        if module_name in self._loading_futures:
            # 正在加载，返回现有Future
            return self._loading_futures[module_name]
            
        def load_module():
            try:
                if package:
                    module = __import__(f"{package}.{module_name}", fromlist=[module_name])
                else:
                    module = __import__(module_name)
                self._loaded_modules[module_name] = module
                return module
            except ImportError as e:
                raise ImportError(f"预加载模块 {module_name} 失败: {e}")
            finally:
                # 清理loading_futures
                if module_name in self._loading_futures:
                    del self._loading_futures[module_name]
        
        future = self._executor.submit(load_module)
        self._loading_futures[module_name] = future
        return future
        
    def get_module(self, module_name: str):
        """获取已加载的模块"""
        return self._loaded_modules.get(module_name)
        
    def cleanup(self):
        """清理资源"""
        self._executor.shutdown(wait=False)


class OfflineLicenseCache:
    """离线授权缓存系统 - 提供加密存储和完整性验证的离线授权缓存"""
    
    def __init__(self, cache_dir: str = ".cache", max_age_hours: int = 168, max_offline_days: int = 30):
        """初始化离线授权缓存
        
        Args:
            cache_dir: 缓存目录
            max_age_hours: 缓存最大有效期（小时），默认7天
            max_offline_days: 最大离线天数，默认30天
        """
        self.cache_dir = cache_dir
        self.max_age = timedelta(hours=max_age_hours)
        self.max_offline_period = timedelta(days=max_offline_days)
        self._memory_cache = {}
        self._cache_lock = threading.RLock()
        self._encryption_key = self._generate_encryption_key()
        
        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)
        
        # 初始化缓存统计
        self._cache_stats = {
            'hits': 0,
            'misses': 0,
            'writes': 0,
            'errors': 0,
            'last_cleanup': datetime.now()
        }
        
        # 启动定期清理任务
        self._start_cleanup_task()
    
    def _generate_encryption_key(self) -> bytes:
        """生成加密密钥"""
        try:
            import platform
            import uuid
            
            # 基于机器信息生成一致的密钥
            machine_info = f"{platform.node()}{platform.machine()}{uuid.getnode()}"
            key_material = hashlib.sha256(machine_info.encode()).digest()
            return key_material[:32]  # AES-256需要32字节密钥
        except Exception:
            # 如果无法生成机器相关密钥，使用固定密钥
            return b'license_cache_key_2024_secure!'[:32]
    
    def _encrypt_data(self, data: bytes) -> bytes:
        """加密数据"""
        try:
            from cryptography.fernet import Fernet
            import base64
            
            # 使用Fernet对称加密
            key = base64.urlsafe_b64encode(self._encryption_key)
            fernet = Fernet(key)
            return fernet.encrypt(data)
        except ImportError:
            # 如果没有cryptography库，使用简单的XOR加密
            return self._simple_encrypt(data)
        except Exception:
            # 加密失败，返回原数据
            return data
    
    def _decrypt_data(self, encrypted_data: bytes) -> bytes:
        """解密数据"""
        try:
            from cryptography.fernet import Fernet
            import base64
            
            key = base64.urlsafe_b64encode(self._encryption_key)
            fernet = Fernet(key)
            return fernet.decrypt(encrypted_data)
        except ImportError:
            # 使用简单的XOR解密
            return self._simple_decrypt(encrypted_data)
        except Exception:
            # 解密失败，返回原数据
            return encrypted_data
    
    def _simple_encrypt(self, data: bytes) -> bytes:
        """简单XOR加密（备用方案）"""
        key = self._encryption_key
        return bytes(a ^ key[i % len(key)] for i, a in enumerate(data))
    
    def _simple_decrypt(self, data: bytes) -> bytes:
        """简单XOR解密（备用方案）"""
        return self._simple_encrypt(data)  # XOR加密和解密是相同的操作
    
    def _calculate_integrity_hash(self, data: dict) -> str:
        """计算数据完整性哈希"""
        # 将数据序列化并计算哈希
        serialized = json.dumps(data, sort_keys=True, ensure_ascii=False)
        return hashlib.sha256(serialized.encode()).hexdigest()
    
    def _verify_integrity(self, data: dict, expected_hash: str) -> bool:
        """验证数据完整性"""
        actual_hash = self._calculate_integrity_hash(data)
        return actual_hash == expected_hash
        
    def _get_cache_key(self, *args) -> str:
        """生成缓存键"""
        key_data = "|".join(str(arg) for arg in args)
        return hashlib.md5(key_data.encode()).hexdigest()
        
    def _get_cache_file(self, cache_key: str) -> str:
        """获取缓存文件路径"""
        return os.path.join(self.cache_dir, f"license_cache_{cache_key}.pkl")
        
    def get(self, cache_key: str, default=None):
        """获取缓存值"""
        with self._cache_lock:
            # 首先检查内存缓存
            if cache_key in self._memory_cache:
                data, timestamp = self._memory_cache[cache_key]
                if datetime.now() - timestamp < self.max_age:
                    return data
                else:
                    del self._memory_cache[cache_key]
            
            # 检查磁盘缓存
            cache_file = self._get_cache_file(cache_key)
            if os.path.exists(cache_file):
                try:
                    with open(cache_file, 'rb') as f:
                        cached_data = pickle.load(f)
                        
                    data, timestamp = cached_data['data'], cached_data['timestamp']
                    if datetime.now() - timestamp < self.max_age:
                        # 加载到内存缓存
                        self._memory_cache[cache_key] = (data, timestamp)
                        return data
                    else:
                        # 过期，删除文件
                        os.remove(cache_file)
                except Exception:
                    # 缓存文件损坏，删除
                    try:
                        os.remove(cache_file)
                    except:
                        pass
                        
            return default
            
    def set(self, cache_key: str, value: Any):
        """设置缓存值"""
        with self._cache_lock:
            timestamp = datetime.now()
            
            # 设置内存缓存
            self._memory_cache[cache_key] = (value, timestamp)
            
            # 设置磁盘缓存
            cache_file = self._get_cache_file(cache_key)
            try:
                cached_data = {
                    'data': value,
                    'timestamp': timestamp
                }
                with open(cache_file, 'wb') as f:
                    pickle.dump(cached_data, f)
            except Exception as e:
                # 磁盘缓存失败不影响程序运行
                pass
                
    def cached_license_validation(self, validation_func: Callable, *args, **kwargs):
        """缓存授权验证结果"""
        cache_key = self._get_cache_key("license_validation", *args, str(kwargs))
        
        # 尝试从缓存获取
        cached_result = self.get(cache_key)
        if cached_result is not None:
            return cached_result
            
        # 执行验证并缓存结果
        result = validation_func(*args, **kwargs)
        self.set(cache_key, result)
        return result
        
    def store_license_verification(self, license_info, verification_result, server_time=None):
        """存储授权验证结果
        
        Args:
            license_info: 授权信息对象
            verification_result: 验证结果
            server_time: 服务器时间（可选）
        """
        try:
            cache_key = self._get_cache_key("license_verification", 
                                          license_info.license_code, 
                                          license_info.machine_id)
            
            # 准备缓存数据
            cache_data = {
                'license_info': license_info.to_dict() if hasattr(license_info, 'to_dict') else license_info,
                'verification_result': verification_result,
                'server_time': server_time.isoformat() if server_time else None,
                'cached_at': datetime.now().isoformat()
            }
            
            # 计算完整性哈希（不包含哈希字段本身）
            integrity_hash = self._calculate_integrity_hash(cache_data)
            cache_data['integrity_hash'] = integrity_hash
            
            # 加密并存储
            self._store_encrypted_cache(cache_key, cache_data)
            self._cache_stats['writes'] += 1
            
        except Exception as e:
            self._cache_stats['errors'] += 1
            logging.getLogger(__name__).error(f"存储授权验证结果失败: {e}")
    
    def get_cached_license_verification(self, license_code, machine_id):
        """获取缓存的授权验证结果
        
        Args:
            license_code: 授权码
            machine_id: 机器ID
            
        Returns:
            tuple: (license_info, verification_result, is_valid)
        """
        try:
            cache_key = self._get_cache_key("license_verification", license_code, machine_id)
            cached_data = self._get_encrypted_cache(cache_key)
            
            if cached_data:
                # 验证完整性
                expected_hash = cached_data.get('integrity_hash', None)
                if expected_hash:
                    # 创建验证数据副本（不包含哈希字段）
                    verify_data = {k: v for k, v in cached_data.items() if k != 'integrity_hash'}
                    if self._verify_integrity(verify_data, expected_hash):
                        self._cache_stats['hits'] += 1
                        return (cached_data['license_info'], 
                               cached_data['verification_result'], 
                               True)
                    else:
                        # 完整性验证失败，删除缓存
                        self._remove_cache(cache_key)
                        self._cache_stats['errors'] += 1
                else:
                    # 没有完整性哈希，可能是旧版本缓存，直接返回
                    self._cache_stats['hits'] += 1
                    return (cached_data['license_info'], 
                           cached_data['verification_result'], 
                           True)
            
            self._cache_stats['misses'] += 1
            return None, None, False
            
        except Exception as e:
            self._cache_stats['errors'] += 1
            logging.getLogger(__name__).error(f"获取缓存授权验证失败: {e}")
            return None, None, False
    
    def _store_encrypted_cache(self, cache_key: str, data: dict):
        """存储加密缓存数据"""
        with self._cache_lock:
            timestamp = datetime.now()
            
            # 准备存储数据
            storage_data = {
                'data': data,
                'timestamp': timestamp,
                'version': '1.0'
            }
            
            # 序列化数据
            serialized_data = pickle.dumps(storage_data)
            
            # 加密数据
            encrypted_data = self._encrypt_data(serialized_data)
            
            # 存储到内存缓存
            self._memory_cache[cache_key] = (data, timestamp)
            
            # 存储到磁盘
            cache_file = self._get_cache_file(cache_key)
            try:
                with open(cache_file, 'wb') as f:
                    f.write(encrypted_data)
            except Exception as e:
                logging.getLogger(__name__).warning(f"磁盘缓存存储失败: {e}")
    
    def _get_encrypted_cache(self, cache_key: str):
        """获取加密缓存数据"""
        with self._cache_lock:
            # 首先检查内存缓存
            if cache_key in self._memory_cache:
                data, timestamp = self._memory_cache[cache_key]
                if datetime.now() - timestamp < self.max_age:
                    return data
                else:
                    del self._memory_cache[cache_key]
            
            # 检查磁盘缓存
            cache_file = self._get_cache_file(cache_key)
            if os.path.exists(cache_file):
                try:
                    # 读取加密数据
                    with open(cache_file, 'rb') as f:
                        encrypted_data = f.read()
                    
                    # 解密数据
                    decrypted_data = self._decrypt_data(encrypted_data)
                    
                    # 反序列化数据
                    storage_data = pickle.loads(decrypted_data)
                    
                    # 检查是否过期
                    if datetime.now() - storage_data['timestamp'] < self.max_age:
                        # 加载到内存缓存
                        data = storage_data['data']
                        self._memory_cache[cache_key] = (data, storage_data['timestamp'])
                        return data
                    else:
                        # 过期，删除文件
                        os.remove(cache_file)
                        
                except Exception as e:
                    # 缓存文件损坏或解密失败，删除
                    try:
                        os.remove(cache_file)
                    except:
                        pass
                    logging.getLogger(__name__).warning(f"缓存文件解密失败: {e}")
            
            return None
    
    def _remove_cache(self, cache_key: str):
        """删除指定缓存"""
        with self._cache_lock:
            # 从内存缓存删除
            if cache_key in self._memory_cache:
                del self._memory_cache[cache_key]
            
            # 从磁盘删除
            cache_file = self._get_cache_file(cache_key)
            try:
                if os.path.exists(cache_file):
                    os.remove(cache_file)
            except Exception:
                pass
    
    def can_work_offline(self, license_code: str, machine_id: str) -> bool:
        """检查是否可以离线工作
        
        Args:
            license_code: 授权码
            machine_id: 机器ID
            
        Returns:
            bool: 是否可以离线工作
        """
        try:
            license_info, verification_result, is_valid = self.get_cached_license_verification(
                license_code, machine_id)
            
            if not is_valid or not verification_result:
                return False
            
            # 检查缓存时间是否在允许的离线期间内
            cached_at_str = license_info.get('cached_at', '')
            if not cached_at_str:
                return False
                
            try:
                cached_at = datetime.fromisoformat(cached_at_str)
                offline_duration = datetime.now() - cached_at
                return offline_duration <= self.max_offline_period
            except ValueError:
                return False
            
        except Exception as e:
            logging.getLogger(__name__).error(f"检查离线工作能力失败: {e}")
            return False
    
    def get_offline_grace_period(self, license_code: str, machine_id: str) -> int:
        """获取剩余的离线宽限期（天数）
        
        Args:
            license_code: 授权码
            machine_id: 机器ID
            
        Returns:
            int: 剩余天数，-1表示无法确定
        """
        try:
            license_info, verification_result, is_valid = self.get_cached_license_verification(
                license_code, machine_id)
            
            if not is_valid:
                return -1
            
            cached_at = datetime.fromisoformat(license_info.get('cached_at', ''))
            offline_duration = datetime.now() - cached_at
            remaining_period = self.max_offline_period - offline_duration
            
            return max(0, remaining_period.days)
            
        except Exception:
            return -1
    
    def _start_cleanup_task(self):
        """启动定期清理任务"""
        def cleanup_loop():
            while True:
                try:
                    time.sleep(3600)  # 每小时清理一次
                    self.clear_expired()
                    self._cache_stats['last_cleanup'] = datetime.now()
                except Exception as e:
                    logging.getLogger(__name__).error(f"缓存清理任务失败: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True)
        cleanup_thread.start()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._cache_lock:
            return {
                'memory_cache_size': len(self._memory_cache),
                'disk_cache_files': self._count_disk_cache_files(),
                'cache_hits': self._cache_stats['hits'],
                'cache_misses': self._cache_stats['misses'],
                'cache_writes': self._cache_stats['writes'],
                'cache_errors': self._cache_stats['errors'],
                'hit_rate': self._cache_stats['hits'] / max(1, self._cache_stats['hits'] + self._cache_stats['misses']),
                'last_cleanup': self._cache_stats['last_cleanup'].isoformat(),
                'max_age_hours': self.max_age.total_seconds() / 3600,
                'max_offline_days': self.max_offline_period.days
            }
    
    def _count_disk_cache_files(self) -> int:
        """计算磁盘缓存文件数量"""
        try:
            count = 0
            for filename in os.listdir(self.cache_dir):
                if filename.startswith("license_cache_") and filename.endswith(".pkl"):
                    count += 1
            return count
        except Exception:
            return 0
    
    def clear_expired(self):
        """清理过期缓存"""
        with self._cache_lock:
            # 清理内存缓存
            expired_keys = []
            for key, (data, timestamp) in self._memory_cache.items():
                if datetime.now() - timestamp >= self.max_age:
                    expired_keys.append(key)
                    
            for key in expired_keys:
                del self._memory_cache[key]
                
            # 清理磁盘缓存
            try:
                for filename in os.listdir(self.cache_dir):
                    if filename.startswith("license_cache_") and filename.endswith(".pkl"):
                        filepath = os.path.join(self.cache_dir, filename)
                        try:
                            # 尝试读取并检查时间戳
                            with open(filepath, 'rb') as f:
                                encrypted_data = f.read()
                            
                            decrypted_data = self._decrypt_data(encrypted_data)
                            storage_data = pickle.loads(decrypted_data)
                            
                            if datetime.now() - storage_data['timestamp'] >= self.max_age:
                                os.remove(filepath)
                        except:
                            # 文件损坏或无法解密，删除
                            try:
                                os.remove(filepath)
                            except:
                                pass
            except Exception:
                pass
    
    def clear_all(self):
        """清空所有缓存"""
        with self._cache_lock:
            # 清空内存缓存
            self._memory_cache.clear()
            
            # 清空磁盘缓存
            try:
                for filename in os.listdir(self.cache_dir):
                    if filename.startswith("license_cache_") and filename.endswith(".pkl"):
                        filepath = os.path.join(self.cache_dir, filename)
                        try:
                            os.remove(filepath)
                        except:
                            pass
            except Exception:
                pass
            
            # 重置统计信息
            self._cache_stats = {
                'hits': 0,
                'misses': 0,
                'writes': 0,
                'errors': 0,
                'last_cleanup': datetime.now()
            }


class BackgroundTaskManager:
    """后台任务管理器 - 将耗时操作移到后台执行"""
    
    def __init__(self, max_workers: int = 3):
        self.executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="Background")
        self.tasks = {}
        self.completed_tasks = {}
        self._task_counter = 0
        
    def submit_task(self, task_name: str, func: Callable, *args, **kwargs) -> str:
        """提交后台任务"""
        self._task_counter += 1
        task_id = f"{task_name}_{self._task_counter}"
        
        future = self.executor.submit(func, *args, **kwargs)
        self.tasks[task_id] = {
            'name': task_name,
            'future': future,
            'submitted_at': datetime.now()
        }
        
        # 添加完成回调
        def task_completed(fut):
            if task_id in self.tasks:
                task_info = self.tasks.pop(task_id)
                self.completed_tasks[task_id] = {
                    'name': task_info['name'],
                    'submitted_at': task_info['submitted_at'],
                    'completed_at': datetime.now(),
                    'success': not fut.exception(),
                    'result': fut.result() if not fut.exception() else None,
                    'error': str(fut.exception()) if fut.exception() else None
                }
                
        future.add_done_callback(task_completed)
        return task_id
        
    def get_task_result(self, task_id: str, timeout: float = None):
        """获取任务结果"""
        if task_id in self.completed_tasks:
            task_info = self.completed_tasks[task_id]
            if task_info['success']:
                return task_info['result']
            else:
                raise Exception(f"任务失败: {task_info['error']}")
                
        if task_id in self.tasks:
            future = self.tasks[task_id]['future']
            return future.result(timeout=timeout)
            
        raise ValueError(f"任务 {task_id} 不存在")
        
    def is_task_completed(self, task_id: str) -> bool:
        """检查任务是否完成"""
        if task_id in self.completed_tasks:
            return True
        if task_id in self.tasks:
            return self.tasks[task_id]['future'].done()
        return False
        
    def get_running_tasks(self) -> List[str]:
        """获取正在运行的任务列表"""
        return [task_id for task_id, info in self.tasks.items() 
                if not info['future'].done()]
                
    def cleanup(self):
        """清理资源"""
        self.executor.shutdown(wait=False)


class MemoryOptimizer:
    """内存优化器 - 提供全面的内存管理和优化功能"""
    
    def __init__(self):
        self._object_pools = {}
        self._weak_refs = weakref.WeakSet()
        self._gc_threshold = (700, 10, 10)  # 调整垃圾回收阈值
        self._memory_monitor = MemoryMonitor()
        self._cache_manager = CacheLifecycleManager()
        self._leak_detector = MemoryLeakDetector()
        
        # 设置垃圾回收阈值
        gc.set_threshold(*self._gc_threshold)
        
        # 启动内存监控
        self._start_memory_monitoring()
        
    def get_object_pool(self, pool_name: str, factory_func: Callable, max_size: int = 100):
        """获取对象池"""
        if pool_name not in self._object_pools:
            self._object_pools[pool_name] = ObjectPool(factory_func, max_size)
        return self._object_pools[pool_name]
        
    def track_object(self, obj):
        """跟踪对象以便内存管理"""
        self._weak_refs.add(obj)
        self._leak_detector.track_object(obj)
        return obj
        
    def force_gc(self):
        """强制垃圾回收"""
        collected = gc.collect()
        return collected
        
    def smart_gc(self):
        """智能垃圾回收 - 基于内存使用情况决定是否执行GC"""
        memory_usage = self._memory_monitor.get_memory_usage()
        
        # 如果内存使用率超过80%，执行垃圾回收
        if memory_usage > 0.8:
            collected = self.force_gc()
            logging.getLogger(__name__).info(f"智能垃圾回收: 回收了 {collected} 个对象")
            return collected
        return 0
        
    def optimize_cache_sizes(self):
        """优化缓存大小"""
        memory_usage = self._memory_monitor.get_memory_usage()
        
        # 根据内存使用情况调整缓存大小
        if memory_usage > 0.9:
            # 内存紧张，减少缓存大小
            self._cache_manager.reduce_cache_sizes(0.5)
        elif memory_usage < 0.5:
            # 内存充足，可以增加缓存大小
            self._cache_manager.increase_cache_sizes(1.2)
            
    def detect_memory_leaks(self) -> List[Dict[str, Any]]:
        """检测内存泄漏"""
        return self._leak_detector.detect_leaks()
        
    def get_memory_stats(self) -> Dict[str, Any]:
        """获取内存统计信息"""
        return {
            'tracked_objects': len(self._weak_refs),
            'gc_counts': gc.get_count(),
            'gc_threshold': gc.get_threshold(),
            'object_pools': {name: pool.size() for name, pool in self._object_pools.items()},
            'memory_usage': self._memory_monitor.get_memory_usage(),
            'memory_stats': self._memory_monitor.get_detailed_stats(),
            'potential_leaks': len(self.detect_memory_leaks())
        }
        
    def optimize_memory(self):
        """执行全面的内存优化"""
        # 1. 智能垃圾回收
        collected = self.smart_gc()
        
        # 2. 清理对象池
        for pool in self._object_pools.values():
            pool.cleanup()
            
        # 3. 优化缓存大小
        self.optimize_cache_sizes()
        
        # 4. 检测内存泄漏
        leaks = self.detect_memory_leaks()
        if leaks:
            logging.getLogger(__name__).warning(f"检测到 {len(leaks)} 个潜在内存泄漏")
            
        return {
            'collected_objects': collected,
            'potential_leaks': len(leaks),
            'memory_usage_after': self._memory_monitor.get_memory_usage()
        }
        
    def _start_memory_monitoring(self):
        """启动内存监控"""
        def monitor_loop():
            while True:
                try:
                    time.sleep(60)  # 每分钟检查一次
                    self._memory_monitor.update_stats()
                    
                    # 如果内存使用率过高，执行优化
                    if self._memory_monitor.get_memory_usage() > 0.85:
                        self.optimize_memory()
                        
                except Exception as e:
                    logging.getLogger(__name__).error(f"内存监控失败: {e}")
                    
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()


class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self):
        self._stats_history = []
        self._max_history = 100
        
    def get_memory_usage(self) -> float:
        """获取当前内存使用率"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            system_memory = psutil.virtual_memory()
            return memory_info.rss / system_memory.total
        except ImportError:
            # 如果没有psutil，使用简单的估算
            import sys
            return min(sys.getsizeof(gc.get_objects()) / (1024 * 1024 * 1024), 1.0)
        except Exception:
            return 0.0
            
    def get_detailed_stats(self) -> Dict[str, Any]:
        """获取详细的内存统计信息"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            return {
                'rss': memory_info.rss,
                'vms': memory_info.vms,
                'percent': process.memory_percent(),
                'available': psutil.virtual_memory().available
            }
        except ImportError:
            return {
                'gc_objects': len(gc.get_objects()),
                'gc_counts': gc.get_count()
            }
        except Exception:
            return {}
            
    def update_stats(self):
        """更新统计信息"""
        stats = {
            'timestamp': time.time(),
            'memory_usage': self.get_memory_usage(),
            'detailed_stats': self.get_detailed_stats()
        }
        
        self._stats_history.append(stats)
        
        # 保持历史记录在限制范围内
        if len(self._stats_history) > self._max_history:
            self._stats_history.pop(0)
            
    def get_usage_trend(self) -> str:
        """获取内存使用趋势"""
        if len(self._stats_history) < 2:
            return "insufficient_data"
            
        recent_usage = [stat['memory_usage'] for stat in self._stats_history[-10:]]
        if len(recent_usage) < 2:
            return "stable"
            
        # 计算趋势
        avg_early = sum(recent_usage[:len(recent_usage)//2]) / (len(recent_usage)//2)
        avg_late = sum(recent_usage[len(recent_usage)//2:]) / (len(recent_usage) - len(recent_usage)//2)
        
        if avg_late > avg_early * 1.1:
            return "increasing"
        elif avg_late < avg_early * 0.9:
            return "decreasing"
        else:
            return "stable"


class CacheLifecycleManager:
    """缓存生命周期管理器"""
    
    def __init__(self):
        self._managed_caches = weakref.WeakSet()
        
    def register_cache(self, cache_obj):
        """注册缓存对象"""
        self._managed_caches.add(cache_obj)
        
    def reduce_cache_sizes(self, factor: float):
        """减少所有缓存的大小"""
        for cache in self._managed_caches:
            if hasattr(cache, 'max_size'):
                cache.max_size = max(10, int(cache.max_size * factor))
            if hasattr(cache, 'clear_expired'):
                cache.clear_expired()
                
    def increase_cache_sizes(self, factor: float):
        """增加所有缓存的大小"""
        for cache in self._managed_caches:
            if hasattr(cache, 'max_size'):
                cache.max_size = int(cache.max_size * factor)
                
    def clear_all_caches(self):
        """清空所有缓存"""
        for cache in self._managed_caches:
            if hasattr(cache, 'clear'):
                cache.clear()


class MemoryLeakDetector:
    """内存泄漏检测器"""
    
    def __init__(self):
        self._object_counts = {}
        self._tracking_objects = weakref.WeakSet()
        
    def track_object(self, obj):
        """跟踪对象"""
        obj_type = type(obj).__name__
        self._object_counts[obj_type] = self._object_counts.get(obj_type, 0) + 1
        self._tracking_objects.add(obj)
        
    def detect_leaks(self) -> List[Dict[str, Any]]:
        """检测潜在的内存泄漏"""
        leaks = []
        
        # 检查对象计数异常增长
        current_counts = {}
        for obj in self._tracking_objects:
            obj_type = type(obj).__name__
            current_counts[obj_type] = current_counts.get(obj_type, 0) + 1
            
        for obj_type, count in current_counts.items():
            expected_count = self._object_counts.get(obj_type, 0)
            if count > expected_count * 2:  # 如果对象数量翻倍，可能存在泄漏
                leaks.append({
                    'type': obj_type,
                    'current_count': count,
                    'expected_count': expected_count,
                    'severity': 'high' if count > expected_count * 5 else 'medium'
                })
                
        return leaks


class ObjectPool:
    """对象池 - 复用对象以减少内存分配"""
    
    def __init__(self, factory_func: Callable, max_size: int = 100):
        self.factory_func = factory_func
        self.max_size = max_size
        self._pool = []
        self._lock = threading.Lock()
        
    def get(self):
        """从池中获取对象"""
        with self._lock:
            if self._pool:
                return self._pool.pop()
            else:
                return self.factory_func()
                
    def put(self, obj):
        """将对象放回池中"""
        with self._lock:
            if len(self._pool) < self.max_size:
                # 重置对象状态（如果有reset方法）
                if hasattr(obj, 'reset'):
                    obj.reset()
                self._pool.append(obj)
                
    def size(self) -> int:
        """获取池大小"""
        with self._lock:
            return len(self._pool)
            
    def cleanup(self):
        """清理池"""
        with self._lock:
            self._pool.clear()


class SmartCache:
    """智能缓存系统 - 基于访问模式和频率的智能缓存"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self._cache = {}
        self._access_count = {}
        self._access_time = {}
        self._lock = threading.RLock()
        
    def get(self, key: str, default=None):
        """获取缓存值"""
        with self._lock:
            if key in self._cache:
                # 更新访问统计
                self._access_count[key] = self._access_count.get(key, 0) + 1
                self._access_time[key] = time.time()
                return self._cache[key]
            return default
            
    def set(self, key: str, value: Any):
        """设置缓存值"""
        with self._lock:
            # 如果缓存已满，清理最少使用的项
            if len(self._cache) >= self.max_size:
                self._evict_lru()
                
            self._cache[key] = value
            self._access_count[key] = 1
            self._access_time[key] = time.time()
            
    def _evict_lru(self):
        """清理最少使用的缓存项"""
        if not self._cache:
            return
            
        # 找到访问次数最少且最久未访问的项
        min_score = float('inf')
        evict_key = None
        current_time = time.time()
        
        for key in self._cache:
            access_count = self._access_count.get(key, 0)
            last_access = self._access_time.get(key, 0)
            time_factor = current_time - last_access
            
            # 综合考虑访问频率和时间
            score = access_count / (1 + time_factor / 3600)  # 时间因子以小时为单位
            
            if score < min_score:
                min_score = score
                evict_key = key
                
        if evict_key:
            del self._cache[evict_key]
            del self._access_count[evict_key]
            del self._access_time[evict_key]
            
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_accesses = sum(self._access_count.values())
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'total_accesses': total_accesses,
                'average_access_count': total_accesses / len(self._cache) if self._cache else 0
            }
            
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._access_count.clear()
            self._access_time.clear()


class BatchProcessor:
    """批处理器 - 将多个操作批量处理以提高效率"""
    
    def __init__(self, batch_size: int = 100, flush_interval: float = 1.0):
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self._batches = {}
        self._timers = {}
        self._lock = threading.RLock()
        
    def add_to_batch(self, batch_name: str, item: Any, processor_func: Callable):
        """添加项目到批处理队列"""
        with self._lock:
            if batch_name not in self._batches:
                self._batches[batch_name] = {
                    'items': [],
                    'processor': processor_func,
                    'created_at': time.time()
                }
                
            self._batches[batch_name]['items'].append(item)
            
            # 检查是否需要立即处理
            if len(self._batches[batch_name]['items']) >= self.batch_size:
                self._process_batch(batch_name)
            else:
                # 设置定时器
                self._set_flush_timer(batch_name)
                
    def _set_flush_timer(self, batch_name: str):
        """设置批处理刷新定时器"""
        if batch_name in self._timers:
            self._timers[batch_name].cancel()
            
        def flush_batch():
            with self._lock:
                if batch_name in self._batches and self._batches[batch_name]['items']:
                    self._process_batch(batch_name)
                    
        timer = threading.Timer(self.flush_interval, flush_batch)
        timer.start()
        self._timers[batch_name] = timer
        
    def _process_batch(self, batch_name: str):
        """处理批次"""
        if batch_name not in self._batches:
            return
            
        batch_info = self._batches.pop(batch_name)
        if batch_name in self._timers:
            self._timers[batch_name].cancel()
            del self._timers[batch_name]
            
        if batch_info['items']:
            try:
                batch_info['processor'](batch_info['items'])
            except Exception as e:
                # 批处理失败，记录错误但不影响程序运行
                logging.getLogger(__name__).error(f"批处理 {batch_name} 失败: {e}")
                
    def flush_all(self):
        """立即处理所有批次"""
        with self._lock:
            batch_names = list(self._batches.keys())
            for batch_name in batch_names:
                self._process_batch(batch_name)
                
    def get_batch_stats(self) -> Dict[str, Any]:
        """获取批处理统计信息"""
        with self._lock:
            return {
                'active_batches': len(self._batches),
                'batch_details': {
                    name: {
                        'item_count': len(info['items']),
                        'created_at': info['created_at']
                    }
                    for name, info in self._batches.items()
                }
            }


class AsyncHandler:
    """异步处理器 - 提供异步操作支持"""
    
    def __init__(self, max_concurrent: int = 10):
        self.max_concurrent = max_concurrent
        self._semaphore = threading.Semaphore(max_concurrent)
        self._executor = ThreadPoolExecutor(max_workers=max_concurrent, thread_name_prefix="AsyncHandler")
        self._active_tasks = {}
        self._task_counter = 0
        self._lock = threading.Lock()
        
    def submit_async(self, func: Callable, *args, **kwargs) -> str:
        """提交异步任务"""
        with self._lock:
            self._task_counter += 1
            task_id = f"async_task_{self._task_counter}"
            
        def wrapped_func():
            with self._semaphore:
                try:
                    return func(*args, **kwargs)
                finally:
                    with self._lock:
                        if task_id in self._active_tasks:
                            del self._active_tasks[task_id]
                            
        future = self._executor.submit(wrapped_func)
        
        with self._lock:
            self._active_tasks[task_id] = {
                'future': future,
                'submitted_at': time.time(),
                'func_name': func.__name__ if hasattr(func, '__name__') else str(func)
            }
            
        return task_id
        
    def get_result(self, task_id: str, timeout: float = None):
        """获取异步任务结果"""
        with self._lock:
            if task_id not in self._active_tasks:
                raise ValueError(f"任务 {task_id} 不存在")
            future = self._active_tasks[task_id]['future']
            
        return future.result(timeout=timeout)
        
    def is_completed(self, task_id: str) -> bool:
        """检查任务是否完成"""
        with self._lock:
            if task_id not in self._active_tasks:
                return True  # 任务不存在，认为已完成
            return self._active_tasks[task_id]['future'].done()
            
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self._lock:
            if task_id not in self._active_tasks:
                return False
            future = self._active_tasks[task_id]['future']
            return future.cancel()
            
    def get_active_tasks(self) -> List[str]:
        """获取活跃任务列表"""
        with self._lock:
            return list(self._active_tasks.keys())
            
    def get_stats(self) -> Dict[str, Any]:
        """获取异步处理统计信息"""
        with self._lock:
            return {
                'active_task_count': len(self._active_tasks),
                'max_concurrent': self.max_concurrent,
                'available_slots': self._semaphore._value
            }
            
    def cleanup(self):
        """清理资源"""
        self._executor.shutdown(wait=False)


class PerformanceOptimizer:
    """性能优化器主类"""
    
    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or logging.getLogger(__name__)
        self.profiler = StartupProfiler()
        self.lazy_loader = LazyLoader()
        self.cache = OfflineLicenseCache()
        self.background_manager = BackgroundTaskManager()
        self.memory_optimizer = MemoryOptimizer()
        
        # 运行时性能组件
        self.smart_cache = SmartCache()
        self.batch_processor = BatchProcessor()
        self.async_handler = AsyncHandler()
        
        # 性能配置
        self.config = {
            'enable_startup_profiling': True,
            'enable_background_tasks': True,
            'enable_caching': True,
            'cache_max_age_hours': 24,
            'background_max_workers': 3,
            'memory_gc_interval': 300,  # 5分钟
            'smart_cache_enabled': True,
            'batch_processing_enabled': True,
            'async_processing_enabled': True,
            'batch_size': 100,
            'cache_hit_threshold': 0.8
        }
        
        # 启动内存优化定时器
        self._start_memory_optimization_timer()
        
    def _start_memory_optimization_timer(self):
        """启动内存优化定时器"""
        def memory_optimization_loop():
            while True:
                time.sleep(self.config['memory_gc_interval'])
                try:
                    collected = self.memory_optimizer.optimize_memory()
                    if collected > 0:
                        self.logger.debug(f"内存优化完成，回收了 {collected} 个对象")
                except Exception as e:
                    self.logger.error(f"内存优化失败: {e}")
                    
        timer_thread = threading.Thread(target=memory_optimization_loop, daemon=True)
        timer_thread.start()
        
    def optimize_startup(self, func: Callable):
        """启动优化装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            if self.config['enable_startup_profiling']:
                self.profiler.checkpoint("startup_begin", "开始启动优化")
                
            try:
                result = func(*args, **kwargs)
                
                if self.config['enable_startup_profiling']:
                    self.profiler.checkpoint("startup_complete", "启动完成")
                    self.logger.info(f"启动性能: {self.profiler.checkpoints[-1]['elapsed']:.3f}秒")
                    
                return result
            except Exception as e:
                if self.config['enable_startup_profiling']:
                    self.profiler.checkpoint("startup_failed", f"启动失败: {e}")
                raise
                
        return wrapper
        
    def cached_operation(self, cache_key: str = None):
        """缓存操作装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                if not self.config['enable_caching']:
                    return func(*args, **kwargs)
                    
                key = cache_key or f"{func.__name__}_{hash(str(args) + str(kwargs))}"
                
                # 尝试从缓存获取
                cached_result = self.cache.get(key)
                if cached_result is not None:
                    self.logger.debug(f"缓存命中: {key}")
                    return cached_result
                    
                # 执行函数并缓存结果
                result = func(*args, **kwargs)
                self.cache.set(key, result)
                self.logger.debug(f"缓存设置: {key}")
                return result
                
            return wrapper
        return decorator
        
    def background_task(self, task_name: str = None):
        """后台任务装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                if not self.config['enable_background_tasks']:
                    return func(*args, **kwargs)
                    
                name = task_name or func.__name__
                task_id = self.background_manager.submit_task(name, func, *args, **kwargs)
                self.logger.debug(f"后台任务已提交: {task_id}")
                return task_id
                
            return wrapper
        return decorator
        
    def smart_cached_operation(self, cache_key: str = None):
        """智能缓存操作装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                if not self.config['smart_cache_enabled']:
                    return func(*args, **kwargs)
                    
                key = cache_key or f"{func.__name__}_{hash(str(args) + str(kwargs))}"
                
                # 尝试从智能缓存获取
                cached_result = self.smart_cache.get(key)
                if cached_result is not None:
                    self.logger.debug(f"智能缓存命中: {key}")
                    return cached_result
                    
                # 执行函数并缓存结果
                result = func(*args, **kwargs)
                self.smart_cache.set(key, result)
                self.logger.debug(f"智能缓存设置: {key}")
                return result
                
            return wrapper
        return decorator
        
    def batch_operation(self, batch_name: str, batch_size: int = None):
        """批处理操作装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                if not self.config['batch_processing_enabled']:
                    return func(*args, **kwargs)
                    
                # 设置批处理大小
                size = batch_size or self.config['batch_size']
                self.batch_processor.batch_size = size
                
                # 添加到批处理队列
                self.batch_processor.add_to_batch(batch_name, (args, kwargs), 
                                                lambda items: [func(*item[0], **item[1]) for item in items])
                
                self.logger.debug(f"项目已添加到批处理: {batch_name}")
                return None  # 批处理操作不返回直接结果
                
            return wrapper
        return decorator
        
    def async_operation(self, task_name: str = None):
        """异步操作装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                if not self.config['async_processing_enabled']:
                    return func(*args, **kwargs)
                    
                task_id = self.async_handler.submit_async(func, *args, **kwargs)
                self.logger.debug(f"异步任务已提交: {task_id}")
                return task_id
                
            return wrapper
        return decorator
        
    def optimize_data_processing(self, data_list: List[Any], process_func: Callable, 
                               batch_size: int = None, use_async: bool = True) -> List[Any]:
        """优化数据处理 - 结合批处理和异步处理"""
        if not data_list:
            return []
            
        size = batch_size or self.config['batch_size']
        results = []
        
        if use_async and self.config['async_processing_enabled']:
            # 异步批处理
            futures = []
            for i in range(0, len(data_list), size):
                batch = data_list[i:i + size]
                task_id = self.async_handler.submit_async(
                    lambda b: [process_func(item) for item in b], batch
                )
                futures.append(task_id)
                
            # 收集结果
            for task_id in futures:
                try:
                    batch_results = self.async_handler.get_result(task_id, timeout=30)
                    results.extend(batch_results)
                except Exception as e:
                    self.logger.error(f"异步批处理失败: {e}")
                    
        else:
            # 同步批处理
            for i in range(0, len(data_list), size):
                batch = data_list[i:i + size]
                try:
                    batch_results = [process_func(item) for item in batch]
                    results.extend(batch_results)
                except Exception as e:
                    self.logger.error(f"同步批处理失败: {e}")
                    
        return results
        
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        return {
            'startup_profile': self.profiler.get_report(),
            'memory_stats': self.memory_optimizer.get_memory_stats(),
            'running_tasks': self.background_manager.get_running_tasks(),
            'cache_stats': {
                'memory_cache_size': len(self.cache._memory_cache),
                'cache_dir': self.cache.cache_dir
            },
            'smart_cache_stats': self.smart_cache.get_stats(),
            'batch_processor_stats': self.batch_processor.get_batch_stats(),
            'async_handler_stats': self.async_handler.get_stats()
        }
        
    def cleanup(self):
        """清理所有资源"""
        try:
            self.lazy_loader.cleanup()
            self.background_manager.cleanup()
            self.cache.clear_expired()
            self.smart_cache.clear()
            self.batch_processor.flush_all()
            self.async_handler.cleanup()
            self.memory_optimizer.optimize_memory()
            self.logger.info("性能优化器清理完成")
        except Exception as e:
            self.logger.error(f"性能优化器清理失败: {e}")
    
    def get_memory_optimization_report(self) -> Dict[str, Any]:
        """获取内存优化报告"""
        return {
            'memory_stats': self.memory_optimizer.get_memory_stats(),
            'optimization_result': self.memory_optimizer.optimize_memory(),
            'cache_stats': self.smart_cache.get_stats(),
            'object_pools': {name: pool.size() for name, pool in self.memory_optimizer._object_pools.items()}
        }
    
    def optimize_for_memory_usage(self):
        """针对内存使用进行优化"""
        # 1. 执行内存优化
        result = self.memory_optimizer.optimize_memory()
        
        # 2. 清理过期缓存
        self.cache.clear_expired()
        self.smart_cache.clear()
        
        # 3. 处理所有批次以释放内存
        self.batch_processor.flush_all()
        
        # 4. 记录优化结果
        self.logger.info(f"内存优化完成: {result}")
        return result


# 全局性能优化器实例
_performance_optimizer = None

def get_performance_optimizer() -> PerformanceOptimizer:
    """获取全局性能优化器实例"""
    global _performance_optimizer
    if _performance_optimizer is None:
        _performance_optimizer = PerformanceOptimizer()
    return _performance_optimizer


# 便捷装饰器
def optimize_startup(func):
    """启动优化装饰器"""
    return get_performance_optimizer().optimize_startup(func)

def cached_operation(cache_key: str = None):
    """缓存操作装饰器"""
    return get_performance_optimizer().cached_operation(cache_key)

def background_task(task_name: str = None):
    """后台任务装饰器"""
    return get_performance_optimizer().background_task(task_name)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码保护模块 - 实现Python代码混淆和反编译保护
提供字符串加密、函数名混淆、反调试检测等功能
"""

import os
import sys
import base64
import hashlib
import random
import string
import time
import threading
import inspect
from typing import Dict, List, Any, Optional, Callable
import logging


class StringObfuscator:
    """字符串混淆器 - 加密和解密关键字符串"""
    
    def __init__(self, key: str = None):
        """初始化字符串混淆器
        
        Args:
            key: 加密密钥，如果为None则自动生成
        """
        self.key = key or self._generate_key()
        self._cache = {}
        
    def _generate_key(self) -> str:
        """生成加密密钥"""
        # 基于系统信息生成密钥
        import platform
        system_info = f"{platform.node()}{platform.system()}{platform.processor()}"
        return hashlib.sha256(system_info.encode()).hexdigest()[:32]
    
    def encrypt_string(self, text: str) -> str:
        """加密字符串
        
        Args:
            text: 要加密的字符串
            
        Returns:
            str: 加密后的字符串（Base64编码）
        """
        if not text:
            return ""
            
        # 简单的XOR加密
        key_bytes = self.key.encode()
        text_bytes = text.encode('utf-8')
        
        encrypted = bytearray()
        for i, byte in enumerate(text_bytes):
            encrypted.append(byte ^ key_bytes[i % len(key_bytes)])
        
        return base64.b64encode(encrypted).decode('ascii')
    
    def decrypt_string(self, encrypted_text: str) -> str:
        """解密字符串
        
        Args:
            encrypted_text: 加密的字符串（Base64编码）
            
        Returns:
            str: 解密后的字符串
        """
        if not encrypted_text:
            return ""
            
        # 检查缓存
        if encrypted_text in self._cache:
            return self._cache[encrypted_text]
        
        try:
            # Base64解码
            encrypted_bytes = base64.b64decode(encrypted_text.encode('ascii'))
            
            # XOR解密
            key_bytes = self.key.encode()
            decrypted = bytearray()
            for i, byte in enumerate(encrypted_bytes):
                decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
            
            result = decrypted.decode('utf-8')
            self._cache[encrypted_text] = result
            return result
            
        except Exception:
            return encrypted_text  # 如果解密失败，返回原文


class FunctionObfuscator:
    """函数名混淆器 - 混淆函数和变量名"""
    
    def __init__(self):
        self.name_mapping = {}
        self.used_names = set()
        
    def generate_obfuscated_name(self, original_name: str) -> str:
        """生成混淆后的名称
        
        Args:
            original_name: 原始名称
            
        Returns:
            str: 混淆后的名称
        """
        if original_name in self.name_mapping:
            return self.name_mapping[original_name]
        
        # 生成随机名称
        while True:
            # 使用随机字母和数字组合
            length = random.randint(8, 16)
            chars = string.ascii_letters + string.digits
            new_name = ''.join(random.choice(chars) for _ in range(length))
            
            # 确保以字母开头
            if new_name[0].isdigit():
                new_name = random.choice(string.ascii_letters) + new_name[1:]
            
            if new_name not in self.used_names:
                self.used_names.add(new_name)
                self.name_mapping[original_name] = new_name
                return new_name   
 
    def obfuscate_code(self, code: str, protected_names: List[str] = None) -> str:
        """混淆代码中的函数名和变量名
        
        Args:
            code: 要混淆的代码
            protected_names: 受保护的名称列表（不进行混淆）
            
        Returns:
            str: 混淆后的代码
        """
        protected_names = protected_names or []
        protected_names.extend(['__init__', '__main__', '__name__', '__file__'])
        
        # 这里实现简单的名称替换
        # 实际应用中可能需要更复杂的AST解析
        lines = code.split('\n')
        obfuscated_lines = []
        
        for line in lines:
            obfuscated_line = line
            # 简单的函数定义混淆
            if 'def ' in line and not any(name in line for name in protected_names):
                # 提取函数名并混淆
                parts = line.split('def ')
                if len(parts) > 1:
                    func_part = parts[1].split('(')[0].strip()
                    if func_part and not func_part.startswith('_'):
                        obfuscated_name = self.generate_obfuscated_name(func_part)
                        obfuscated_line = line.replace(f'def {func_part}', f'def {obfuscated_name}')
            
            obfuscated_lines.append(obfuscated_line)
        
        return '\n'.join(obfuscated_lines)


class AntiDebugger:
    """反调试检测器 - 检测调试器和逆向工程工具"""
    
    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or logging.getLogger(__name__)
        self.detection_active = True
        self._start_monitoring()
    
    def _start_monitoring(self):
        """启动监控线程"""
        def monitor():
            while self.detection_active:
                try:
                    if self.detect_debugger():
                        self.logger.warning("检测到调试器，触发安全响应")
                        self._security_response()
                    
                    if self.detect_virtual_machine():
                        self.logger.warning("检测到虚拟机环境")
                    
                    time.sleep(5)  # 每5秒检查一次
                except Exception as e:
                    self.logger.error(f"反调试监控异常: {e}")
                    time.sleep(10)
        
        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()
    
    def detect_debugger(self) -> bool:
        """检测调试器
        
        Returns:
            bool: 是否检测到调试器
        """
        try:
            # 检测Python调试器
            if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
                return True
            
            # 检测pdb调试器
            frame = inspect.currentframe()
            while frame:
                if 'pdb' in str(frame.f_code.co_filename).lower():
                    return True
                frame = frame.f_back
            
            # 检测IDE调试环境
            debug_vars = ['PYCHARM_HOSTED', 'VSCODE_PID', 'PYTHONPATH']
            for var in debug_vars:
                if var in os.environ:
                    return True
            
            return False
            
        except Exception:
            return False
    
    def detect_virtual_machine(self) -> bool:
        """检测虚拟机环境
        
        Returns:
            bool: 是否在虚拟机中运行
        """
        try:
            import platform
            
            # 检测虚拟机特征
            vm_indicators = [
                'vmware', 'virtualbox', 'qemu', 'xen', 'hyper-v',
                'parallels', 'bochs', 'kvm'
            ]
            
            system_info = platform.platform().lower()
            processor_info = platform.processor().lower()
            
            for indicator in vm_indicators:
                if indicator in system_info or indicator in processor_info:
                    return True
            
            # Windows特定检测
            if platform.system() == 'Windows':
                try:
                    import wmi
                    c = wmi.WMI()
                    for system in c.Win32_ComputerSystem():
                        if 'virtual' in system.Model.lower():
                            return True
                except ImportError:
                    pass
            
            return False
            
        except Exception:
            return False
    
    def _security_response(self):
        """安全响应 - 检测到威胁时的处理"""
        try:
            # 记录安全事件
            self.logger.critical("安全威胁检测：程序可能正在被调试或逆向")
            
            # 可以实现更严格的响应，如：
            # 1. 退出程序
            # 2. 清除敏感数据
            # 3. 发送警报
            
            # 这里实现温和的响应
            print("程序检测到异常环境，请在正常环境下运行")
            
        except Exception as e:
            self.logger.error(f"安全响应执行失败: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.detection_active = False

class CodeIntegrityChecker:
    """代码完整性检查器 - 验证程序文件的完整性"""
    
    def __init__(self, protected_files: List[str] = None):
        """初始化完整性检查器
        
        Args:
            protected_files: 需要保护的文件列表
        """
        self.protected_files = protected_files or []
        self.file_hashes = {}
        self._calculate_initial_hashes()
    
    def _calculate_initial_hashes(self):
        """计算初始文件哈希值"""
        for file_path in self.protected_files:
            if os.path.exists(file_path):
                self.file_hashes[file_path] = self._calculate_file_hash(file_path)
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件的SHA256哈希值
        """
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                return hashlib.sha256(content).hexdigest()
        except Exception:
            return ""
    
    def verify_integrity(self) -> bool:
        """验证文件完整性
        
        Returns:
            bool: 所有文件是否完整
        """
        for file_path, expected_hash in self.file_hashes.items():
            if not os.path.exists(file_path):
                return False
            
            current_hash = self._calculate_file_hash(file_path)
            if current_hash != expected_hash:
                return False
        
        return True
    
    def get_modified_files(self) -> List[str]:
        """获取被修改的文件列表
        
        Returns:
            List[str]: 被修改的文件路径列表
        """
        modified_files = []
        
        for file_path, expected_hash in self.file_hashes.items():
            if not os.path.exists(file_path):
                modified_files.append(file_path)
                continue
            
            current_hash = self._calculate_file_hash(file_path)
            if current_hash != expected_hash:
                modified_files.append(file_path)
        
        return modified_files


class CodeProtectionManager:
    """代码保护管理器 - 统一管理所有保护功能"""
    
    def __init__(self, config: Dict[str, Any] = None, logger: logging.Logger = None):
        """初始化代码保护管理器
        
        Args:
            config: 配置参数
            logger: 日志记录器
        """
        self.config = config or {}
        self.logger = logger or logging.getLogger(__name__)
        
        # 初始化各个保护组件
        self.string_obfuscator = StringObfuscator()
        self.function_obfuscator = FunctionObfuscator()
        
        # 反调试检测（可选启用）
        self.anti_debugger = None
        if self.config.get('enable_anti_debug', True):
            self.anti_debugger = AntiDebugger(self.logger)
        
        # 完整性检查器
        protected_files = self.config.get('protected_files', [])
        self.integrity_checker = CodeIntegrityChecker(protected_files)
        
        self.logger.info("代码保护管理器初始化完成")
    
    def protect_string(self, text: str) -> str:
        """保护字符串（加密）
        
        Args:
            text: 要保护的字符串
            
        Returns:
            str: 加密后的字符串
        """
        return self.string_obfuscator.encrypt_string(text)
    
    def unprotect_string(self, encrypted_text: str) -> str:
        """解保护字符串（解密）
        
        Args:
            encrypted_text: 加密的字符串
            
        Returns:
            str: 解密后的字符串
        """
        return self.string_obfuscator.decrypt_string(encrypted_text)
    
    def obfuscate_function_names(self, code: str, protected_names: List[str] = None) -> str:
        """混淆函数名
        
        Args:
            code: 要混淆的代码
            protected_names: 受保护的名称列表
            
        Returns:
            str: 混淆后的代码
        """
        return self.function_obfuscator.obfuscate_code(code, protected_names)
    
    def verify_program_integrity(self) -> bool:
        """验证程序完整性
        
        Returns:
            bool: 程序是否完整
        """
        try:
            is_intact = self.integrity_checker.verify_integrity()
            if not is_intact:
                modified_files = self.integrity_checker.get_modified_files()
                self.logger.warning(f"检测到文件被修改: {modified_files}")
            return is_intact
        except Exception as e:
            self.logger.error(f"完整性验证失败: {e}")
            return False
    
    def is_running_in_safe_environment(self) -> bool:
        """检查是否在安全环境中运行
        
        Returns:
            bool: 是否在安全环境中
        """
        if not self.anti_debugger:
            return True
        
        # 检测调试器
        if self.anti_debugger.detect_debugger():
            self.logger.warning("检测到调试器环境")
            return False
        
        # 检测虚拟机（可选）
        if self.config.get('block_vm', False):
            if self.anti_debugger.detect_virtual_machine():
                self.logger.warning("检测到虚拟机环境")
                return False
        
        return True
    
    def cleanup(self):
        """清理资源"""
        if self.anti_debugger:
            self.anti_debugger.stop_monitoring()
        self.logger.info("代码保护管理器资源清理完成")
# 全局保护管理器实例
_protection_manager = None


def get_protection_manager(config: Dict[str, Any] = None, logger: logging.Logger = None) -> CodeProtectionManager:
    """获取代码保护管理器实例（单例模式）
    
    Args:
        config: 配置参数
        logger: 日志记录器
        
    Returns:
        CodeProtectionManager: 保护管理器实例
    """
    global _protection_manager
    if _protection_manager is None:
        _protection_manager = CodeProtectionManager(config, logger)
    return _protection_manager


def protect_string(text: str) -> str:
    """便捷函数：保护字符串
    
    Args:
        text: 要保护的字符串
        
    Returns:
        str: 加密后的字符串
    """
    return get_protection_manager().protect_string(text)


def unprotect_string(encrypted_text: str) -> str:
    """便捷函数：解保护字符串
    
    Args:
        encrypted_text: 加密的字符串
        
    Returns:
        str: 解密后的字符串
    """
    return get_protection_manager().unprotect_string(encrypted_text)


def verify_integrity() -> bool:
    """便捷函数：验证程序完整性
    
    Returns:
        bool: 程序是否完整
    """
    return get_protection_manager().verify_program_integrity()


def check_safe_environment() -> bool:
    """便捷函数：检查安全环境
    
    Returns:
        bool: 是否在安全环境中
    """
    return get_protection_manager().is_running_in_safe_environment()


# 装饰器：保护函数
def protected_function(func: Callable) -> Callable:
    """装饰器：为函数添加保护
    
    Args:
        func: 要保护的函数
        
    Returns:
        Callable: 受保护的函数
    """
    def wrapper(*args, **kwargs):
        # 检查环境安全性
        if not check_safe_environment():
            raise RuntimeError("程序运行环境不安全")
        
        # 验证程序完整性
        if not verify_integrity():
            raise RuntimeError("程序完整性验证失败")
        
        return func(*args, **kwargs)
    
    return wrapper


if __name__ == "__main__":
    # 测试代码保护功能
    logging.basicConfig(level=logging.INFO)
    
    # 创建保护管理器
    config = {
        'enable_anti_debug': True,
        'block_vm': False,
        'protected_files': ['main.py', 'license_manager.py']
    }
    
    manager = get_protection_manager(config)
    
    # 测试字符串保护
    original = "这是一个敏感字符串"
    encrypted = manager.protect_string(original)
    decrypted = manager.unprotect_string(encrypted)
    
    print(f"原始字符串: {original}")
    print(f"加密字符串: {encrypted}")
    print(f"解密字符串: {decrypted}")
    
    # 测试环境检查
    print(f"安全环境: {manager.is_running_in_safe_environment()}")
    print(f"程序完整性: {manager.verify_program_integrity()}")
    
    # 清理资源
    manager.cleanup()
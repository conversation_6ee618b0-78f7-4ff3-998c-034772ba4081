import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import sys
import os
import traceback
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Font, PatternFill
from condition import a, b  # 假设这是条件和提示信息模块
import concurrent.futures
from functools import partial
import logging
from datetime import datetime
import json
from PIL import Image, ImageTk  # 添加PIL库以处理图像
import csv
from license_manager import LicenseManager
from license_ui import LicenseStatusWidget, show_license_warning, show_license_input, contact_support
from license_models import LicenseStatus, LicenseError
from performance_optimizer import optimize_startup, get_performance_optimizer

def sc(row, start, end, pattern):
    """生成序列化的字段值列表"""
    result = []
    for i in range(start, end + 1):
        key = pattern.format(str(i).zfill(2))
        result.append(row.get(key, ''))
    return result

def get_root():
    return os.path.dirname(sys.executable) if hasattr(sys, "frozen") else os.path.dirname(__file__)

def safe_save(wb, path):
    try:
        wb.save(path)
    except PermissionError:
        temp_path = path + ".tmp"
        wb.save(temp_path)
        os.replace(temp_path, path)

def load_input_file(path):
    """根据文件扩展名加载数据"""
    if path.endswith('.xlsx'):
        return load_excel_file(path)
    elif path.endswith('.csv'):
        return load_csv_file(path)
    else:
        raise ValueError("不支持的文件格式，请选择.xlsx或.csv文件")

def load_excel_file(path):
    """加载Excel文件，增加错误处理和性能优化"""
    try:
        wb = load_workbook(path, data_only=True, read_only=True)
        ws = wb.active
        rows = list(ws.rows)
        
        if not rows:
            raise ValueError("Excel文件为空")
        
        field_codes = [str(cell.value).strip() if cell.value else '' for cell in rows[0]]
        field_to_index = {code: idx for idx, code in enumerate(field_codes) if code}
        
        if not field_to_index:
            raise ValueError("未找到有效的字段列名")
        
        cases = []
        for row_idx, row in enumerate(rows[1:], 2):
            case = {'_row_number': row_idx}
            for code, idx in field_to_index.items():
                if idx < len(row):
                    case[code] = str(row[idx].value) if row[idx].value is not None else ''
                else:
                    case[code] = ''
            cases.append(case)
        
        wb.close()
        return cases
        
    except Exception as e:
        raise Exception(f"加载Excel文件失败: {str(e)}")

def load_csv_file(path):
    """加载CSV文件"""
    try:
        cases = []
        encodings_to_try = ['utf-8-sig', 'gbk', 'gb18030', 'gb2312', 'latin1']
        
        header = None
        rows = None
        
        for encoding in encodings_to_try:
            try:
                with open(path, 'r', encoding=encoding, newline='') as f:
                    reader = csv.reader(f)
                    header = [h.strip() for h in next(reader)]
                    rows = list(reader)
                break
            except (UnicodeDecodeError, StopIteration):
                continue
        
        if header is None:
            raise Exception("无法使用常见的编码（UTF-8, GBK, GB18030）解码文件或文件为空。")

        field_to_index = {code: idx for idx, code in enumerate(header) if code}

        if not field_to_index:
            raise ValueError("未找到有效的字段列名")

        for row_idx, row in enumerate(rows, 2):
            case = {'_row_number': row_idx}
            for code, idx in field_to_index.items():
                if idx < len(row):
                    case[code] = row[idx]
                else:
                    case[code] = ''
            cases.append(case)
        return cases
    except StopIteration:
        raise ValueError("CSV文件为空")
    except Exception as e:
        raise Exception(f"加载CSV文件失败: {str(e)}")

def format_date(date_str):
    """格式化日期字符串，增加更多格式支持"""
    if not date_str:
        return ''
    try:
        date_str = str(date_str).strip()
        if ' ' in date_str:
            date_part, time_part = date_str.split(' ', 1)
        else:
            date_part, time_part = date_str, '00:00:00'
            
        # 支持更多日期格式
        if '-' in date_part:
            parts = date_part.split('-')
            if len(parts) == 3:
                year, month, day = parts[0], parts[1].zfill(2), parts[2].split(' ')[0].zfill(2)
                return f"{year}/{month}/{day} {time_part}"
        elif '/' in date_part:
            parts = date_part.split('/')
            if len(parts) == 3:
                year, month, day = parts[0], parts[1].zfill(2), parts[2].zfill(2)
                return f"{year}/{month}/{day} {time_part}"
        elif len(date_part) == 8 and date_part.isdigit():  # YYYYMMDD格式
            year, month, day = date_part[:4], date_part[4:6], date_part[6:8]
            return f"{year}/{month}/{day} {time_part}"
            
        return f"{date_str} {time_part}"
    except Exception as e:
        print(f"日期格式化错误: {str(e)} - 原始值: {date_str}")
        return date_str

class DataValidator:
    """数据验证器类，用于封装验证逻辑"""
    
    def __init__(self):
        # 不再使用新的规则系统，直接使用原始逻辑
        pass
        
    def validate_row(self, row, conditions):
        """验证单行数据，使用原始的prepare_rule_arguments逻辑"""
        errors = []
        
        # 使用原始的prepare_rule_arguments函数生成参数
        try:
            rule_args = prepare_rule_arguments(row)
        except Exception as e:
            errors.append(f"生成规则参数失败: {str(e)}")
            return errors
        
        # 验证每个条件
        for i, condition in enumerate(conditions):
            try:
                if i >= len(rule_args):
                    # 如果规则参数不够，跳过
                    continue
                    
                # 获取当前规则的参数
                args = rule_args[i]
                
                # 确保args是tuple格式，但不要错误地解包列表
                if not isinstance(args, tuple):
                    if isinstance(args, list):
                        # 如果是列表，将其作为单个参数
                        result_args = (args,)
                    else:
                        # 单个值，转换为tuple
                        result_args = (args,)
                else:
                    # 已经是tuple，直接使用
                    result_args = args
                
                # 执行验证条件
                ok = condition(*result_args)
                
                if not ok:
                    # 使用原始的错误描述
                    if i < len(b):
                        error_desc = b[i]
                    else:
                        error_desc = f"规则{i+1}验证失败"
                    errors.append(f"{i+1}. {error_desc}")
                    
            except TypeError as e:
                # 参数不匹配错误
                error_msg = f"规则{i+1}参数不匹配: {str(e)}"
                errors.append(f"{i+1}. {error_msg}")
                
            except ValueError as e:
                # 数值转换错误
                error_msg = f"规则{i+1}数值错误: {str(e)}"
                errors.append(f"{i+1}. {error_msg}")
                
            except Exception as e:
                # 其他异常
                error_msg = f"规则{i+1}执行异常: {str(e)}"
                if hasattr(e, '__class__'):
                    error_msg += f" ({e.__class__.__name__})"
                errors.append(f"{i+1}. {error_msg}")
                
        return errors

# 创建全局验证器实例
_validator = DataValidator()

def get_base_result(row):
    """提取基础结果信息"""
    return [
        str(row.get('A02', '')),  # 医疗机构名称
        str(row.get('A48', '')),  # 病案号
        format_date(row.get('B15', '')),  # 出院时间
        str(row.get('A11', '')),  # 姓名
        str(row.get('B16C', '')),  # 出院科别
    ]

def process_row(row):
    """处理单行数据，使用优化的验证器"""
    errors = []
    base_result = get_base_result(row)
    row_number = row.get('_row_number', '未知')
    
    try:
        # 使用全局验证器实例
        errors = _validator.validate_row(row, a)
        
    except Exception as e:
        errors.append(f"第{row_number}行数据处理错误: {str(e)}")

    if not errors:
        return []
    
    return [base_result + [error] for error in errors]

def prepare_rule_arguments(row):
    """准备规则验证所需的参数"""
    return [
        (row.get('A01', '')),  # 机构代码 - 使用空字符串作为默认值
        (row.get("D01", "0"),  # 总费用 - 使用"0"作为默认值以便计算
         sc(row, 11, 19, "D{}") + sc(row, 20, 23, "D{}") + sc(row, 24, 34, "D{}")),
        (row.get("D20", "0"), row.get("D20x01", "0"), row.get("D20x02", "0")),  # 规则3: 手术治疗费=麻醉费+手术费
        (row.get("A28C", "")),  # 住址邮编
        (row.get("B25", "")),   # 住院医师
        (row.get("B24", "")),   # 主治医师
        (row.get("B31", "")),   # 质控医师
        (row.get("B30C", "")),  # 病案质量
        (row.get("B32", "")),   # 质控护师
        (row.get("B33", "")),   # 质控日期, 
        (row.get("C26C", "")),  # ABORh血型,
        (row.get("D26", ""), row.get("F21", "")), # 血费、输血反应
        (row.get("D26", ""), row.get("F22", ""), row.get("F23", ""), row.get("F24", ""), row.get("F25", ""), row.get("F26", "")),  # 血费、输血反应
        (row.get("D26", ""), row.get("C27C", "")),  # 血费、输血反应
        (row.get("C26C", ""), row.get("C27C", "")),  # ABO血型,# Rh血型
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C"), row.get("C12C", "")),  # 15、中毒损伤外因
        (row.get("C03C", ""), row.get("C09C", "")), #主诊与病理
        (row.get("C03C", ""), row.get("C09C", "")),
        (row.get("C03C", ""), row.get("C09C", "")),
        (row.get("C03C", ""), row.get("C09C", "")),
        (row.get("C03C", ""), row.get("C09C", "")),
        (row.get("C03C", ""), row.get("C09C", "")),
        (row.get("C03C", ""), row.get("C09C", "")),  # 22
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),  # 23 主诊与所有诊断
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),  # 24
        (row.get("C03C", "")),  # 25
        (row.get("C03C", "")),
        (row.get("C03C", "")),
        (row.get("C03C", "")),
        (row.get("C03C", "")),
        (row.get("C03C", "")),  # 30
        (row.get("C03C", "")),
        (row.get("C03C", "")),
        (row.get("C03C", "")),
        (row.get("C03C", "")),
        (row.get("C03C", "")),  # 35
        (row.get("C03C", "")),
        (row.get("C03C", "")),
        (row.get("C03C", "")),
        (row.get("C03C", "")),
        (row.get("C03C", "")),  
        (row.get("C03C", "")),
        (row.get("C03C", "")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),  # 43
        (row.get("B23", "")), #主副主任医师
        (row.get("B33", ""), row.get("B15", "")),#质控日期, 出院日期
        (row.get("A38C", "")), #职业
        (row.get("B26", "")), #责任护士
        (row.get("A02", "")),  # 48，医疗机构名称
        (row.get("A46C", ""), row.get("A29", ""), row.get("A30", ""), row.get("A31C", "")), #医疗付费方式
        (row.get("A46C", "")),
        (row.get("C24C", ""), row.get("C25", "")), #药物过敏，过敏药物
        (row.get("A11", "")),  # 52,姓名
        (row.get("A12C", ""), row.get("C14x01C", ""), sc(row, 1, 20, "C35x{}C")), # 53,性别，所有手术名称
        (row.get("A12C", ""), row.get("C14x01C", ""), sc(row, 1, 20, "C35x{}C")),
        (row.get("A12C", ""), row.get("C01C", ""), row.get("C03C", ""), [row.get("C03C", "")] + sc(row, 1, 20, "C06x{}C")),  # 55
        (row.get("A12C", ""), row.get("C01C", ""), row.get("C03C", ""), [row.get("C03C", "")] + sc(row, 1, 20, "C06x{}C")),  # 56，性别诊断
        (row.get("A12C", ""), row.get("A14", ""), row.get("A21C", "")),
        (row.get("A12C", "")), #58
        (row.get("A17", "")),
        (row.get("A17", "")),  # 60
        (row.get("A17", ""), row.get("A14", ""), row.get("A16", "")), #新生儿入院体重，年龄，年龄不足1周岁的年龄（天）
        (sc(row, 1, 5, "A18x{}")), #新生儿出生体重
        (sc(row, 1, 5, "A18x{}")),
        (sc(row, 1, 5, "A18x{}"), row.get("C03C", ""), sc(row, 1, 20, "C06x{}C"), row.get("A14", ""), row.get("A16", "")),  
        (row.get("A28C", "")),    #65,住址邮编
        (row.get("C12C", "")),  #损伤中毒外因
        (row.get("A27", "")),   #现住址电话
        (row.get("A27", "")),
        (row.get("A26", "")),  # 69，现住址
        (row.get("C37x06", ""), row.get("B12", ""), row.get("B15", "")),#手术日期，入院日期，出院日期，
        (row.get("C37x05", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x04", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x03", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x02", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x01", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x07", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x08", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x09", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x10", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x11", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x12", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x13", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x14", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x15", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x16", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x17", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x18", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x19", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("C37x20", ""), row.get("B12", ""), row.get("B15", "")),
        (row.get("B36C", ""), row.get("B37", "")),  # 90,是否有出院31天内再住院计划，出院31天内再住院目的
        (row.get("B36C", "")),
        (row.get("B20", "")),#92，实际住院天数
        (row.get("B20", "")),
        (row.get("B20", ""), row.get("B12", ""), row.get("B15", "")),  # 94
        (row.get("A20", ""), row.get("A12C", "")),
        (row.get("A20", "")), # 96，身份证号
        (row.get("A20", "")),
        (row.get("B11C", ""), row.get("B11C", "")),  # 98，入院途径
        (row.get("B11C", ""), row.get("C03C", "")),
        (row.get("B11C", "")),
        (row.get("B12", ""), row.get("B15", "")),  # 101
        (row.get("B12", "")),
        (row.get("B12", ""), row.get("A13", ""), row.get("A16", "")),
        (row.get("B13C", "")),  # 104
        (row.get("C04N", ""), row.get("C03C", ""), row.get("C05C", ""), row.get("F05", "")),
        (row.get("A16", ""), row.get("A14", "")),
        (row.get("A14", ""), row.get("A38C", "")),
        (row.get("A14", ""), row.get("B12", ""), row.get("A13", "")),
        (row.get("A14", ""), row.get("A17", "")),
        (row.get("A14", ""), sc(row, 1, 5, "A18x{}")),
        (row.get("A14", ""), row.get("A21C", "")),  # 113
        (row.get("A14", ""), row.get("A33C", "")),
        (row.get("A14", ""), row.get("A38C", "")),
        (row.get("A14", "")),
        (row.get("A14", "")),
        (row.get("A14", "")),
        (row.get("A14", ""), row.get("A16", "")),
        (row.get("A14", ""), row.get("A46C", "")),
        (row.get("A14", ""), [row.get("C03C", "")] + sc(row, 1, 10, "C06x{}C")),
        (row.get("A16", ""), row.get("A14", "")),  # 122
        (row.get("A19C", "")),
        (row.get("C01C", "")),
        (row.get("C02N", "")),
        (row.get("C01C", ""), row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C01C", ""), [row.get("C03C", "")] + sc(row, 1, 20, "C06x{}C")),
        (row.get("D20x01", ""), [row.get("C23x01", "")] + sc(row, 1, 20, "C44x{}")),
        (row.get("D20x01", ""), [row.get("C22x01C", "")] + sc(row, 1, 20, "C43x{}C")),
        (row.get("A32", ""), row.get("A11", "")),  # 130
        (row.get("A32", "")),
        (row.get("A32", ""), row.get("A33C", ""), row.get("A34", ""), row.get("A35", "")),
        (row.get("A33C", ""), row.get("A21C", "")),
        (row.get("A33C", "")),
        (row.get("A33C", "")),
        (row.get("A35", "")),
        (row.get("A35", "")),
        (row.get("A34", "")),  # 138
        (row.get("B34C", ""), row.get("B35", "")),
        (row.get("B34C", ""), row.get("B35", "")),
        (row.get("B34C", ""), row.get("C34C", "")),
        (row.get("B34C", "")),
        (row.get("B22", "")),
        (row.get("B12", ""), row.get("B15", ""), row.get("C49x01", ""), row.get("C50x01", "")),
        (row.get("B12", ""), row.get("B15", ""), row.get("C49x02", ""), row.get("C50x02", "")),
        (row.get("B12", ""), row.get("B15", ""), row.get("C49x03", ""), row.get("C50x03", "")),
        (row.get("B12", ""), row.get("B15", ""), row.get("C49x04", ""), row.get("C50x04", "")),  # 147
        (row.get("B12", ""), row.get("B15", ""), row.get("C49x05", ""), row.get("C50x05", "")),
        (row.get("A23C", "")),
        (row.get("A21C", ""), row.get("A33C", "")),
        (row.get("A21C", "")),
        (row.get("A25C", "")),
        (row.get("A25C", "")),
        (row.get("A24", "")),
        (row.get("A15C", ""), row.get("A19C", "")),  
        (row.get("A15C", "")),
        (row.get("A31C", "")),
        (row.get("A31C", "")),  
        (row.get("A29", "")),
        (row.get("A30", "")),
        (row.get("A49", "")),  #157
        (row.get("A30", "")),  
        ([row.get("C03C", "")] + sc(row, 1, 20, "C06x{}C"), sc(row, 28, 33, "C{}")),
        (sc(row, 1, 20, "C06x{}C"), sc(row, 1, 5, "A18x{}")),
        (row.get("B15", "")),  # 161
        (row.get("B16C", "")),
        (row.get("A13", ""), row.get("A20", "")),
        (row.get("A13", "")),
        (row.get("A22", "")),
        (row.get("D15", ""), row.get("C11", "")),
        (row.get("C09C", "")),
        (row.get("C11", ""), row.get("C09C", "")),
        (row.get("A48", "")),
        (row.get("B29", "")),  # 170
        (row.get("D20", ""), row.get("C14x01C", ""), row.get("C15x01N", ""), row.get("C16x01", ""), row.get("C18x01", "")),   
        (row.get("C15x01N", ""), row.get("C17x01", ""), row.get("C21x01C", "")),
        (row.get("C22x01C", ""), row.get("C23x01", "")),  # 173
        (row.get("C05C", "")),
        (row.get("C04N", "")),
        (row.get("C03C", "")),
        (row.get("B14", "")),
        (row.get("B17", "")),
        (row.get("A16", ""), [row.get("C03C", "")] + sc(row, 1, 10, "C06x{}C"), row.get("A14", "")),
        (row.get("D01", ""), sum([float(each) if each.strip() and each.strip().replace('.', '', 1).isdigit() else 0 for each in sc(row, 11, 34, "D{}") + [row.get("D19x01", ""), row.get("D20x01", ""),row.get("D20x02", ""),row.get("D23x01", "")]])),
        (row.get("D01", ""), row.get("D09", "")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),  # 主诊与所有诊断
        (row.get("A14", ""), row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")), #185
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#188
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")), #191
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")), #197
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#200
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#204
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#208
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#211
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#214
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),       
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#217
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#220
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#223
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#226
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#229
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#232
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#235
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#240
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),   
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#243
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#246
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#249
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#252
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#255
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#258
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#261
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#264
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#267
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#270
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#273
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#276
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#279
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#282        
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#285
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#288
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#292
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#295
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#298
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#301
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#304
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#307
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#310
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#313
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#316
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#319
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#322
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#325
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#328
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#331
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#334
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#337
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#339
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#340
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#341
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#342
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#343
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#344
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#345
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#346
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#347
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#348
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#349
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#350
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#351
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#352
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#353
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#354
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#355
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#356
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#357
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#358
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#359
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#360
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#361
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#362
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#363
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#364
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#365
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#366
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#367
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#368
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#369
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#370
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#371
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#372
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#373
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#374
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#375
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#376
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#377
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#378
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#379
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#380
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#381
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#382
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#383
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#384
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#385
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#386
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#387
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#388
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#389
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#390
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#391
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#392
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#393
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#394    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#395
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#396
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#397
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#398
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#399
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#400
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#401
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#402
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#403
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#404
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#405
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#406
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#407
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#408
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#409
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#410
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#411
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#412
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#413
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#414
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#415
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#416
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#417
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#418
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#419
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#420
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#421
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#422
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#423
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#424
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#425
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#426
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#427
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#428
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#429
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#430
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#431
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#432
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#433
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#434
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#435
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#436
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#437
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#438
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#439
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#440
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#441
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#442
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#443
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#444
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#445
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#446
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#447
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#448
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#449
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#450
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#451
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#452
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#453
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#454
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#455
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#456
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#457
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#458
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#459
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#460
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#461
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#462
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#463
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#464
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#465
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#466
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#467
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#468
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#469
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#470
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#471
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#472
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#473
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#474
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#475
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#476
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#477
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#478
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#479
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#480
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#481
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#482
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#483
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#484
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#485
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#486
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#487
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#488        
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#489
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#490
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#491
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#492
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#493
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#494
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#495
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#496
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#497
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#498
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#499
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#500
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#501        
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#502
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#503
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#504
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#505
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#506
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#507
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#508
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#509
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#510
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#511
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#512
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#513
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#514
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#515
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#516
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#517
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#518
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#519
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#520
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#521
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#522
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#523
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#524
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#525
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#526
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#527
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#528
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#529
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#530
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#531
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#532
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#533
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#534
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#535
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#536
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#537
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#538
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#539
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#540
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#541
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#542
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#543
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#544
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#545
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#546
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#547
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#548
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#549
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#550
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#551
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#552
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#553
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#554
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#555    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#556
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#557
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#558
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#559
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#560
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#561
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#562
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#563
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#564
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#565
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#566
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#567
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#568
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#569
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#570
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#571
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#572
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#573
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#574
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#575
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#576
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#577
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#578
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#579
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#580
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#581
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#582
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#583
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#584
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#585
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#586
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#587
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#588
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#589
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#590
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#591
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#592
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#593
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#594
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#595
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#596
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#597
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#598
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#599
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#600  
            (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#601    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#602
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#603
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#604    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#605
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#606
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#607
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#608
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#609
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#610
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#611
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#612
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#613
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#614
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#615
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#616
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#617
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#618
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#619
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#620
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#621
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#622
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#623
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#624
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#625
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#626
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#627
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#628
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#629
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#630
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#631
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#632
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#633
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#634
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#635    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#636    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#637    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#638    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#639    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#640    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#641
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#642
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#643
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#644
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#645
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#646
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#647
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#648
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#649
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#650
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#651
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#652
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#653
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#654
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#655
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#656
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#657
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#658
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#659
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#660
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#661
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#662
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#663
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#664
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#665    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#666
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#667
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#668
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#669
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#670
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#671
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#672
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#673
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#674
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#675    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#676
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#677
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#678
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#679
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#680
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#681
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#682
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#683
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#684
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#685
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#686
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#687
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#688
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#689
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#690
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#691
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#692
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#693
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#694
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#695    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#696
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#697    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#698
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#699
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#700
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#701
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#702
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#703    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#704
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#705
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#706
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#707
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#708
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#709
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#710
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#711    
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#712
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#713
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#714
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#715
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#716
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#717
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#718
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#719
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#720
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#721
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#722
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#723
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#724
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#725
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#726
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#727
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#728
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#729
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#730
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#731
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#732
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#733
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#734
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#735
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#736
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#737
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#738
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#739
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#740
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#741
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#742
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#743
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#744
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#745
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#746
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#747
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#748
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#749
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#750
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#751
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#752
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#753
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#754
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#755
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#756
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#757
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#758
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#759
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#760
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#761
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#762
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#763
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#764
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#765
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#766
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#767
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#768
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#769
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#770
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#771
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#772
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#773
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#774
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#775
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#776
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#777
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#778
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#779
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#780
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#781
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#782
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#783
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#784
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#785
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#786
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#787
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#788
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#789
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#790
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#791
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#792
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#793
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#794
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#795
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#796
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#797
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#798
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#799
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#800
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#801
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#802
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#803
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#804
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#805
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#806
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#807
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#808
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#809
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#810
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#811
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#812
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#813
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#814
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#815
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#816
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#817
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#818
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#819
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#820
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#821
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#822
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#823
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#824
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#825
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#826
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#827
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#828
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#829
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#830
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#831
        (row.get("A12C", ""), row.get("C01C", ""), row.get("C03C", ""), [row.get("C03C", "")] + sc(row, 1, 20, "C06x{}C")),
        (row.get("A12C", ""), row.get("C01C", ""), row.get("C03C", ""), [row.get("C03C", "")] + sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#834
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#835
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#836
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#837
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#838
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#839
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#840
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#841
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#842
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#843
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#844
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#845
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#846
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#847
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#848
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#849
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#850
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#851
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#852
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#853
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#854
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#855
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#856
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#857
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#858
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#859
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#860
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#861
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#862
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#863
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#864
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#865
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#866
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#867
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#868
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#869
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#870
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#871
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#872
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#873
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#874
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#875
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#876
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#877
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#878
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#879
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#880
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#881
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#882
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#883
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#884
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#885
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#886
        (row.get("C03C", ""), row.get("B34C", "")),#887
        (row.get("C03C", "")), #888    
        (row.get("C03C", "")),#889
        (row.get("B13C", ""), row.get("B16C", ""), row.get("B21C", "")),#890
        (row.get("C03C", ""), row.get("C14x01C", ""), sc(row, 1, 20, "C35x{}C")),#891
        (row.get("C14x01C", ""), sc(row, 1, 20, "C35x{}C")),#892
        (row.get("C14x01C", "")),#893
        (row.get("C01C", ""), row.get("C03C", ""), sc(row, 1, 20, "C06x{}C"),row.get("B12", ""),row.get("A13", "")),     
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),       
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")), #896
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#899
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C"),row.get("C09C", "")),#900
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C"),row.get("C09C", "")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C"),row.get("C09C", "")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C"),row.get("C09C", "")),#903
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#906
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C"),row.get("A14", "")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#1000
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),
        (row.get("C03C", ""), sc(row, 1, 20, "C06x{}C")),#1004



        ]
    
class EnhancedDataProcessorApp:
    @optimize_startup
    def __init__(self, root, logger=None, config=None, expiration_date=None):
        # 获取性能优化器
        self.performance_optimizer = get_performance_optimizer()
        self.performance_optimizer.profiler.checkpoint("app_init_start", "应用初始化开始")
        
        self.root = root
        self.logger = logger or logging.getLogger(__name__)
        self.config = config or load_config()  # 如果没有传入配置，则加载默认配置
        self.expiration_date = expiration_date # 保存过期日期
        
        self.performance_optimizer.profiler.checkpoint("config_loaded", "配置加载完成")
        
        # 初始化授权管理器 - 新增
        try:
            self.license_manager = LicenseManager(logger=self.logger)
            self.logger.info("授权管理器初始化成功")
        except Exception as e:
            self.logger.error(f"授权管理器初始化失败: {str(e)}")
            self.license_manager = None
            
        self.performance_optimizer.profiler.checkpoint("license_manager_init", "授权管理器初始化完成")
        
        self.root.title("首页逻辑&编码校验工具-Cokew 1.0")  # 版本更新为1.0
        self.root.geometry("700x400")  # 增加窗口大小
        self.root.resizable(True, True)  # 允许调整大小
        self.root.configure(bg='#f5f5f7')  # 使用更现代的背景色

        try:
            icon_path = os.path.join(get_root(), "icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception:
            pass

        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')

        # 定义颜色变量
        primary_color = '#4361ee'  # 主色调
        secondary_color = '#3f37c9'  # 次要色调
        bg_color = '#f5f5f7'  # 背景色
        text_color = '#333333'  # 文本色

        # 配置各种样式
        style.configure('TButton', font=('微软雅黑', 10), padding=8)
        style.configure('TLabel', font=('微软雅黑', 10), background=bg_color, foreground=text_color)
        style.configure('Header.TLabel', font=('微软雅黑', 14, 'bold'), background=bg_color, foreground=text_color)
        style.configure('Status.TLabel', font=('微软雅黑', 9), background=bg_color, foreground='#666666')
        style.configure('TProgressbar', thickness=16, background=primary_color)
        style.configure('Custom.TFrame', background=bg_color)

        # 按钮样式
        style.configure('Primary.TButton', background=primary_color, foreground='white')
        style.map('Primary.TButton',
                 background=[('active', secondary_color), ('pressed', secondary_color)],
                 foreground=[('active', 'white'), ('pressed', 'white')])

        style.configure('Secondary.TButton', background='#ffffff', foreground=text_color)
        style.map('Secondary.TButton',
                 background=[('active', '#f0f0f0'), ('pressed', '#e6e6e6')],
                 foreground=[('active', text_color), ('pressed', text_color)])

        # 主框架
        self.main_frame = ttk.Frame(root, padding="20", style='Custom.TFrame')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 顶部信息框架
        header_frame = ttk.Frame(self.main_frame, style='Custom.TFrame')
        header_frame.pack(fill=tk.X, pady=(0, 20))  # 增加下边距

        # 创建左侧布局，包含logo和标题
        left_header = ttk.Frame(header_frame, style='Custom.TFrame')
        left_header.pack(side=tk.LEFT, fill=tk.Y)

        # 添加标志图片
        try:
            # 尝试加载最合适的logo版本
            logo_paths = [
                ("logo.png", 64),       # 标准版本
                ("logo_large.png", 64), # 高分辨率版本
                ("logo_small.png", 48), # 小版本
                ("icon.png", 32)        # 图标版本
            ]

            logo_loaded = False
            for logo_file, display_size in logo_paths:
                logo_path = os.path.join(get_root(), logo_file)
                if os.path.exists(logo_path):
                    # 加载图像
                    logo_img = Image.open(logo_path)
                    # 确保大小合适
                    logo_size = (display_size, display_size)
                    logo_img = logo_img.resize(logo_size, Image.Resampling.LANCZOS)
                    self.logo_photo = ImageTk.PhotoImage(logo_img)

                    # 创建标签显示图像
                    logo_label = ttk.Label(left_header, image=self.logo_photo, background=bg_color)
                    logo_label.pack(side=tk.LEFT, padx=(0, 12))  # 增加右边距
                    logo_loaded = True
                    self.logger.info(f"已加载logo: {logo_file}")
                    break

            if not logo_loaded:
                self.logger.warning("未找到任何logo文件")
        except Exception as e:
            self.logger.error(f"加载logo失败: {str(e)}")
            traceback.print_exc()

        # 标题
        title_frame = ttk.Frame(left_header, style='Custom.TFrame')
        title_frame.pack(side=tk.LEFT, fill=tk.Y)

        ttk.Label(title_frame, text="首页逻辑 & 编码校验工具",
                 style='Header.TLabel').pack(side=tk.TOP, anchor=tk.W)

        # 添加副标题
        ttk.Label(title_frame, text="医疗数据质量控制系统",
                 style='Status.TLabel').pack(side=tk.TOP, anchor=tk.W, pady=(2, 0))

        # 版本号放在右侧
        version_label = ttk.Label(header_frame, text="v1.0", style='Status.TLabel')
        version_label.pack(side=tk.RIGHT, padx=(10, 0))

        # 说明文本
        info_frame = ttk.Frame(self.main_frame, style='Custom.TFrame')
        info_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(info_frame,
                 text="导入Excel或CSV文件进行医疗编码校验,结果将保存至输出文件夹。",
                 style='Status.TLabel').pack(anchor=tk.W)

        # 按钮框架
        button_frame = ttk.Frame(self.main_frame, style='Custom.TFrame')
        button_frame.pack(fill=tk.X, pady=(0, 20))

        # 创建按钮
        self.import_btn = ttk.Button(
            button_frame,
            text="导入文件",
            command=self.process_file,
            style='Primary.TButton',
            width=18
        )
        self.import_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.output_btn = ttk.Button(
            button_frame,
            text="打开输出文件夹",
            command=self.open_output_folder,
            style='Secondary.TButton',
            width=18
        )
        self.output_btn.pack(side=tk.LEFT)

        # 进度框架
        progress_frame = ttk.Frame(self.main_frame, style='Custom.TFrame')
        progress_frame.pack(fill=tk.X, pady=(0, 10))

        progress_label = ttk.Label(progress_frame, text="处理进度:", style='TLabel')
        progress_label.pack(side=tk.LEFT, padx=(0, 10))

        self.progress = ttk.Progressbar(
            progress_frame,
            length=460,
            mode='determinate',
            style='TProgressbar'
        )
        self.progress.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 进度百分比显示
        self.progress_percent = tk.StringVar(value="0%")
        percent_label = ttk.Label(progress_frame, textvariable=self.progress_percent, style='Status.TLabel')
        percent_label.pack(side=tk.RIGHT, padx=(10, 0))

        # 状态框架
        status_frame = ttk.Frame(self.main_frame, style='Custom.TFrame')
        status_frame.pack(fill=tk.X, pady=(5, 5))

        status_label = ttk.Label(status_frame, text="状态:", style='TLabel')
        status_label.pack(side=tk.LEFT, padx=(0, 10))

        self.status = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(status_frame, textvariable=self.status, style='Status.TLabel')
        self.status_label.pack(side=tk.LEFT)

        # 添加装饰性分隔线
        separator = ttk.Separator(self.main_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=(10, 10))

        # 底部信息面板
        info_panel = ttk.Frame(self.main_frame, style='Custom.TFrame')
        info_panel.pack(fill=tk.BOTH, expand=True)

        # 左侧统计信息
        stats_frame = ttk.Frame(info_panel, style='Custom.TFrame')
        stats_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        self.stats_var = tk.StringVar(value="准备就绪，等待处理...")
        self.stats_label = ttk.Label(stats_frame, textvariable=self.stats_var, style='Status.TLabel')
        self.stats_label.pack(anchor=tk.W)

        # 在左侧添加授权状态显示
        if self.license_manager:
            try:
                # 获取授权状态信息
                status, message = self.license_manager.validate_license()
                
                # 创建状态显示标签 - 放在左下角
                status_text = "状态: 试用版"
                if status == LicenseStatus.VALID:
                    status_text = "状态: 已授权"
                elif status == LicenseStatus.EXPIRED:
                    status_text = "状态: 已过期"
                elif status == LicenseStatus.TRIAL_EXPIRED:
                    status_text = "状态: 试用期已过期"
                
                status_label = ttk.Label(
                    stats_frame,
                    text=status_text,
                    style='Status.TLabel',
                    foreground="blue" if status == LicenseStatus.TRIAL else "green" if status == LicenseStatus.VALID else "red"
                )
                status_label.pack(anchor=tk.W, pady=(10, 0))
                
                self.logger.info("授权状态显示创建成功")
                
            except Exception as e:
                self.logger.error(f"创建授权状态显示失败: {str(e)}")

        # 中间时间信息框架
        center_frame = ttk.Frame(info_panel, style='Custom.TFrame')
        center_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 添加时间信息 - 放在中间
        if self.license_manager:
            try:
                status, message = self.license_manager.validate_license()
                
                # 创建详细时间信息标签 - 放在中间
                detail_text = ""
                if status == LicenseStatus.TRIAL:
                    trial_status = self.license_manager.check_trial_period()
                    if trial_status and trial_status.end_date:
                        end_date_str = trial_status.end_date.strftime('%Y年%m月%d日')
                        detail_text = f"试用期至: {end_date_str} (剩余{trial_status.days_remaining}天)"
                elif status == LicenseStatus.VALID:
                    license_info = self.license_manager.get_license_info()
                    if license_info.expiry_date:
                        expiry_date_str = license_info.expiry_date.strftime('%Y年%m月%d日')
                        days_left = license_info.days_until_expiry()
                        detail_text = f"有效期至: {expiry_date_str} (剩余{days_left}天)"
                else:
                    detail_text = message
                
                if detail_text:
                    detail_label = ttk.Label(
                        center_frame,
                        text=detail_text,
                        style='Status.TLabel',
                        foreground="gray"
                    )
                    detail_label.pack(anchor=tk.CENTER, pady=(10, 0))
                
            except Exception as e:
                self.logger.error(f"创建时间信息显示失败: {str(e)}")

        # 右侧版权信息和管理按钮
        copyright_frame = ttk.Frame(info_panel, style='Custom.TFrame')
        copyright_frame.pack(side=tk.RIGHT)

        copyright_label = ttk.Label(
            copyright_frame,
            text="© 2025 ZhiHui Medipro",
            style='Status.TLabel'
        )
        copyright_label.pack(anchor=tk.E)

        # 添加管理按钮 - 放在右下角
        if self.license_manager:
            manage_btn = ttk.Button(
                copyright_frame,
                text="授权管理",
                command=self._show_license_dialog,
                width=8
            )
            manage_btn.pack(anchor=tk.E, pady=(5, 0))
        else:
            # 降级到原有显示方式
            if self.expiration_date:
                expiration_text = f"有效期至: {self.expiration_date.strftime('%Y年%m月%d日')}"
                expiration_label = ttk.Label(
                    copyright_frame,
                    text=expiration_text,
                    style='Status.TLabel'
                )
                expiration_label.pack(anchor=tk.E, pady=(5, 0))

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def _show_license_dialog(self):
        """显示授权管理对话框"""
        if not self.license_manager:
            messagebox.showerror("错误", "授权管理器未初始化")
            return
        
        # 显示授权码输入对话框
        license_code = show_license_input(self.root)
        
        if license_code:
            # 尝试激活授权码
            success, message = self.license_manager.activate_license(license_code)
            
            if success:
                messagebox.showinfo("成功", message)
                # 刷新授权状态显示
                self.root.after(100, self._refresh_license_status)
            else:
                messagebox.showerror("失败", message)
    
    def _refresh_license_status(self):
        """刷新授权状态显示"""
        # 重新创建界面以显示更新后的授权状态
        try:
            # 这里可以添加刷新逻辑，或者重启应用程序
            messagebox.showinfo("提示", "授权状态已更新，请重启程序以查看最新状态")
        except Exception as e:
            self.logger.error(f"刷新授权状态失败: {str(e)}")

    def open_output_folder(self):
        """打开输出文件夹"""
        try:
            output_dir = get_root()
            if os.path.exists(output_dir):
                os.startfile(output_dir)
            else:
                messagebox.showwarning("警告", "输出文件夹不存在")
        except Exception as e:
            self.logger.error(f"打开输出文件夹失败: {str(e)}")
            messagebox.showerror("错误", f"无法打开输出文件夹: {str(e)}")

    def process_file(self):
        # 检查规则数量与提示信息数量是否匹配
        if len(a) != len(b):
            messagebox.showwarning("警告", f"规则数量({len(a)})与提示信息数量({len(b)})不匹配")
            return

        try:
            input_path = filedialog.askopenfilename(
                title="选择Excel或CSV文件",
                filetypes=[("表格文件", "*.xlsx *.csv"), ("Excel文件", "*.xlsx"), ("CSV文件", "*.csv"), ("所有文件", "*.* Exper")]
            )
            if not input_path:
                return

            self.status.set("加载文件中...")
            self.progress_percent.set("0%")
            self.stats_var.set("正在加载文件...")
            self.import_btn.state(['disabled'])
            self.root.update()

            cases = load_input_file(input_path)
            total_cases = len(cases)

            if total_cases == 0:
                self.status.set("无数据")
                self.import_btn.state(['!disabled'])
                messagebox.showinfo("提示", "Excel文件中没有找到有效数据")
                return

            self.progress['maximum'] = total_cases

            output_wb = Workbook()
            output_ws = output_wb.active
            output_ws.append(['医疗机构名称', '病案号', '出院时间', '姓名', '出院科别', '错误内容'])

            # 使用线程池处理数据
            processed_results = []
            # 使用配置中的参数
            max_workers = self.config.get("max_workers", min(8, os.cpu_count() or 4))

            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 创建任务
                future_to_idx = {executor.submit(process_row, case): idx for idx, case in enumerate(cases, 1)}

                # 处理完成的任务
                processed_count = 0
                for future in concurrent.futures.as_completed(future_to_idx):
                    processed_count += 1
                    idx = future_to_idx[future]
                    try:
                        result = future.result()
                        if result:
                            processed_results.extend(result)

                        self.progress['value'] = processed_count
                        percent = int((processed_count / total_cases) * 100)
                        self.progress_percent.set(f"{percent}%")
                        self.status.set(f"处理中 {processed_count}/{total_cases}")
                        self.root.update_idletasks()

                    except Exception as e:
                        traceback.print_exc()
                        print(f"处理第{idx}条数据时发生错误: {str(e)}")

            # 将所有结果写入Excel
            for row in processed_results:
                output_ws.append(row)

            # 设置标题样式
            header_font = Font(bold=True, size=12)
            header_fill = PatternFill(start_color="E0E0E0", end_color="E0E0E0", fill_type="solid")

            for cell in output_ws[1]:
                cell.font = header_font
                cell.fill = header_fill

            # 调整列宽
            for column in output_ws.columns:
                max_length = max(min(len(str(cell.value or '')), 50) for cell in column)
                output_ws.column_dimensions[column[0].column_letter].width = max_length + 2

            # 调整错误内容列宽，保证可读性
            output_ws.column_dimensions['F'].width = 60

            # 交替行颜色
            for row_idx, row in enumerate(output_ws.iter_rows(min_row=2)):
                if row_idx % 2 == 0:
                    for cell in row:
                        cell.fill = PatternFill(start_color="F8F8F8", end_color="F8F8F8", fill_type="solid")

            # 冻结首行
            output_ws.freeze_panes = "A2"

            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"校验结果_{timestamp}.xlsx"
            output_path = os.path.join(get_root(), output_filename)

            safe_save(output_wb, output_path)

            # 更新统计信息
            error_count = len(processed_results)
            self.stats_var.set(f"处理完成: 共验证 {total_cases} 条记录，发现 {error_count} 个问题")
            self.progress['value'] = total_cases
            self.progress_percent.set("100%")
            self.status.set("处理完成")
            self.import_btn.state(['!disabled'])

            messagebox.showinfo(
                "成功",
                f"处理完成!\n\n共验证 {total_cases} 条记录\n发现 {error_count} 个问题\n\n结果已保存到:\n{output_path}"
            )

        except Exception as e:
            self.status.set("错误")
            self.stats_var.set(f"处理失败: {str(e)}")
            self.import_btn.state(['!disabled'])
            self.logger.error(f"处理文件时发生错误: {str(e)}")
            traceback.print_exc()
            messagebox.showerror("错误", f"处理过程中发生错误:\n{str(e)}")

    def on_closing(self):
        if messagebox.askokcancel("退出", "确定要退出吗？"):
            self.root.destroy()

def setup_logging():
    """设置日志记录"""
    log_dir = os.path.join(get_root(), "logs")
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f"process_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def load_config():
    """加载配置文件"""
    config_path = os.path.join(get_root(), "config.json")
    default_config = {
        "max_workers": min(8, os.cpu_count() or 4),
        "auto_save_interval": 10,
        "output_filename": "output.xlsx",
        "column_width_limit": 50,
        "theme": "modern"  # 新增主题配置
    }

    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
            return default_config
    else:
        # 创建默认配置文件
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"创建配置文件失败: {str(e)}")
        return default_config
        
# 在菜单中添加授权管理选项
def _create_menu(self):
    menubar = tk.Menu(self.root)
    self.root.config(menu=menubar)
    
    # 帮助菜单
    help_menu = tk.Menu(menubar, tearoff=0)
    menubar.add_cascade(label="帮助", menu=help_menu)
    help_menu.add_command(label="关于", command=self._show_about)
    help_menu.add_separator()
    help_menu.add_command(label="授权管理", command=self._show_license_manager)
    help_menu.add_command(label="联系客服", command=contact_support)

def _show_license_manager(self):
    """显示授权管理对话框"""
    if self.license_manager:
        license_code = show_license_input(self.root)
        if license_code:
            success, message = self.license_manager.activate_license(license_code)
            if success:
                messagebox.showinfo("成功", message)
                if hasattr(self, 'license_status_widget'):
                    self.license_status_widget.refresh()
            else:
                messagebox.showerror("失败", message)        

def main():
    print("程序启动中...")
    try:
        # 设置默认过期日期（用于向后兼容）
        import datetime
        expiration_date = datetime.date(2025, 8, 1)
        
        # 初始化授权管理器
        license_manager = None
        try:
            license_manager = LicenseManager()
            print("授权管理器初始化成功")
        except Exception as e:
            print(f"授权管理器初始化失败: {str(e)}")
        
        # 执行授权检查
        if license_manager:
            try:
                status, message = license_manager.validate_license()
                print(f"授权状态: {status.value}, 消息: {message}")
                
                # 处理不同的授权状态
                if status == LicenseStatus.EXPIRED:
                    result = show_license_warning(None, 0, False)
                    if result == "license":
                        license_code = show_license_input(None)
                        if license_code:
                            success, msg = license_manager.activate_license(license_code)
                            if not success:
                                messagebox.showerror("激活失败", msg)
                                sys.exit()
                        else:
                            sys.exit()
                    elif result == "contact":
                        contact_support()
                        sys.exit()
                    else:
                        sys.exit()
                
                elif status == LicenseStatus.TRIAL_EXPIRED:
                    result = show_license_warning(None, 0, True)
                    if result == "license":
                        license_code = show_license_input(None)
                        if license_code:
                            success, msg = license_manager.activate_license(license_code)
                            if not success:
                                messagebox.showerror("激活失败", msg)
                                sys.exit()
                        else:
                            sys.exit()
                    elif result == "contact":
                        contact_support()
                        sys.exit()
                    else:
                        sys.exit()
                
                elif status in [LicenseStatus.TRIAL, LicenseStatus.VALID]:
                    # 检查是否需要显示警告
                    license_info = license_manager.get_license_info()
                    if license_info.is_trial():
                        trial_status = license_manager.check_trial_period()
                        if trial_status.needs_warning(7):  # 7天警告
                            show_license_warning(None, trial_status.days_remaining, True)
                    elif license_info.days_until_expiry() <= 7:  # 正式版7天警告
                        show_license_warning(None, license_info.days_until_expiry(), False)
                
                elif status == LicenseStatus.INVALID:
                    messagebox.showerror("授权无效", message)
                    sys.exit()
                    
            except LicenseError as e:
                messagebox.showerror("授权错误", str(e))
                sys.exit()
            except Exception as e:
                print(f"授权检查过程中发生错误: {str(e)}")
                # 降级到原有检查方式
                current_date = datetime.date.today()
                if current_date > expiration_date:
                    messagebox.showerror("软件过期", "本软件已过使用期限，请联系开发者获取最新版本。")
                    sys.exit()
        else:
            # 降级到原有检查方式
            current_date = datetime.date.today()
            if current_date > expiration_date:
                messagebox.showerror("软件过期", "本软件已过使用期限，请联系开发者获取最新版本。")
                sys.exit()

        logger = setup_logging()
        print("日志设置完成")
        config = load_config()
        print("配置加载完成")

        try:
            print("创建Tk根窗口...")
            root = tk.Tk()
            print("根窗口创建成功")

            # 设置窗口位置为屏幕中央
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()
            window_width = 800
            window_height = 600
            x_position = (screen_width - window_width) // 2
            y_position = (screen_height - window_height) // 2

            # 设置窗口大小和位置
            root.geometry(f"{window_width}x{window_height}+{x_position}+{y_position}")

            # 强制窗口显示在最前面
            root.attributes("-topmost", True)

            print(f"窗口位置: {x_position},{y_position}, 大小: {window_width}x{window_height}")
            print("初始化应用程序...")
            app = EnhancedDataProcessorApp(root, logger, config, expiration_date)
            print("应用程序初始化成功，开始主循环")

            root.mainloop()
            print("主循环结束")
        except Exception as e:
            print(f"程序初始化或运行时出现错误: {str(e)}")
            logger.error(f"程序初始化或运行时出现错误: {str(e)}")
            traceback.print_exc()
            messagebox.showerror("错误", f"程序初始化或运行时出现错误:\n{str(e)}")
    except Exception as e:
        print(f"程序启动异常: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
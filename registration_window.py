#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注册窗口 - 专门用于软件注册激活的窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import os

class RegistrationWindow:
    """注册激活窗口"""
    
    def __init__(self, parent=None, license_manager=None, title="软件注册", trial_expired=False):
        self.parent = parent
        self.license_manager = license_manager
        self.result = None
        self.license_code = None
        self.trial_expired = trial_expired

        # 创建窗口 - 完全避免创建根窗口
        if parent:
            self.window = tk.Toplevel(parent)
        else:
            # 直接创建主窗口，不使用Toplevel
            self.window = tk.Tk()
        
        self.window.title(title)
        self.window.geometry("650x580")  # 增大窗口尺寸以适应美化内容
        self.window.resizable(False, False)
        
        # 设置窗口背景色
        self.window.configure(bg='#f0f0f0')

        # 设置窗口居中
        self.center_window()

        # 设置窗口图标
        self.set_window_icon()
        
        # 配置样式
        self.setup_styles()

        # 设置窗口为模态
        if parent:
            self.window.transient(parent)
            self.window.grab_set()
        
        # 如果试用期已到期，设置为强制模态窗口
        if trial_expired:
            self.window.grab_set()  # 强制获取焦点
            self.window.attributes("-topmost", True)  # 置顶显示
            self.window.focus_force()  # 强制获取焦点

        # 创建界面
        self.create_widgets()

        # 绑定关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)

    def get_current_status_text(self):
        """获取当前授权状态文本"""
        if not self.license_manager:
            return "当前状态: 授权管理器未初始化"

        try:
            from license_models import LicenseStatus
            status, message = self.license_manager.validate_license()

            if status == LicenseStatus.TRIAL_EXPIRED:
                return "当前状态: 试用期已到期，需要注册才能继续使用"
            elif status == LicenseStatus.TRIAL:
                trial_status = self.license_manager.check_trial_period()
                if trial_status:
                    return f"当前状态: 试用期剩余 {trial_status.days_remaining} 天"
                else:
                    return "当前状态: 试用期"
            elif status == LicenseStatus.VALID:
                return "当前状态: 已注册（正式版）"
            elif status == LicenseStatus.EXPIRED:
                return "当前状态: 授权已过期，需要续费"
            else:
                return f"当前状态: {message}"
        except Exception as e:
            return f"当前状态: 获取状态失败 - {str(e)}"

    def center_window(self):
        """将窗口居中显示"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def set_window_icon(self):
        """设置窗口图标"""
        try:
            icon_path = "icon.png"
            if os.path.exists(icon_path):
                icon_image = Image.open(icon_path)
                icon_image = icon_image.resize((32, 32), Image.Resampling.LANCZOS)
                icon_photo = ImageTk.PhotoImage(icon_image)
                self.window.iconphoto(False, icon_photo)
        except Exception:
            pass
    
    def setup_styles(self):
        """配置窗口样式"""
        style = ttk.Style()
        
        # 配置主题
        try:
            style.theme_use('clam')  # 使用现代主题
        except:
            pass
        
        # 配置按钮样式
        style.configure("Accent.TButton",
                       font=("Arial", 10, "bold"),
                       padding=(20, 8))
        
        # 配置过期警告样式
        style.configure("Warning.TLabel",
                       font=("Arial", 11, "bold"),
                       foreground="#d32f2f",  # 红色
                       background="#f0f0f0")
        
        # 配置状态样式
        style.configure("Status.TLabel",
                       font=("Arial", 10),
                       background="#f0f0f0")
        
        # 配置输入框样式
        style.configure("Custom.TEntry",
                       font=("Courier New", 12),
                       padding=8)
    
    def create_widgets(self):
        """创建窗口界面"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题区域
        self.create_title_section(main_frame)
        
        # 分隔线
        ttk.Separator(main_frame, orient='horizontal').pack(fill=tk.X, pady=20)
        
        # 注册码输入区域
        self.create_input_section(main_frame)
        
        # 按钮区域
        self.create_button_section(main_frame)
        
        # 底部信息区域
        self.create_info_section(main_frame)
    
    def create_title_section(self, parent):
        """创建标题区域"""
        title_frame = ttk.Frame(parent)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 软件图标和标题
        try:
            logo_path = "logo.png"
            if os.path.exists(logo_path):
                logo_image = Image.open(logo_path)
                logo_image = logo_image.resize((64, 64), Image.Resampling.LANCZOS)
                self.logo_photo = ImageTk.PhotoImage(logo_image)
                logo_label = ttk.Label(title_frame, image=self.logo_photo)
                logo_label.pack(side=tk.LEFT, padx=(0, 15))
        except Exception:
            pass
        
        # 标题文本
        title_text_frame = ttk.Frame(title_frame)
        title_text_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        title_label = ttk.Label(title_text_frame, text="医疗数据校验工具",
                               font=("Arial", 16, "bold"))
        title_label.pack(anchor=tk.W)

        # 根据试用期状态显示不同的副标题
        if self.trial_expired:
            subtitle_text = "试用期已到期 - 请立即注册"
            # 使用tk.Label而不是ttk.Label来支持红色字体
            subtitle_label = tk.Label(title_text_frame, text=subtitle_text,
                                    font=("Arial", 12, "bold"),
                                    foreground="#d32f2f",  # 红色
                                    background="#f0f0f0")
        else:
            subtitle_text = "软件注册激活"
            subtitle_label = ttk.Label(title_text_frame, text=subtitle_text,
                                      font=("Arial", 12, "normal"))
        
        subtitle_label.pack(anchor=tk.W, pady=(5, 0))
    
    def create_input_section(self, parent):
        """创建注册码输入区域"""
        input_frame = ttk.LabelFrame(parent, text="注册信息", padding="15")
        input_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 当前状态显示 - 使用tk.Label来支持红色字体
        status_text = self.get_current_status_text()
        if self.trial_expired:
            status_label = tk.Label(input_frame, text=status_text,
                                   font=("Arial", 10, "bold"),
                                   foreground="#d32f2f",  # 红色
                                   background="#f0f0f0")
        else:
            status_label = tk.Label(input_frame, text=status_text,
                                   font=("Arial", 10),
                                   foreground="#1976d2",  # 蓝色
                                   background="#f0f0f0")
        status_label.pack(anchor=tk.W, pady=(0, 10))

        # 说明文本
        if self.trial_expired:
            info_text = ("您的试用期已经到期，需要注册才能继续使用软件。\n"
                        "请输入您购买的软件注册码以激活完整功能。\n"
                        "如果您还没有注册码，请联系我们的销售团队。")
            info_color = "#d32f2f"  # 红色
        else:
            info_text = ("请输入您购买的软件注册码以激活完整功能。\n"
                        "如果您还没有注册码，请联系我们的销售团队。")
            info_color = "#666666"  # 灰色

        info_label = tk.Label(input_frame, text=info_text,
                             font=("Arial", 9), 
                             foreground=info_color,
                             background="#f0f0f0",
                             justify=tk.LEFT)
        info_label.pack(anchor=tk.W, pady=(0, 15))
        
        # 注册码输入
        code_label = ttk.Label(input_frame, text="注册码:", font=("Arial", 10, "bold"))
        code_label.pack(anchor=tk.W, pady=(0, 5))
        
        self.code_entry = ttk.Entry(input_frame, font=("Courier New", 12), 
                                   width=40, style="Custom.TEntry")
        self.code_entry.pack(fill=tk.X, pady=(0, 10))
        self.code_entry.focus()
        
        # 绑定回车键
        self.code_entry.bind('<Return>', lambda e: self.register_software())
        
        # 注册码格式提示
        format_label = ttk.Label(input_frame, 
                                text="格式示例: XXXX-XXXX-XXXX-XXXX", 
                                font=("Arial", 8), foreground="gray")
        format_label.pack(anchor=tk.W)
    
    def create_button_section(self, parent):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 注册按钮
        register_btn = ttk.Button(button_frame, text="立即注册", 
                                 command=self.register_software,
                                 style="Accent.TButton")
        register_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 联系客服按钮
        contact_btn = ttk.Button(button_frame, text="联系客服", 
                                command=self.contact_support)
        contact_btn.pack(side=tk.RIGHT)
        
        # 只显示退出按钮，取消稍后注册选项
        exit_btn = ttk.Button(button_frame, text="退出程序",
                             command=self.exit_application)
        exit_btn.pack(side=tk.LEFT)
    
    def create_info_section(self, parent):
        """创建底部信息区域"""
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        # 分隔线
        ttk.Separator(info_frame, orient='horizontal').pack(fill=tk.X, pady=(0, 10))
        
        # 购买信息
        purchase_text = ("如需购买注册码，请联系：\n"
                        "公司：智汇医疗有限责任公司 (ZhiHui Medipro)\n"
                        "电话：15903143106\n"
                        "邮箱：<EMAIL>\n"
                        "微信：15903143106")
        purchase_label = ttk.Label(info_frame, text=purchase_text, 
                                  font=("Arial", 8), foreground="gray")
        purchase_label.pack(anchor=tk.W)
    
    def is_trial_available(self):
        """检查是否还有试用期可用"""
        if not self.license_manager:
            return False
        
        try:
            trial_status = self.license_manager.check_trial_period()
            return trial_status.is_active and trial_status.days_remaining > 0
        except:
            return False
    
    def register_software(self):
        """注册软件"""
        license_code = self.code_entry.get().strip()
        
        if not license_code:
            messagebox.showwarning("输入错误", "请输入注册码！", parent=self.window)
            self.code_entry.focus()
            return
        
        # 验证注册码格式
        if not self.validate_license_format(license_code):
            messagebox.showwarning("格式错误", 
                                 "注册码格式不正确！\n请检查输入的注册码。", 
                                 parent=self.window)
            self.code_entry.focus()
            return
        
        # 尝试激活
        if self.license_manager:
            try:
                # 显示处理中状态
                self.show_processing_state(True)
                
                success, message = self.license_manager.activate_license(license_code)
                
                self.show_processing_state(False)
                
                if success:
                    messagebox.showinfo("注册成功", 
                                      f"恭喜！软件注册成功！\n\n{message}\n\n"
                                      "现在您可以使用软件的所有功能。", 
                                      parent=self.window)
                    self.result = "success"
                    self.license_code = license_code
                    self.window.destroy()
                else:
                    messagebox.showerror("注册失败", 
                                       f"注册失败：{message}\n\n"
                                       "请检查注册码是否正确，或联系客服获取帮助。", 
                                       parent=self.window)
                    self.code_entry.select_range(0, tk.END)
                    self.code_entry.focus()
                    
            except Exception as e:
                self.show_processing_state(False)
                messagebox.showerror("注册错误", 
                                   f"注册过程中发生错误：{str(e)}\n\n"
                                   "请稍后重试或联系客服。", 
                                   parent=self.window)
        else:
            messagebox.showerror("系统错误", "授权管理器未初始化！", parent=self.window)
    
    def validate_license_format(self, license_code):
        """验证注册码格式"""
        # 简单的格式验证
        if len(license_code) < 10:
            return False
        
        # 检查是否包含非法字符
        allowed_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-')
        return all(c.upper() in allowed_chars for c in license_code)
    
    def show_processing_state(self, processing):
        """显示处理中状态"""
        if processing:
            self.window.config(cursor="wait")
            self.window.update()
        else:
            self.window.config(cursor="")
            self.window.update()
    
    def contact_support(self):
        """联系客服"""
        from license_ui import contact_support
        contact_support()

    def exit_application(self):
        """退出应用程序"""
        result = messagebox.askyesno("确认退出",
                                   "确定要退出程序吗？\n\n"
                                   "退出后需要注册才能重新使用软件。",
                                   parent=self.window)
        if result:
            self.result = "exit"
            self.window.destroy()

    def is_trial_available(self):
        """检查是否还有试用期可用"""
        return not self.trial_expired
    

    
    def on_close(self):
        """窗口关闭事件"""
        # 不允许关闭窗口，强制注册或退出
        messagebox.showwarning("无法关闭", 
                             "必须注册才能使用软件。\n\n"
                             "请输入注册码完成注册，或选择退出程序。", 
                             parent=self.window)
        return  # 阻止窗口关闭
    
    def show(self):
        """显示窗口并等待结果"""
        self.window.wait_window()
        return self.result, self.license_code

def show_registration_window(parent=None, license_manager=None, title="软件注册", trial_expired=False):
    """显示注册窗口

    Args:
        parent: 父窗口
        license_manager: 授权管理器
        title: 窗口标题
        trial_expired: 试用期是否已到期

    Returns:
        tuple: (结果, 注册码) - 结果可能是 "success", "later", "exit" 或 None
    """
    window = RegistrationWindow(parent, license_manager, title, trial_expired)
    return window.show()

# 测试代码
if __name__ == "__main__":
    import sys
    sys.path.insert(0, os.path.dirname(__file__))
    
    try:
        from license_manager import LicenseManager
        license_manager = LicenseManager()
    except:
        license_manager = None
    
    # 直接调用注册窗口，不需要创建额外的根窗口
    result, license_code = show_registration_window(None, license_manager, "测试注册窗口")
    
    print(f"注册结果: {result}")
    if license_code:
        print(f"注册码: {license_code}")
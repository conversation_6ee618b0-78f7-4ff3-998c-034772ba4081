#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注册窗口 - 专门用于软件注册激活的窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import os

class RegistrationWindow:
    """注册激活窗口"""
    
    def __init__(self, parent=None, license_manager=None, title="软件注册"):
        self.parent = parent
        self.license_manager = license_manager
        self.result = None
        self.license_code = None
        
        # 创建窗口
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.window.title(title)
        self.window.geometry("500x400")
        self.window.resizable(False, False)
        
        # 设置窗口居中
        self.center_window()
        
        # 设置窗口图标
        self.set_window_icon()
        
        # 设置窗口为模态
        if parent:
            self.window.transient(parent)
            self.window.grab_set()
        
        # 创建界面
        self.create_widgets()
        
        # 绑定关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
        
    def center_window(self):
        """将窗口居中显示"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def set_window_icon(self):
        """设置窗口图标"""
        try:
            icon_path = "icon.png"
            if os.path.exists(icon_path):
                icon_image = Image.open(icon_path)
                icon_image = icon_image.resize((32, 32), Image.Resampling.LANCZOS)
                icon_photo = ImageTk.PhotoImage(icon_image)
                self.window.iconphoto(False, icon_photo)
        except Exception:
            pass
    
    def create_widgets(self):
        """创建窗口界面"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题区域
        self.create_title_section(main_frame)
        
        # 分隔线
        ttk.Separator(main_frame, orient='horizontal').pack(fill=tk.X, pady=20)
        
        # 注册码输入区域
        self.create_input_section(main_frame)
        
        # 按钮区域
        self.create_button_section(main_frame)
        
        # 底部信息区域
        self.create_info_section(main_frame)
    
    def create_title_section(self, parent):
        """创建标题区域"""
        title_frame = ttk.Frame(parent)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 软件图标和标题
        try:
            logo_path = "logo.png"
            if os.path.exists(logo_path):
                logo_image = Image.open(logo_path)
                logo_image = logo_image.resize((64, 64), Image.Resampling.LANCZOS)
                self.logo_photo = ImageTk.PhotoImage(logo_image)
                logo_label = ttk.Label(title_frame, image=self.logo_photo)
                logo_label.pack(side=tk.LEFT, padx=(0, 15))
        except Exception:
            pass
        
        # 标题文本
        title_text_frame = ttk.Frame(title_frame)
        title_text_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        title_label = ttk.Label(title_text_frame, text="医疗数据校验工具", 
                               font=("Arial", 16, "bold"))
        title_label.pack(anchor=tk.W)
        
        subtitle_label = ttk.Label(title_text_frame, text="软件注册激活", 
                                  font=("Arial", 12))
        subtitle_label.pack(anchor=tk.W, pady=(5, 0))
    
    def create_input_section(self, parent):
        """创建注册码输入区域"""
        input_frame = ttk.LabelFrame(parent, text="注册信息", padding="15")
        input_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 说明文本
        info_text = ("请输入您购买的软件注册码以激活完整功能。\n"
                    "如果您还没有注册码，请联系我们的销售团队。")
        info_label = ttk.Label(input_frame, text=info_text, 
                              font=("Arial", 9), foreground="gray")
        info_label.pack(anchor=tk.W, pady=(0, 15))
        
        # 注册码输入
        code_label = ttk.Label(input_frame, text="注册码:", font=("Arial", 10, "bold"))
        code_label.pack(anchor=tk.W, pady=(0, 5))
        
        self.code_entry = ttk.Entry(input_frame, font=("Courier", 11), width=40)
        self.code_entry.pack(fill=tk.X, pady=(0, 10))
        self.code_entry.focus()
        
        # 绑定回车键
        self.code_entry.bind('<Return>', lambda e: self.register_software())
        
        # 注册码格式提示
        format_label = ttk.Label(input_frame, 
                                text="格式示例: XXXX-XXXX-XXXX-XXXX", 
                                font=("Arial", 8), foreground="gray")
        format_label.pack(anchor=tk.W)
    
    def create_button_section(self, parent):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 注册按钮
        register_btn = ttk.Button(button_frame, text="立即注册", 
                                 command=self.register_software,
                                 style="Accent.TButton")
        register_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 联系客服按钮
        contact_btn = ttk.Button(button_frame, text="联系客服", 
                                command=self.contact_support)
        contact_btn.pack(side=tk.RIGHT)
        
        # 稍后注册按钮（如果允许试用）
        if self.is_trial_available():
            later_btn = ttk.Button(button_frame, text="稍后注册", 
                                  command=self.register_later)
            later_btn.pack(side=tk.LEFT)
    
    def create_info_section(self, parent):
        """创建底部信息区域"""
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        # 分隔线
        ttk.Separator(info_frame, orient='horizontal').pack(fill=tk.X, pady=(0, 10))
        
        # 购买信息
        purchase_text = ("如需购买注册码，请联系：\n"
                        "电话：400-123-4567\n"
                        "邮箱：<EMAIL>\n"
                        "网站：www.example.com")
        purchase_label = ttk.Label(info_frame, text=purchase_text, 
                                  font=("Arial", 8), foreground="gray")
        purchase_label.pack(anchor=tk.W)
    
    def is_trial_available(self):
        """检查是否还有试用期可用"""
        if not self.license_manager:
            return False
        
        try:
            trial_status = self.license_manager.check_trial_period()
            return trial_status.is_active and trial_status.days_remaining > 0
        except:
            return False
    
    def register_software(self):
        """注册软件"""
        license_code = self.code_entry.get().strip()
        
        if not license_code:
            messagebox.showwarning("输入错误", "请输入注册码！", parent=self.window)
            self.code_entry.focus()
            return
        
        # 验证注册码格式
        if not self.validate_license_format(license_code):
            messagebox.showwarning("格式错误", 
                                 "注册码格式不正确！\n请检查输入的注册码。", 
                                 parent=self.window)
            self.code_entry.focus()
            return
        
        # 尝试激活
        if self.license_manager:
            try:
                # 显示处理中状态
                self.show_processing_state(True)
                
                success, message = self.license_manager.activate_license(license_code)
                
                self.show_processing_state(False)
                
                if success:
                    messagebox.showinfo("注册成功", 
                                      f"恭喜！软件注册成功！\n\n{message}\n\n"
                                      "现在您可以使用软件的所有功能。", 
                                      parent=self.window)
                    self.result = "success"
                    self.license_code = license_code
                    self.window.destroy()
                else:
                    messagebox.showerror("注册失败", 
                                       f"注册失败：{message}\n\n"
                                       "请检查注册码是否正确，或联系客服获取帮助。", 
                                       parent=self.window)
                    self.code_entry.select_range(0, tk.END)
                    self.code_entry.focus()
                    
            except Exception as e:
                self.show_processing_state(False)
                messagebox.showerror("注册错误", 
                                   f"注册过程中发生错误：{str(e)}\n\n"
                                   "请稍后重试或联系客服。", 
                                   parent=self.window)
        else:
            messagebox.showerror("系统错误", "授权管理器未初始化！", parent=self.window)
    
    def validate_license_format(self, license_code):
        """验证注册码格式"""
        # 简单的格式验证
        if len(license_code) < 10:
            return False
        
        # 检查是否包含非法字符
        allowed_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-')
        return all(c.upper() in allowed_chars for c in license_code)
    
    def show_processing_state(self, processing):
        """显示处理中状态"""
        if processing:
            self.window.config(cursor="wait")
            self.window.update()
        else:
            self.window.config(cursor="")
            self.window.update()
    
    def contact_support(self):
        """联系客服"""
        from license_ui import contact_support
        contact_support()
    
    def register_later(self):
        """稍后注册"""
        if self.is_trial_available():
            result = messagebox.askyesno("稍后注册", 
                                       "您选择稍后注册。\n\n"
                                       "试用期结束后，您需要注册才能继续使用软件。\n\n"
                                       "确定要稍后注册吗？", 
                                       parent=self.window)
            if result:
                self.result = "later"
                self.window.destroy()
        else:
            messagebox.showinfo("无法稍后注册", 
                              "试用期已结束，必须现在注册才能继续使用软件。", 
                              parent=self.window)
    
    def on_close(self):
        """窗口关闭事件"""
        if self.is_trial_available():
            result = messagebox.askyesnocancel("确认关闭", 
                                             "您还没有完成注册。\n\n"
                                             "是否稍后注册？\n"
                                             "选择'否'将退出程序。", 
                                             parent=self.window)
            if result is True:  # 稍后注册
                self.result = "later"
                self.window.destroy()
            elif result is False:  # 退出程序
                self.result = "exit"
                self.window.destroy()
            # result is None (取消) - 不关闭窗口
        else:
            result = messagebox.askyesno("确认退出", 
                                       "试用期已结束，必须注册才能使用软件。\n\n"
                                       "确定要退出程序吗？", 
                                       parent=self.window)
            if result:
                self.result = "exit"
                self.window.destroy()
    
    def show(self):
        """显示窗口并等待结果"""
        self.window.wait_window()
        return self.result, self.license_code

def show_registration_window(parent=None, license_manager=None, title="软件注册"):
    """显示注册窗口"""
    window = RegistrationWindow(parent, license_manager, title)
    return window.show()

# 测试代码
if __name__ == "__main__":
    import sys
    sys.path.insert(0, os.path.dirname(__file__))
    
    try:
        from license_manager import LicenseManager
        license_manager = LicenseManager()
    except:
        license_manager = None
    
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    result, license_code = show_registration_window(None, license_manager, "测试注册窗口")
    
    print(f"注册结果: {result}")
    if license_code:
        print(f"注册码: {license_code}")
    
    root.destroy()
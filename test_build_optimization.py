#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化打包脚本的功能
"""
import os
import sys
import tempfile
import shutil
from pathlib import Path

def test_build_script_import():
    """测试构建脚本可以正常导入"""
    try:
        import build_exe
        print("✓ 构建脚本导入成功")
        return True
    except ImportError as e:
        print(f"✗ 构建脚本导入失败: {e}")
        return False

def test_dependency_analysis():
    """测试依赖分析功能"""
    try:
        from build_exe import analyze_dependencies
        essential, redundant = analyze_dependencies()
        
        # 验证返回的是集合类型
        assert isinstance(essential, set), "essential_modules应该是集合类型"
        assert isinstance(redundant, set), "redundant_modules应该是集合类型"
        
        # 验证包含预期的模块
        assert 'tkinter' in essential, "essential_modules应该包含tkinter"
        assert 'openpyxl' in essential, "essential_modules应该包含openpyxl"
        
        print("✓ 依赖分析功能测试通过")
        return True
    except Exception as e:
        print(f"✗ 依赖分析功能测试失败: {e}")
        return False

def test_hash_calculation():
    """测试文件哈希计算功能"""
    try:
        from build_exe import calculate_file_hash
        
        # 创建临时测试文件
        with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8') as f:
            f.write("测试内容")
            temp_file = f.name
        
        try:
            # 计算哈希
            hash_value = calculate_file_hash(temp_file)
            
            # 验证哈希值格式
            assert len(hash_value) == 64, "SHA256哈希值应该是64个字符"
            assert all(c in '0123456789abcdef' for c in hash_value), "哈希值应该只包含十六进制字符"
            
            print("✓ 文件哈希计算功能测试通过")
            return True
        finally:
            # 清理临时文件
            os.unlink(temp_file)
            
    except Exception as e:
        print(f"✗ 文件哈希计算功能测试失败: {e}")
        return False

def test_lazy_imports_creation():
    """测试延迟导入模块创建"""
    try:
        from build_exe import implement_lazy_imports
        
        # 创建延迟导入模块
        result = implement_lazy_imports()
        assert result == True, "延迟导入模块创建应该成功"
        
        # 验证文件是否创建
        assert os.path.exists('lazy_imports.py'), "lazy_imports.py文件应该被创建"
        
        # 验证文件内容
        with open('lazy_imports.py', 'r', encoding='utf-8') as f:
            content = f.read()
            assert 'LazyImporter' in content, "文件应该包含LazyImporter类"
            assert 'lazy_importer' in content, "文件应该包含全局实例"
        
        print("✓ 延迟导入模块创建测试通过")
        return True
    except Exception as e:
        print(f"✗ 延迟导入模块创建测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists('lazy_imports.py'):
            os.remove('lazy_imports.py')

def test_integrity_checker_creation():
    """测试完整性检查器创建"""
    try:
        from build_exe import create_runtime_integrity_checker
        
        # 创建完整性检查器
        result = create_runtime_integrity_checker()
        assert result == True, "完整性检查器创建应该成功"
        
        # 验证文件是否创建
        assert os.path.exists('integrity_checker.py'), "integrity_checker.py文件应该被创建"
        
        # 验证文件内容
        with open('integrity_checker.py', 'r', encoding='utf-8') as f:
            content = f.read()
            assert 'IntegrityChecker' in content, "文件应该包含IntegrityChecker类"
            assert 'verify_startup_integrity' in content, "文件应该包含启动验证函数"
        
        print("✓ 完整性检查器创建测试通过")
        return True
    except Exception as e:
        print(f"✗ 完整性检查器创建测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists('integrity_checker.py'):
            os.remove('integrity_checker.py')

def test_secure_exit_creation():
    """测试安全退出机制创建"""
    try:
        from build_exe import create_secure_exit_mechanism
        
        # 创建安全退出机制
        result = create_secure_exit_mechanism()
        assert result == True, "安全退出机制创建应该成功"
        
        # 验证文件是否创建
        assert os.path.exists('secure_exit.py'), "secure_exit.py文件应该被创建"
        
        # 验证文件内容
        with open('secure_exit.py', 'r', encoding='utf-8') as f:
            content = f.read()
            assert 'SecureExit' in content, "文件应该包含SecureExit类"
            assert 'emergency_exit' in content, "文件应该包含紧急退出函数"
        
        print("✓ 安全退出机制创建测试通过")
        return True
    except Exception as e:
        print(f"✗ 安全退出机制创建测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists('secure_exit.py'):
            os.remove('secure_exit.py')

def main():
    """运行所有测试"""
    print("=" * 50)
    print("     优化打包脚本功能测试")
    print("=" * 50)
    
    tests = [
        test_build_script_import,
        test_dependency_analysis,
        test_hash_calculation,
        test_lazy_imports_creation,
        test_integrity_checker_creation,
        test_secure_exit_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ 测试 {test_func.__name__} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    if passed == total:
        print("✓ 所有测试通过!")
    else:
        print(f"✗ {total - passed} 个测试失败")
    print("=" * 50)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
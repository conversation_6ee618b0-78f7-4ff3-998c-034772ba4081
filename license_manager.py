#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
授权管理器 - 软件授权系统的核心控制器
提供授权验证、试用期管理、配置管理等功能
"""

import os
import sys
import json
import hashlib
import platform
import uuid
from datetime import datetime, timedelta
from typing import Optional, Tuple, Dict, Any
import logging
import threading

from license_models import (
    LicenseInfo, LicenseConfig, LicenseStatus, LicenseType, TrialStatus,
    ValidationResult, LicenseError, LicenseExpiredError, TrialExpiredError,
    InvalidLicenseCodeError, ConfigurationError
)
from performance_optimizer import (
    get_performance_optimizer, optimize_startup, cached_operation, background_task,
    OfflineLicenseCache
)


class LicenseManager:
    """授权管理器 - 系统核心控制器"""
    
    def __init__(self, config_path: str = None, logger: logging.Logger = None):
        """初始化授权管理器
        
        Args:
            config_path: 配置文件路径
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.config_path = config_path or "license.dat"
        self.config = LicenseConfig()
        self.license_info = LicenseInfo()
        self._machine_id = None
        self._performance_optimizer = get_performance_optimizer()
        self._validation_lock = threading.RLock()
        
        # 初始化离线授权缓存
        self._offline_cache = OfflineLicenseCache(
            cache_dir=".cache/license",
            max_age_hours=self.config.max_offline_days * 24 if hasattr(self.config, 'max_offline_days') else 168,
            max_offline_days=getattr(self.config, 'max_offline_days', 30)
        )
        
        # 初始化
        self._load_config()
        self._load_license_info()
        
        # 启动后台网络验证任务（如果需要）
        self._start_background_validation()
    
    def validate_license(self) -> Tuple[LicenseStatus, str]:
        """执行完整的授权验证流程
        
        Returns:
            Tuple[LicenseStatus, str]: 验证状态和消息
        """
        try:
            self.logger.info("开始授权验证流程")
            
            # 1. 检查试用期状态
            trial_status = self.check_trial_period()
            if trial_status.is_trial:
                if trial_status.is_expired():
                    self.logger.warning("试用期已过期")
                    return LicenseStatus.TRIAL_EXPIRED, "试用期已过期，请购买正式授权"
                elif trial_status.needs_warning(self.config.warning_days):
                    days = trial_status.days_remaining
                    message = f"试用期还剩 {days} 天，请及时购买正式授权"
                    self.logger.info(message)
                    return LicenseStatus.TRIAL, message
                else:
                    self.logger.info(f"试用期正常，还剩 {trial_status.days_remaining} 天")
                    return LicenseStatus.TRIAL, f"试用期还剩 {trial_status.days_remaining} 天"
            
            # 2. 检查正式授权
            if not self.license_info.license_code:
                self.logger.warning("未找到有效的授权码")
                return LicenseStatus.INVALID, "未找到有效的授权码"
            
            # 3. 检查过期日期
            if self.license_info.is_expired():
                self.logger.warning("授权已过期")
                return LicenseStatus.EXPIRED, "授权已过期，请续费"
            
            # 4. 检查即将过期
            days_left = self.license_info.days_until_expiry()
            if days_left <= self.config.warning_days:
                message = f"授权将在 {days_left} 天后过期，请及时续费"
                self.logger.warning(message)
                return LicenseStatus.VALID, message
            
            # 5. 授权有效
            self.logger.info("授权验证通过")
            return LicenseStatus.VALID, "授权有效"
            
        except Exception as e:
            error_msg = f"授权验证过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return LicenseStatus.INVALID, error_msg
    
    def check_trial_period(self) -> TrialStatus:
        """检查试用期状态
        
        Returns:
            TrialStatus: 试用期状态信息
        """
        try:
            # 如果已有正式授权，不使用试用期
            if self.license_info.license_code and not self.license_info.is_trial():
                return TrialStatus(is_trial=False, is_active=False)
            
            # 检查是否首次运行
            if not self.license_info.trial_start_date:
                # 检查是否可以激活试用期
                if not self._can_activate_trial():
                    self.logger.warning("试用期激活被拒绝：检测到可能的滥用")
                    return TrialStatus(is_trial=False, is_active=False)
                
                # 首次运行，激活试用期
                self._activate_trial()
            else:
                # 验证试用期的完整性
                if not self._validate_trial_integrity():
                    self.logger.warning("试用期验证失败：检测到篡改")
                    return TrialStatus(is_trial=False, is_active=False)
            
            # 计算试用期状态
            if self.license_info.trial_start_date:
                start_date = self.license_info.trial_start_date
                end_date = start_date + timedelta(days=self.license_info.trial_days)
                now = datetime.now()
                
                days_used = (now - start_date).days
                # 修复天数计算：使用总秒数计算，确保准确计算剩余天数
                total_seconds_remaining = (end_date - now).total_seconds()
                days_remaining = max(0, int(total_seconds_remaining / 86400))
                
                return TrialStatus(
                    is_trial=True,
                    is_active=now <= end_date,
                    start_date=start_date,
                    end_date=end_date,
                    days_remaining=days_remaining,
                    days_used=days_used
                )
            
            return TrialStatus(is_trial=False, is_active=False)
            
        except Exception as e:
            self.logger.error(f"检查试用期状态时发生错误: {str(e)}")
            return TrialStatus(is_trial=False, is_active=False)
    
    def activate_license(self, license_code: str) -> Tuple[bool, str]:
        """激活授权码
        
        Args:
            license_code: 授权码
            
        Returns:
            Tuple[bool, str]: 激活结果和消息
        """
        try:
            self.logger.info(f"尝试激活授权码: {license_code[:8]}...")
            
            # 验证授权码格式
            if not self._validate_license_code_format(license_code):
                return False, "授权码格式无效"
            
            # 解析授权码信息
            license_data = self._parse_license_code(license_code)
            if not license_data:
                return False, "授权码无效或已损坏"
            
            # 检查硬件绑定
            if self.config.enable_hardware_binding:
                if not self._validate_hardware_binding(license_data):
                    return False, "授权码与当前设备不匹配"
            
            # 更新授权信息
            self.license_info.license_code = license_code
            self.license_info.license_type = LicenseType(license_data.get('type', 'standard'))
            self.license_info.expiry_date = datetime.fromisoformat(license_data['expiry_date'])
            self.license_info.user_name = license_data.get('user_name', '')
            self.license_info.company_name = license_data.get('company_name', '')
            self.license_info.features = license_data.get('features', [])
            self.license_info.max_users = license_data.get('max_users', 1)
            self.license_info.machine_id = self.get_machine_id()
            self.license_info.last_verified = datetime.now()
            
            # 保存授权信息
            self._save_license_info()
            
            self.logger.info("授权码激活成功")
            return True, "授权码激活成功"
            
        except Exception as e:
            error_msg = f"激活授权码时发生错误: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def get_license_info(self) -> LicenseInfo:
        """获取当前授权信息
        
        Returns:
            LicenseInfo: 授权信息
        """
        return self.license_info
    
    def get_machine_id(self) -> str:
        """获取机器唯一标识
        
        Returns:
            str: 机器ID
        """
        if self._machine_id:
            return self._machine_id
        
        try:
            # 获取系统信息
            system_info = [
                platform.node(),  # 计算机名
                platform.machine(),  # 机器类型
                platform.processor(),  # 处理器信息
            ]
            
            # 尝试获取MAC地址
            try:
                mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                               for elements in range(0, 2*6, 2)][::-1])
                system_info.append(mac)
            except:
                pass
            
            # 生成机器ID
            machine_string = '|'.join(filter(None, system_info))
            self._machine_id = hashlib.sha256(machine_string.encode()).hexdigest()[:16]
            
            return self._machine_id
            
        except Exception as e:
            self.logger.error(f"获取机器ID时发生错误: {str(e)}")
            # 使用随机ID作为后备
            self._machine_id = hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()[:16]
            return self._machine_id
    
    def _activate_trial(self) -> None:
        """激活试用期"""
        try:
            self.logger.info("激活试用期")
            self.license_info.trial_start_date = datetime.now()
            self.license_info.license_type = LicenseType.TRIAL
            self.license_info.trial_days = self.config.trial_days
            self.license_info.machine_id = self.get_machine_id()
            self._save_license_info()
            
        except Exception as e:
            self.logger.error(f"激活试用期时发生错误: {str(e)}")
    
    def _validate_license_code_format(self, license_code: str) -> bool:
        """验证授权码格式
        
        Args:
            license_code: 授权码
            
        Returns:
            bool: 格式是否有效
        """
        # 简单的格式验证：长度和字符检查
        if not license_code or len(license_code) < 16:
            return False
        
        # 检查是否包含非法字符
        allowed_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-')
        return all(c in allowed_chars for c in license_code.upper())
    
    def _parse_license_code(self, license_code: str) -> Optional[Dict[str, Any]]:
        """解析授权码信息
        
        Args:
            license_code: 授权码
            
        Returns:
            Optional[Dict[str, Any]]: 解析出的授权信息
        """
        try:
            # 首先尝试从授权码数据库中查找
            try:
                import json
                with open('license_database.json', 'r', encoding='utf-8') as f:
                    license_db = json.load(f)
                    
                if license_code in license_db:
                    data = license_db[license_code]
                    # 检查是否过期
                    expiry_date = datetime.fromisoformat(data['expiry_date'])
                    if expiry_date > datetime.now():
                        return {
                            'type': data['type'],
                            'expiry_date': data['expiry_date'],
                            'user_name': data['user_name'],
                            'company_name': data['company_name'],
                            'features': ['basic', 'advanced'],
                            'max_users': 1,
                            'machine_id': data.get('machine_id') or self.get_machine_id()
                        }
                    else:
                        self.logger.warning(f"授权码已过期: {license_code}")
                        return None
                        
            except FileNotFoundError:
                self.logger.info("未找到授权码数据库，使用默认验证")
            
            # 如果数据库中没有找到，使用简化的验证逻辑（向后兼容）
            # 任何符合格式的授权码都给予1年有效期
            license_data = {
                'type': 'standard',
                'expiry_date': (datetime.now() + timedelta(days=365)).isoformat(),
                'user_name': 'Licensed User',
                'company_name': 'Licensed Company',
                'features': ['basic', 'advanced'],
                'max_users': 1,
                'machine_id': self.get_machine_id()
            }
            
            return license_data
            
        except Exception as e:
            self.logger.error(f"解析授权码时发生错误: {str(e)}")
            return None
    
    def _validate_hardware_binding(self, license_data: Dict[str, Any]) -> bool:
        """验证硬件绑定
        
        Args:
            license_data: 授权数据
            
        Returns:
            bool: 硬件绑定是否有效
        """
        try:
            expected_machine_id = license_data.get('machine_id')
            current_machine_id = self.get_machine_id()
            
            # 如果授权数据中没有机器ID，则跳过验证
            if not expected_machine_id:
                return True
            
            return expected_machine_id == current_machine_id
            
        except Exception as e:
            self.logger.error(f"验证硬件绑定时发生错误: {str(e)}")
            return False
    
    def _load_config(self) -> None:
        """加载配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    self.config = LicenseConfig.from_dict(config_data.get('config', {}))
                    self.logger.info("配置加载成功")
            else:
                self.logger.info("配置文件不存在，使用默认配置")
                self._save_config()
                
        except Exception as e:
            self.logger.error(f"加载配置时发生错误: {str(e)}")
            self.config = LicenseConfig()  # 使用默认配置
    
    def _save_config(self) -> None:
        """保存配置"""
        try:
            config_data = {
                'config': self.config.to_dict(),
                'version': '1.0',
                'created': datetime.now().isoformat()
            }
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
                
            self.logger.info("配置保存成功")
            
        except Exception as e:
            self.logger.error(f"保存配置时发生错误: {str(e)}")
    
    def _load_license_info(self) -> None:
        """加载授权信息"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    license_data = data.get('license_info', {})
                    if license_data:
                        self.license_info = LicenseInfo.from_dict(license_data)
                        self.logger.info("授权信息加载成功")
                    else:
                        self.logger.info("未找到授权信息，使用默认设置")
            else:
                self.logger.info("授权文件不存在")
                
        except Exception as e:
            self.logger.error(f"加载授权信息时发生错误: {str(e)}")
            self.license_info = LicenseInfo()  # 使用默认信息
    
    def _save_license_info(self) -> None:
        """保存授权信息"""
        try:
            # 读取现有数据
            data = {}
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            
            # 更新授权信息
            data['license_info'] = self.license_info.to_dict()
            data['last_updated'] = datetime.now().isoformat()
            
            # 保存数据
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            self.logger.info("授权信息保存成功")
            
        except Exception as e:
            self.logger.error(f"保存授权信息时发生错误: {str(e)}")
    
    def _can_activate_trial(self) -> bool:
        """检查是否可以激活试用期
        
        Returns:
            bool: 是否允许激活试用期
        """
        try:
            # 开发者模式：如果存在开发者标记文件，跳过安全检查
            if os.path.exists("dev_mode.txt"):
                self.logger.info("检测到开发者模式，跳过试用期安全检查")
                return True
            # 1. 检查全局试用期记录文件
            global_trial_file = os.path.expanduser("~/.medical_validator_trial")
            
            if os.path.exists(global_trial_file):
                try:
                    with open(global_trial_file, 'r', encoding='utf-8') as f:
                        trial_data = json.load(f)
                    
                    # 检查当前机器是否已经使用过试用期
                    current_machine_id = self.get_machine_id()
                    used_machines = trial_data.get('used_machines', [])
                    
                    if current_machine_id in used_machines:
                        self.logger.warning(f"机器 {current_machine_id} 已使用过试用期")
                        return False
                    
                    # 检查试用期使用次数限制
                    trial_count = trial_data.get('trial_count', 0)
                    if trial_count >= 3:  # 最多允许3次试用
                        self.logger.warning("试用期使用次数已达上限")
                        return False
                        
                except Exception as e:
                    self.logger.error(f"读取全局试用期记录失败: {e}")
                    # 如果文件损坏，为安全起见拒绝激活
                    return False
            
            # 2. 记录当前机器的试用期激活
            self._record_trial_activation()
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查试用期激活权限时发生错误: {str(e)}")
            return False
    
    def _record_trial_activation(self) -> None:
        """记录试用期激活"""
        try:
            global_trial_file = os.path.expanduser("~/.medical_validator_trial")
            current_machine_id = self.get_machine_id()
            
            # 读取现有记录
            trial_data = {'used_machines': [], 'trial_count': 0, 'activations': []}
            if os.path.exists(global_trial_file):
                try:
                    with open(global_trial_file, 'r', encoding='utf-8') as f:
                        trial_data = json.load(f)
                except:
                    pass
            
            # 更新记录
            if current_machine_id not in trial_data['used_machines']:
                trial_data['used_machines'].append(current_machine_id)
                trial_data['trial_count'] += 1
                trial_data['activations'].append({
                    'machine_id': current_machine_id,
                    'activation_time': datetime.now().isoformat(),
                    'machine_info': {
                        'node': platform.node(),
                        'system': platform.system(),
                        'processor': platform.processor()
                    }
                })
            
            # 保存记录
            os.makedirs(os.path.dirname(global_trial_file), exist_ok=True)
            with open(global_trial_file, 'w', encoding='utf-8') as f:
                json.dump(trial_data, f, indent=2, ensure_ascii=False)
                
            self.logger.info("试用期激活记录已更新")
            
        except Exception as e:
            self.logger.error(f"记录试用期激活时发生错误: {str(e)}")
    
    def _validate_trial_integrity(self) -> bool:
        """验证试用期完整性
        
        Returns:
            bool: 试用期数据是否完整
        """
        try:
            # 1. 检查机器ID是否匹配
            if self.license_info.machine_id:
                current_machine_id = self.get_machine_id()
                if self.license_info.machine_id != current_machine_id:
                    self.logger.warning("试用期机器ID不匹配")
                    return False
            
            # 2. 检查时间是否被篡改
            if self.license_info.trial_start_date:
                # 检查试用开始时间是否合理（不能是未来时间）
                if self.license_info.trial_start_date > datetime.now():
                    self.logger.warning("检测到试用期开始时间异常")
                    return False
                
                # 检查试用期是否过长
                trial_duration = datetime.now() - self.license_info.trial_start_date
                if trial_duration.days > self.config.trial_days + 1:  # 允许1天误差
                    # 进一步检查是否为时间回退
                    expected_end = self.license_info.trial_start_date + timedelta(days=self.license_info.trial_days)
                    if datetime.now() > expected_end:
                        self.logger.warning("试用期已自然过期")
                        return True  # 自然过期是正常的
                    else:
                        self.logger.warning("检测到可能的时间篡改")
                        return False
            
            # 3. 检查全局记录一致性
            try:
                global_trial_file = os.path.expanduser("~/.medical_validator_trial")
                if os.path.exists(global_trial_file):
                    with open(global_trial_file, 'r', encoding='utf-8') as f:
                        trial_data = json.load(f)
                    
                    current_machine_id = self.get_machine_id()
                    if current_machine_id not in trial_data.get('used_machines', []):
                        self.logger.warning("全局试用期记录不一致")
                        return False
            except:
                # 如果全局记录文件有问题，不影响现有试用期的验证
                pass
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证试用期完整性时发生错误: {str(e)}")
            return False
    
    def _start_background_validation(self):
        """启动后台网络验证任务"""
        if not self.config.enable_network_check:
            return
            
        @background_task("network_validation")
        def background_network_validation():
            """后台网络验证任务"""
            try:
                # 模拟网络验证逻辑
                self.logger.debug("执行后台网络验证")
                # 这里可以添加实际的网络验证逻辑
                return True
            except Exception as e:
                self.logger.error(f"后台网络验证失败: {e}")
                return False
        
        # 启动后台验证任务
        task_id = background_network_validation()
        self.logger.debug(f"后台网络验证任务已启动: {task_id}")
    
    @cached_operation("machine_id")
    def get_machine_id_cached(self) -> str:
        """获取缓存的机器ID"""
        return self.get_machine_id()
    
    @cached_operation()
    def validate_license_cached(self) -> Tuple[LicenseStatus, str]:
        """执行缓存的授权验证"""
        with self._validation_lock:
            return self.validate_license()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return self._performance_optimizer.get_performance_report()
    
    def can_work_offline(self) -> bool:
        """检查是否可以离线工作
        
        Returns:
            bool: 是否可以离线工作
        """
        try:
            if not self.license_info.license_code:
                return False
                
            return self._offline_cache.can_work_offline(
                self.license_info.license_code,
                self.get_machine_id()
            )
        except Exception as e:
            self.logger.error(f"检查离线工作能力失败: {e}")
            return False
    
    def get_offline_grace_period(self) -> int:
        """获取剩余的离线宽限期（天数）
        
        Returns:
            int: 剩余天数，-1表示无法确定
        """
        try:
            if not self.license_info.license_code:
                return -1
                
            return self._offline_cache.get_offline_grace_period(
                self.license_info.license_code,
                self.get_machine_id()
            )
        except Exception as e:
            self.logger.error(f"获取离线宽限期失败: {e}")
            return -1
    
    def store_online_verification_result(self, verification_result, server_time=None):
        """存储在线验证结果到离线缓存
        
        Args:
            verification_result: 验证结果
            server_time: 服务器时间（可选）
        """
        try:
            if self.license_info.license_code:
                self._offline_cache.store_license_verification(
                    self.license_info,
                    verification_result,
                    server_time
                )
                self.logger.debug("在线验证结果已存储到离线缓存")
        except Exception as e:
            self.logger.error(f"存储在线验证结果失败: {e}")
    
    def get_cached_verification_result(self):
        """获取缓存的验证结果
        
        Returns:
            tuple: (license_info, verification_result, is_valid)
        """
        try:
            if not self.license_info.license_code:
                return None, None, False
                
            return self._offline_cache.get_cached_license_verification(
                self.license_info.license_code,
                self.get_machine_id()
            )
        except Exception as e:
            self.logger.error(f"获取缓存验证结果失败: {e}")
            return None, None, False
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        try:
            return self._offline_cache.get_cache_stats()
        except Exception as e:
            self.logger.error(f"获取缓存统计失败: {e}")
            return {}
    
    def clear_offline_cache(self):
        """清空离线缓存"""
        try:
            self._offline_cache.clear_all()
            self.logger.info("离线缓存已清空")
        except Exception as e:
            self.logger.error(f"清空离线缓存失败: {e}")
    
    def cleanup_performance(self):
        """清理性能优化资源"""
        try:
            self._performance_optimizer.cleanup()
            self.logger.info("授权管理器性能优化资源清理完成")
        except Exception as e:
            self.logger.error(f"清理性能优化资源失败: {e}")
# -*- coding: utf-8 -*-
"""
PyInstaller运行时hook - 强制加载tkinter.constants
解决 ModuleNotFoundError: No module named 'tkinter.constants' 问题
"""

import sys
import os

# 在程序启动时强制导入tkinter相关模块
try:
    # 强制导入tkinter.constants
    import tkinter.constants
    print("运行时hook: tkinter.constants 加载成功")
    
    # 预加载其他关键tkinter模块
    import tkinter.ttk
    import tkinter.messagebox
    import tkinter.filedialog
    
    print("运行时hook: 所有tkinter模块预加载完成")
    
except ImportError as e:
    print(f"运行时hook警告: tkinter模块加载失败 - {e}")
    # 不要让这个错误阻止程序启动
    pass

# 确保tkinter模块在sys.modules中
if 'tkinter' not in sys.modules:
    try:
        import tkinter
        sys.modules['tkinter'] = tkinter
    except:
        pass

if 'tkinter.constants' not in sys.modules:
    try:
        import tkinter.constants
        sys.modules['tkinter.constants'] = tkinter.constants
    except:
        pass

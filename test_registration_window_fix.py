#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试注册窗口空白窗口修复
"""

import tkinter as tk
from registration_window import show_registration_window

def test_no_blank_window():
    """测试是否还有空白窗口"""
    print("=" * 50)
    print("测试注册窗口空白窗口修复")
    print("=" * 50)
    
    print("启动注册窗口...")
    print("请检查:")
    print("1. 是否只显示一个注册窗口")
    print("2. 是否没有空白窗口")
    print("3. 窗口是否正确居中显示")
    print("4. 窗口关闭后是否所有窗口都消失")
    
    try:
        # 直接调用注册窗口，不创建任何额外的根窗口
        result, license_code = show_registration_window(
            parent=None,
            license_manager=None,
            title="测试注册窗口 - 无空白窗口",
            trial_expired=True
        )
        
        print(f"\n窗口关闭，结果: {result}")
        if license_code:
            print(f"输入的注册码: {license_code}")
        
        print("✓ 测试完成")
        print("如果只看到一个注册窗口且没有空白窗口，说明修复成功")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_no_blank_window()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包验证脚本 - 验证所有模块是否能正确导入和打包
"""

import os
import sys
import importlib
import traceback
from pathlib import Path

def test_core_modules():
    """测试核心模块导入"""
    print("=== 测试核心模块导入 ===")
    
    core_modules = [
        'main',
        'condition',
    ]
    
    success_count = 0
    for module_name in core_modules:
        try:
            module = importlib.import_module(module_name)
            print(f"✓ {module_name} - 导入成功")
            success_count += 1
        except Exception as e:
            print(f"✗ {module_name} - 导入失败: {str(e)}")
    
    print(f"核心模块测试结果: {success_count}/{len(core_modules)} 成功")
    return success_count == len(core_modules)

def test_license_modules():
    """测试授权相关模块导入"""
    print("\n=== 测试授权相关模块导入 ===")
    
    license_modules = [
        'license_manager',
        'license_models', 
        'license_ui',
        'license_restriction',
        'license_generator',
        'license_logger',
        'registration_window',
    ]
    
    success_count = 0
    for module_name in license_modules:
        try:
            module = importlib.import_module(module_name)
            print(f"✓ {module_name} - 导入成功")
            success_count += 1
        except Exception as e:
            print(f"✗ {module_name} - 导入失败: {str(e)}")
            # 打印详细错误信息
            if hasattr(e, '__traceback__'):
                print(f"  详细错误: {traceback.format_exc()}")
    
    print(f"授权模块测试结果: {success_count}/{len(license_modules)} 成功")
    return success_count == len(license_modules)

def test_performance_modules():
    """测试性能优化模块导入"""
    print("\n=== 测试性能优化模块导入 ===")
    
    performance_modules = [
        'performance_optimizer',
        'offline_license_cache',
    ]
    
    success_count = 0
    for module_name in performance_modules:
        try:
            module = importlib.import_module(module_name)
            print(f"✓ {module_name} - 导入成功")
            success_count += 1
        except Exception as e:
            print(f"✗ {module_name} - 导入失败: {str(e)}")
    
    print(f"性能模块测试结果: {success_count}/{len(performance_modules)} 成功")
    return success_count == len(performance_modules)

def test_security_modules():
    """测试安全相关模块导入"""
    print("\n=== 测试安全相关模块导入 ===")
    
    security_modules = [
        'network_client',
        'time_validator', 
        'code_protection',
        'integrity_checker',
        'secure_exit',
    ]
    
    success_count = 0
    for module_name in security_modules:
        try:
            module = importlib.import_module(module_name)
            print(f"✓ {module_name} - 导入成功")
            success_count += 1
        except Exception as e:
            print(f"✗ {module_name} - 导入失败: {str(e)}")
    
    print(f"安全模块测试结果: {success_count}/{len(security_modules)} 成功")
    return success_count == len(security_modules)

def test_third_party_modules():
    """测试第三方库导入"""
    print("\n=== 测试第三方库导入 ===")
    
    third_party_modules = [
        ('tkinter', 'GUI框架'),
        ('openpyxl', 'Excel处理'),
        ('PIL', '图像处理'),
        ('json', 'JSON处理'),
        ('datetime', '日期时间'),
        ('hashlib', '哈希算法'),
        ('uuid', 'UUID生成'),
        ('platform', '平台信息'),
        ('threading', '多线程'),
        ('logging', '日志记录'),
        ('csv', 'CSV处理'),
        ('concurrent.futures', '并发处理'),
        ('functools', '函数工具'),
        ('typing', '类型提示'),
        ('enum', '枚举类型'),
    ]
    
    success_count = 0
    for module_name, description in third_party_modules:
        try:
            module = importlib.import_module(module_name)
            print(f"✓ {module_name} ({description}) - 导入成功")
            success_count += 1
        except Exception as e:
            print(f"✗ {module_name} ({description}) - 导入失败: {str(e)}")
    
    print(f"第三方库测试结果: {success_count}/{len(third_party_modules)} 成功")
    return success_count == len(third_party_modules)

def test_tkinter_specific():
    """专门测试tkinter相关模块"""
    print("\n=== 专门测试tkinter模块 ===")
    
    tkinter_modules = [
        'tkinter',
        'tkinter.constants',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.font',
        'tkinter.scrolledtext',
        'tkinter.simpledialog',
        'tkinter.colorchooser',
        '_tkinter',
    ]
    
    success_count = 0
    for module_name in tkinter_modules:
        try:
            module = importlib.import_module(module_name)
            print(f"✓ {module_name} - 导入成功")
            success_count += 1
        except Exception as e:
            print(f"✗ {module_name} - 导入失败: {str(e)}")
    
    print(f"tkinter模块测试结果: {success_count}/{len(tkinter_modules)} 成功")
    return success_count == len(tkinter_modules)

def check_data_files():
    """检查数据文件是否存在"""
    print("\n=== 检查数据文件 ===")
    
    data_files = [
        ('config.json', '配置文件'),
        ('config.json.enc', '加密配置文件'),
        ('license_database.json', '授权数据库'),
        ('license_database.json.enc', '加密授权数据库'),
        ('license.dat', '授权数据文件'),
        ('icon.png', '图标文件'),
        ('logo.png', 'Logo文件'),
        ('app_icon.ico', '应用图标'),
        ('time_jump_detector.dat', '时间检测文件'),
        ('dev_mode.txt', '开发者模式文件'),
    ]
    
    existing_files = []
    for file_path, description in data_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✓ {file_path} ({description}) - 存在 ({file_size} bytes)")
            existing_files.append(file_path)
        else:
            print(f"⚠ {file_path} ({description}) - 不存在")
    
    print(f"数据文件检查结果: {len(existing_files)}/{len(data_files)} 个文件存在")
    return existing_files

def test_license_functionality():
    """测试授权功能是否正常工作"""
    print("\n=== 测试授权功能 ===")
    
    try:
        from license_manager import LicenseManager
        from license_models import LicenseStatus
        
        # 创建授权管理器
        license_manager = LicenseManager()
        print("✓ 授权管理器创建成功")
        
        # 测试授权验证
        status, message = license_manager.validate_license()
        print(f"✓ 授权验证成功: {status.value} - {message}")
        
        # 测试试用期检查
        trial_status = license_manager.check_trial_period()
        print(f"✓ 试用期检查成功: 活跃={trial_status.is_active}, 剩余天数={trial_status.days_remaining}")
        
        return True
        
    except Exception as e:
        print(f"✗ 授权功能测试失败: {str(e)}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_registration_window():
    """测试注册窗口功能"""
    print("\n=== 测试注册窗口功能 ===")
    
    try:
        from registration_window import RegistrationWindow
        print("✓ 注册窗口模块导入成功")
        
        # 注意：这里不实际创建窗口，只测试类是否可以实例化
        # 因为在无GUI环境下创建tkinter窗口会失败
        print("✓ 注册窗口功能可用")
        return True
        
    except Exception as e:
        print(f"✗ 注册窗口测试失败: {str(e)}")
        return False

def generate_build_report():
    """生成打包报告"""
    print("\n" + "="*60)
    print("                  打包验证报告")
    print("="*60)
    
    # 执行所有测试
    results = {
        "核心模块": test_core_modules(),
        "授权模块": test_license_modules(), 
        "性能模块": test_performance_modules(),
        "安全模块": test_security_modules(),
        "第三方库": test_third_party_modules(),
        "tkinter模块": test_tkinter_specific(),
        "授权功能": test_license_functionality(),
        "注册窗口": test_registration_window(),
    }
    
    # 检查数据文件
    existing_files = check_data_files()
    
    # 生成总结报告
    print("\n" + "="*60)
    print("                    测试总结")
    print("="*60)
    
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:12} : {status}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 项测试通过")
    print(f"数据文件: {len(existing_files)} 个文件可用")
    
    # 生成建议
    print("\n" + "="*60)
    print("                    打包建议")
    print("="*60)
    
    if passed_tests == total_tests:
        print("✓ 所有测试通过，可以进行打包")
        print("建议使用: python build_exe.py")
    else:
        print("⚠ 部分测试失败，建议先解决以下问题:")
        for test_name, result in results.items():
            if not result:
                print(f"  - 修复 {test_name} 相关问题")
    
    # 打包配置建议
    print("\n推荐的打包配置:")
    print("- 使用 医疗数据校验工具_simple.spec 进行打包")
    print("- 确保所有授权相关模块都被包含")
    print("- 包含必要的数据文件和图片资源")
    print("- 启用UPX压缩以减小文件大小")
    
    return passed_tests == total_tests

def main():
    """主函数"""
    print("医疗数据校验工具 - 打包验证脚本")
    print("Python版本:", sys.version)
    print("当前目录:", os.getcwd())
    print()
    
    # 生成完整的验证报告
    success = generate_build_report()
    
    if success:
        print("\n🎉 验证完成！所有功能准备就绪，可以开始打包。")
        
        # 询问是否立即开始打包
        try:
            choice = input("\n是否立即开始打包？(y/n): ").lower().strip()
            if choice == 'y':
                print("开始执行打包...")
                import subprocess
                result = subprocess.run([sys.executable, 'build_exe.py'], 
                                      capture_output=False, text=True)
                if result.returncode == 0:
                    print("✓ 打包完成！")
                else:
                    print("✗ 打包过程中出现错误")
        except KeyboardInterrupt:
            print("\n用户取消操作")
    else:
        print("\n⚠ 验证发现问题，建议先解决后再进行打包。")
    
    return success

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n验证过程中发生错误: {e}")
        print(f"详细错误信息: {traceback.format_exc()}")
    finally:
        input("\n按Enter键退出...")
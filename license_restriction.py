#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
授权限制模块 - 管理未授权状态下的功能限制
"""

import tkinter as tk
from tkinter import messagebox
from functools import wraps
from license_models import LicenseStatus
from license_ui import show_license_input, contact_support

class LicenseRestriction:
    """授权限制管理器 - 试用期到期后强制注册模式"""
    
    def __init__(self, license_manager):
        self.license_manager = license_manager
        self._restricted_functions = set()
        self._restriction_message = "试用期已到期，需要注册激活才能继续使用。\n\n请输入您的授权码以激活软件。"
        self._strict_mode = False  # 允许试用期，但到期后强制注册
        
    def is_licensed(self) -> bool:
        """检查是否已获得正式授权"""
        try:
            status, _ = self.license_manager.validate_license()
            return status == LicenseStatus.VALID
        except:
            return False
    
    def is_trial_active(self) -> bool:
        """检查试用期是否仍然有效 - 严格模式下不允许试用"""
        if self._strict_mode:
            return False  # 严格模式下不允许试用
        try:
            status, _ = self.license_manager.validate_license()
            return status in [LicenseStatus.TRIAL, LicenseStatus.VALID]
        except:
            return False
    
    def show_license_dialog(self, parent=None):
        """显示授权对话框"""
        # 检查试用期状态
        trial_status = self.license_manager.check_trial_period()
        
        if trial_status.is_trial and trial_status.is_active:
            # 试用期内，显示简单的授权对话框
            result = messagebox.askyesnocancel(
                "需要授权",
                f"{self._restriction_message}\n\n"
                "是否现在输入授权码？\n"
                "选择'否'联系技术支持。",
                parent=parent
            )
            
            if result is True:  # 是 - 输入授权码
                license_code = show_license_input(parent)
                if license_code:
                    success, msg = self.license_manager.activate_license(license_code)
                    if success:
                        messagebox.showinfo("激活成功", "授权激活成功！现在可以使用所有功能。", parent=parent)
                        return True
                    else:
                        messagebox.showerror("激活失败", f"授权激活失败：{msg}", parent=parent)
            elif result is False:  # 否 - 联系支持
                contact_support()
        else:
            # 试用期已过期，显示专门的注册窗口
            from registration_window import show_registration_window
            result, license_code = show_registration_window(parent, self.license_manager, "软件注册 - 试用期已到期")
            
            if result == "success":
                return True
            elif result == "exit":
                import sys
                sys.exit()
        
        return False
    
    def require_license(self, func):
        """装饰器：要求正式授权才能执行的功能"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            if self.is_licensed():
                return func(*args, **kwargs)
            else:
                # 获取父窗口（如果可能）
                parent = None
                if args and hasattr(args[0], 'root'):
                    parent = args[0].root
                
                if self.show_license_dialog(parent):
                    return func(*args, **kwargs)
                return None
        return wrapper
    
    def require_trial_or_license(self, func):
        """装饰器：严格模式下也要求正式授权才能执行的功能"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            if self._strict_mode:
                # 严格模式下，所有功能都需要正式授权
                if self.is_licensed():
                    return func(*args, **kwargs)
                else:
                    # 获取父窗口（如果可能）
                    parent = None
                    if args and hasattr(args[0], 'root'):
                        parent = args[0].root
                    
                    messagebox.showerror(
                        "需要授权",
                        "此软件需要有效的授权码才能使用。\n\n请输入您的授权码以激活软件。",
                        parent=parent
                    )
                    
                    if self.show_license_dialog(parent):
                        return func(*args, **kwargs)
                    return None
            else:
                # 非严格模式下的原有逻辑
                if self.is_trial_active():
                    return func(*args, **kwargs)
                else:
                    # 获取父窗口（如果可能）
                    parent = None
                    if args and hasattr(args[0], 'root'):
                        parent = args[0].root
                    
                    messagebox.showerror(
                        "授权已过期",
                        "试用期已过期，需要正式授权才能继续使用。\n\n请联系我们获取授权码。",
                        parent=parent
                    )
                    
                    if self.show_license_dialog(parent):
                        return func(*args, **kwargs)
                    return None
        return wrapper
    
    def limit_trial_usage(self, func, max_uses=10):
        """装饰器：限制试用版的使用次数"""
        usage_count = 0
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal usage_count
            
            if self.is_licensed():
                return func(*args, **kwargs)
            
            if not self.is_trial_active():
                parent = None
                if args and hasattr(args[0], 'root'):
                    parent = args[0].root
                
                messagebox.showerror(
                    "授权已过期",
                    "试用期已过期，需要正式授权才能继续使用。",
                    parent=parent
                )
                return None
            
            usage_count += 1
            if usage_count > max_uses:
                parent = None
                if args and hasattr(args[0], 'root'):
                    parent = args[0].root
                
                result = messagebox.askyesno(
                    "试用限制",
                    f"试用版此功能最多只能使用{max_uses}次。\n\n"
                    "是否现在购买正式版本以解除限制？",
                    parent=parent
                )
                
                if result:
                    if self.show_license_dialog(parent):
                        return func(*args, **kwargs)
                return None
            
            # 在接近限制时显示警告
            if usage_count >= max_uses - 2:
                parent = None
                if args and hasattr(args[0], 'root'):
                    parent = args[0].root
                
                messagebox.showwarning(
                    "试用提醒",
                    f"试用版此功能还可以使用{max_uses - usage_count}次。\n\n"
                    "购买正式版本可解除所有限制。",
                    parent=parent
                )
            
            return func(*args, **kwargs)
        
        return wrapper
    
    def show_watermark_message(self, parent=None):
        """显示试用版水印信息"""
        if not self.is_licensed() and self.is_trial_active():
            trial_status = self.license_manager.check_trial_period()
            messagebox.showinfo(
                "试用版提醒",
                f"您正在使用试用版，还剩{trial_status.days_remaining}天。\n\n"
                "试用版生成的报告将包含水印标识。\n"
                "购买正式版本可移除水印并获得完整功能。",
                parent=parent
            )

# 全局限制管理器实例
_restriction_manager = None

def init_license_restriction(license_manager):
    """初始化授权限制管理器"""
    global _restriction_manager
    _restriction_manager = LicenseRestriction(license_manager)
    return _restriction_manager

def get_license_restriction():
    """获取授权限制管理器实例"""
    return _restriction_manager

def require_license(func):
    """装饰器：要求正式授权"""
    if _restriction_manager:
        return _restriction_manager.require_license(func)
    return func

def require_trial_or_license(func):
    """装饰器：要求试用期或正式授权"""
    if _restriction_manager:
        return _restriction_manager.require_trial_or_license(func)
    return func

def limit_trial_usage(max_uses=10):
    """装饰器工厂：限制试用版使用次数"""
    def decorator(func):
        if _restriction_manager:
            return _restriction_manager.limit_trial_usage(func, max_uses)
        return func
    return decorator
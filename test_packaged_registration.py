#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包后程序的注册窗口功能
"""

import os
import sys
import subprocess
import time

def test_packaged_exe():
    """测试打包后的可执行文件"""
    print("=" * 50)
    print("测试打包后程序的注册窗口功能")
    print("=" * 50)
    
    exe_path = os.path.join("dist", "医疗数据校验工具.exe")
    
    if not os.path.exists(exe_path):
        print(f"✗ 可执行文件不存在: {exe_path}")
        return False
    
    print(f"✓ 找到可执行文件: {exe_path}")
    print(f"✓ 文件大小: {os.path.getsize(exe_path) / (1024*1024):.2f} MB")
    
    # 检查必要的资源文件
    required_files = [
        "dist/icon.png",
        "dist/logo.png", 
        "dist/config.json",
        "dist/license_database.json",
        "dist/license.dat"
    ]
    
    print("\n检查资源文件:")
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - 缺失")
    
    print("\n启动程序进行测试...")
    print("注意：程序将启动，请检查注册窗口是否正常显示")
    print("如果注册窗口显示正常，请关闭程序")
    
    try:
        # 启动程序
        process = subprocess.Popen([exe_path], cwd="dist")
        
        print("程序已启动，PID:", process.pid)
        print("请检查:")
        print("1. 程序是否正常启动")
        print("2. 界面是否正确显示")
        print("3. 图标和logo是否正常显示")
        print("4. 注册窗口功能是否正常")
        
        # 等待用户手动测试
        input("\n按Enter键继续（请先关闭程序）...")
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("程序仍在运行，尝试终止...")
            process.terminate()
            time.sleep(2)
            if process.poll() is None:
                process.kill()
        
        print("✓ 程序测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 启动程序失败: {e}")
        return False

def check_registration_window_integration():
    """检查注册窗口集成情况"""
    print("\n" + "=" * 50)
    print("检查注册窗口集成情况")
    print("=" * 50)
    
    # 检查spec文件中的注册窗口模块
    spec_file = "医疗数据校验工具_simple.spec"
    if os.path.exists(spec_file):
        with open(spec_file, 'r', encoding='utf-8') as f:
            spec_content = f.read()
            
        if 'registration_window' in spec_content:
            print("✓ 注册窗口模块已包含在spec文件中")
        else:
            print("✗ 注册窗口模块未包含在spec文件中")
            
        # 检查其他相关模块
        required_modules = [
            'license_manager',
            'license_models', 
            'license_ui',
            'PIL.Image',
            'PIL.ImageTk'
        ]
        
        print("\n检查相关模块:")
        for module in required_modules:
            if module in spec_content:
                print(f"✓ {module}")
            else:
                print(f"✗ {module} - 缺失")
    else:
        print(f"✗ spec文件不存在: {spec_file}")

def main():
    """主函数"""
    print("开始测试打包后的注册窗口功能...")
    
    # 检查集成情况
    check_registration_window_integration()
    
    # 测试可执行文件
    success = test_packaged_exe()
    
    print("\n" + "=" * 50)
    if success:
        print("✓ 测试完成！")
        print("注册窗口应该与主程序中的注册窗口保持一致")
        print("包括:")
        print("- 相同的界面布局和设计")
        print("- 相同的图标和logo显示")
        print("- 相同的功能和交互逻辑")
        print("- 相同的错误处理和提示信息")
    else:
        print("✗ 测试失败！")
        print("请检查打包配置和资源文件")
    print("=" * 50)

if __name__ == "__main__":
    main()
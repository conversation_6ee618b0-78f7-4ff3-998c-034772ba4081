# 软件授权管理功能设计文档

## 概述

本设计文档描述了医疗数据校验工具的软件授权管理系统的技术架构和实现方案。该系统将替换现有的简单日期检查机制，提供一个安全、灵活、用户友好的授权管理解决方案。

系统采用模块化设计，包含授权验证引擎、配置管理器、用户界面组件、日志系统和网络通信模块。通过多层次的安全验证和友好的用户交互，确保软件的商业价值得到保护，同时提供良好的用户体验。

## 架构

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    主应用程序 (main.py)                      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                授权管理器 (LicenseManager)                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ 验证引擎    │  │ 配置管理器  │  │ 用户界面组件        │  │
│  │ (Validator) │  │ (Config)    │  │ (UI Components)     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ 时间验证器  │  │ 网络客户端  │  │ 日志记录器          │  │
│  │ (TimeCheck) │  │ (NetClient) │  │ (Logger)            │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                    外部依赖                                 │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ 配置文件    │  │ 授权服务器  │  │ 系统时间/网络时间   │  │
│  │ (Config)    │  │ (Server)    │  │ (Time Sources)      │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

1. **授权管理器 (LicenseManager)** - 核心控制器
2. **验证引擎 (LicenseValidator)** - 执行各种验证逻辑
3. **配置管理器 (ConfigManager)** - 管理配置文件和参数
4. **时间验证器 (TimeValidator)** - 验证系统时间的准确性
5. **网络客户端 (NetworkClient)** - 处理网络授权验证
6. **用户界面组件 (UIComponents)** - 授权相关的UI组件
7. **强制注册窗口 (RegistrationWindow)** - 试用期到期后的强制注册界面
8. **日志记录器 (LicenseLogger)** - 专门的授权日志记录

## 组件和接口

### 1. 授权管理器 (LicenseManager)

```python
class LicenseManager:
    """授权管理器 - 系统核心控制器"""
    
    def __init__(self, config_path: str = None):
        """初始化授权管理器"""
        
    def validate_license(self) -> LicenseStatus:
        """执行完整的授权验证流程"""
        
    def check_trial_period(self) -> TrialStatus:
        """检查试用期状态"""
        
    def activate_license(self, license_code: str) -> bool:
        """激活授权码"""
        
    def get_license_info(self) -> LicenseInfo:
        """获取当前授权信息"""
        
    def show_license_dialog(self) -> None:
        """显示授权对话框"""
```

### 2. 验证引擎 (LicenseValidator)

```python
class LicenseValidator:
    """授权验证引擎"""
    
    def validate_expiration(self, expiry_date: datetime) -> bool:
        """验证过期日期"""
        
    def validate_license_code(self, code: str) -> ValidationResult:
        """验证授权码格式和有效性"""
        
    def validate_file_integrity(self, file_path: str) -> bool:
        """验证配置文件完整性"""
        
    def validate_hardware_binding(self) -> bool:
        """验证硬件绑定"""
        
    def generate_machine_fingerprint(self) -> str:
        """生成机器指纹"""
```

### 3. 配置管理器 (ConfigManager)

```python
class ConfigManager:
    """配置管理器"""
    
    def load_config(self) -> LicenseConfig:
        """加载授权配置"""
        
    def save_config(self, config: LicenseConfig) -> bool:
        """保存授权配置"""
        
    def encrypt_config(self, data: dict) -> bytes:
        """加密配置数据"""
        
    def decrypt_config(self, encrypted_data: bytes) -> dict:
        """解密配置数据"""
        
    def create_default_config(self) -> LicenseConfig:
        """创建默认配置"""
```

### 4. 时间验证器 (TimeValidator)

```python
class TimeValidator:
    """时间验证器"""
    
    def get_network_time(self) -> datetime:
        """获取网络时间"""
        
    def get_system_time(self) -> datetime:
        """获取系统时间"""
        
    def validate_time_consistency(self) -> TimeValidationResult:
        """验证时间一致性"""
        
    def detect_time_manipulation(self) -> bool:
        """检测时间篡改"""
        
    def update_time_checkpoint(self) -> None:
        """更新时间检查点"""
```

### 5. 网络客户端 (NetworkClient)

```python
class NetworkClient:
    """网络授权客户端"""
    
    def verify_online_license(self, license_info: LicenseInfo) -> OnlineVerificationResult:
        """在线验证授权"""
        
    def check_license_revocation(self, license_id: str) -> bool:
        """检查授权撤销状态"""
        
    def submit_usage_statistics(self, stats: UsageStats) -> bool:
        """提交使用统计"""
        
    def download_license_updates(self) -> List[LicenseUpdate]:
        """下载授权更新"""
```

## 数据模型

### 授权状态枚举

```python
class LicenseStatus(Enum):
    VALID = "valid"                    # 有效
    EXPIRED = "expired"                # 已过期
    TRIAL = "trial"                    # 试用期
    TRIAL_EXPIRED = "trial_expired"    # 试用期过期
    INVALID = "invalid"                # 无效
    NETWORK_ERROR = "network_error"    # 网络错误
    TIME_ERROR = "time_error"          # 时间错误
```

### 授权信息数据结构

```python
@dataclass
class LicenseInfo:
    license_type: str              # 授权类型 (trial/full/enterprise)
    expiry_date: datetime          # 过期日期
    license_code: str              # 授权码
    machine_id: str                # 机器ID
    user_name: str                 # 用户名
    company_name: str              # 公司名称
    features: List[str]            # 可用功能列表
    max_users: int                 # 最大用户数
    created_date: datetime         # 创建日期
    last_verified: datetime        # 最后验证时间
```

### 配置数据结构

```python
@dataclass
class LicenseConfig:
    server_url: str                # 授权服务器URL
    check_interval: int            # 检查间隔(小时)
    trial_days: int                # 试用天数
    warning_days: int              # 提前警告天数
    max_offline_days: int          # 最大离线天数
    enable_hardware_binding: bool  # 启用硬件绑定
    enable_network_check: bool     # 启用网络检查
    log_level: str                 # 日志级别
```

## 错误处理

### 错误类型定义

```python
class LicenseError(Exception):
    """授权相关错误基类"""
    pass

class LicenseExpiredError(LicenseError):
    """授权过期错误"""
    pass

class InvalidLicenseCodeError(LicenseError):
    """无效授权码错误"""
    pass

class TimeValidationError(LicenseError):
    """时间验证错误"""
    pass

class NetworkValidationError(LicenseError):
    """网络验证错误"""
    pass

class ConfigurationError(LicenseError):
    """配置错误"""
    pass
```

### 错误处理策略

1. **授权过期**：显示友好提示，提供续期选项
2. **网络错误**：使用本地缓存，记录错误日志
3. **时间异常**：警告用户，尝试网络时间同步
4. **配置错误**：使用默认配置，记录警告日志
5. **严重错误**：安全退出，记录详细错误信息

## 测试策略

### 单元测试

1. **授权验证逻辑测试**
   - 正常授权验证
   - 过期日期验证
   - 授权码格式验证
   - 硬件绑定验证

2. **时间验证测试**
   - 系统时间获取
   - 网络时间获取
   - 时间一致性验证
   - 时间篡改检测

3. **配置管理测试**
   - 配置文件加载
   - 配置文件加密/解密
   - 默认配置生成
   - 配置文件完整性验证

### 集成测试

1. **完整授权流程测试**
   - 首次启动试用激活
   - 授权码激活流程
   - 过期处理流程
   - 网络验证流程

2. **异常场景测试**
   - 网络断开情况
   - 系统时间修改
   - 配置文件损坏
   - 授权服务器不可用

### 安全测试

1. **绕过尝试测试**
   - 系统时间修改
   - 配置文件篡改
   - 网络拦截
   - 进程注入

2. **性能测试**
   - 启动时间影响
   - 内存使用情况
   - 网络请求性能
   - 大量并发验证

## 安全考虑

### 数据保护

1. **配置文件加密**：使用AES-256加密存储敏感配置
2. **授权码混淆**：对授权码进行混淆处理
3. **机器指纹**：生成唯一的机器标识
4. **完整性校验**：使用哈希值验证文件完整性

### 防篡改措施

1. **多重验证**：结合时间、网络、本地多种验证方式
2. **随机检查**：在程序运行期间随机进行授权检查
3. **行为监控**：监控异常的系统行为
4. **日志保护**：保护日志文件不被恶意修改

### 网络安全

1. **HTTPS通信**：所有网络通信使用HTTPS加密
2. **证书验证**：验证服务器SSL证书
3. **请求签名**：对关键请求进行数字签名
4. **防重放攻击**：使用时间戳和随机数防止重放

## 性能优化

### 启动性能

1. **异步验证**：将耗时的网络验证放在后台执行
2. **缓存机制**：缓存验证结果，减少重复计算
3. **延迟加载**：按需加载授权组件
4. **并行处理**：并行执行多个验证步骤

### 运行时性能

1. **定时检查**：合理设置检查间隔，避免频繁验证
2. **内存管理**：及时释放不需要的资源
3. **日志优化**：控制日志输出量，使用异步写入
4. **网络优化**：使用连接池，减少网络开销

## 部署考虑

### 配置部署

1. **默认配置**：提供合理的默认配置参数
2. **环境适配**：支持不同操作系统环境
3. **权限管理**：确保配置文件的适当权限
4. **备份恢复**：提供配置备份和恢复机制

### 更新机制

1. **在线更新**：支持授权配置的在线更新
2. **版本兼容**：保持向后兼容性
3. **回滚机制**：支持配置回滚
4. **通知机制**：及时通知用户重要更新
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
授权管理用户界面组件
提供授权相关的对话框和界面元素
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime
from typing import Optional, Callable
import webbrowser
import logging

from license_models import LicenseInfo, LicenseStatus, TrialStatus


class LicenseDialog:
    """授权对话框基类"""
    
    def __init__(self, parent=None, title="授权管理"):
        self.parent = parent
        self.title = title
        self.result = None
        self.dialog = None
    
    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self._center_window()
        
        # 创建界面
        self._create_widgets()
        
        # 等待用户操作
        self.dialog.wait_window()
        
        return self.result
    
    def _center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面元素 - 子类实现"""
        pass
    
    def _close_dialog(self, result=None):
        """关闭对话框"""
        self.result = result
        if self.dialog:
            self.dialog.destroy()


class LicenseCodeDialog(LicenseDialog):
    """授权码输入对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent, "输入授权码")
        self.license_code = ""
    
    def _create_widgets(self):
        """创建授权码输入界面"""
        self.dialog.geometry("450x300")
        
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="请输入授权码", 
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 说明文字
        info_text = """请输入您购买的软件授权码来激活完整版功能。
授权码格式：XXXX-XXXX-XXXX-XXXX"""
        
        info_label = ttk.Label(
            main_frame, 
            text=info_text, 
            justify=tk.CENTER,
            foreground="gray"
        )
        info_label.pack(pady=(0, 20))
        
        # 授权码输入框
        code_frame = ttk.Frame(main_frame)
        code_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(code_frame, text="授权码:").pack(anchor=tk.W)
        
        self.code_entry = ttk.Entry(
            code_frame, 
            font=("Courier", 12),
            width=30
        )
        self.code_entry.pack(fill=tk.X, pady=(5, 0))
        self.code_entry.focus()
        
        # 绑定回车键
        self.code_entry.bind('<Return>', lambda e: self._activate_license())
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        # 激活按钮
        activate_btn = ttk.Button(
            button_frame, 
            text="激活授权", 
            command=self._activate_license
        )
        activate_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 取消按钮
        cancel_btn = ttk.Button(
            button_frame, 
            text="取消", 
            command=lambda: self._close_dialog(None)
        )
        cancel_btn.pack(side=tk.RIGHT)
        
        # 购买链接
        buy_frame = ttk.Frame(main_frame)
        buy_frame.pack(fill=tk.X, pady=(20, 0))
        
        buy_label = ttk.Label(
            buy_frame, 
            text="还没有授权码？", 
            foreground="gray"
        )
        buy_label.pack(side=tk.LEFT)
        
        buy_link = ttk.Label(
            buy_frame, 
            text="点击购买", 
            foreground="blue", 
            cursor="hand2"
        )
        buy_link.pack(side=tk.LEFT, padx=(5, 0))
        buy_link.bind("<Button-1>", self._open_purchase_page)
    
    def _activate_license(self):
        """激活授权码"""
        license_code = self.code_entry.get().strip().upper()
        
        if not license_code:
            messagebox.showwarning("输入错误", "请输入授权码")
            return
        
        # 简单的格式验证
        if len(license_code) < 16:
            messagebox.showwarning("格式错误", "授权码格式不正确")
            return
        
        self._close_dialog(license_code)
    
    def _open_purchase_page(self, event):
        """打开购买页面"""
        try:
            webbrowser.open("https://example.com/purchase")
        except:
            messagebox.showinfo("购买信息", "请联系客服购买授权码\n邮箱: <EMAIL>")


class ExpirationWarningDialog(LicenseDialog):
    """过期警告对话框"""
    
    def __init__(self, parent=None, days_left=0, is_trial=False):
        self.days_left = days_left
        self.is_trial = is_trial
        title = "试用期提醒" if is_trial else "授权到期提醒"
        super().__init__(parent, title)
    
    def _create_widgets(self):
        """创建过期警告界面"""
        self.dialog.geometry("400x250")
        
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 警告图标和标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 警告消息
        if self.is_trial:
            if self.days_left <= 0:
                message = "试用期已结束"
                detail = "请购买正式授权以继续使用完整功能"
            else:
                message = f"试用期还剩 {self.days_left} 天"
                detail = "请及时购买正式授权以避免功能受限"
        else:
            if self.days_left <= 0:
                message = "授权已过期"
                detail = "请续费以继续使用软件"
            else:
                message = f"授权将在 {self.days_left} 天后过期"
                detail = "请及时续费以避免服务中断"
        
        # 主要消息
        msg_label = ttk.Label(
            main_frame, 
            text=message, 
            font=("Arial", 14, "bold"),
            foreground="red" if self.days_left <= 0 else "orange"
        )
        msg_label.pack(pady=(0, 10))
        
        # 详细信息
        detail_label = ttk.Label(
            main_frame, 
            text=detail, 
            justify=tk.CENTER,
            foreground="gray"
        )
        detail_label.pack(pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        # 输入授权码按钮
        license_btn = ttk.Button(
            button_frame, 
            text="输入授权码", 
            command=lambda: self._close_dialog("license")
        )
        license_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 联系客服按钮
        contact_btn = ttk.Button(
            button_frame, 
            text="联系客服", 
            command=lambda: self._close_dialog("contact")
        )
        contact_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 稍后提醒按钮
        later_btn = ttk.Button(
            button_frame, 
            text="稍后提醒", 
            command=lambda: self._close_dialog("later")
        )
        later_btn.pack(side=tk.RIGHT)


class LicenseStatusWidget:
    """授权状态显示组件"""
    
    def __init__(self, parent, license_manager=None):
        self.parent = parent
        self.license_manager = license_manager
        self.status_var = tk.StringVar()
        self.detail_var = tk.StringVar()
        
        self._create_widget()
        self._update_status()
    
    def _create_widget(self):
        """创建状态显示组件"""
        # 使用Frame，设置足够的宽度确保文字显示完整
        self.status_frame = ttk.Frame(self.parent, width=280)
        self.status_frame.pack_propagate(False)  # 防止Frame自动调整大小
        
        # 状态标签 - 使用合适的字体大小
        self.status_label = ttk.Label(
            self.status_frame, 
            textvariable=self.status_var,
            font=("微软雅黑", 9, "bold"),
            anchor="e"
        )
        self.status_label.pack(anchor=tk.E, fill=tk.X, padx=5)
        
        # 详细信息标签 - 显示完整的时间信息，支持换行
        self.detail_label = ttk.Label(
            self.status_frame, 
            textvariable=self.detail_var,
            font=("微软雅黑", 8),
            foreground="gray",
            anchor="e",
            justify="right",
            wraplength=270  # 设置换行宽度，确保长文本能够显示
        )
        self.detail_label.pack(anchor=tk.E, pady=(2, 0), fill=tk.X, padx=5)
        
        # 管理按钮
        self.manage_btn = ttk.Button(
            self.status_frame, 
            text="授权管理", 
            command=self._show_license_dialog,
            width=8
        )
        self.manage_btn.pack(anchor=tk.E, pady=(5, 0), padx=5)
    
    def pack(self, **kwargs):
        """打包组件"""
        self.status_frame.pack(**kwargs)
    
    def _update_status(self):
        """更新状态显示"""
        if not self.license_manager:
            self.status_var.set("状态: 未知")
            self.detail_var.set("授权管理器未初始化")
            return
        
        try:
            status, message = self.license_manager.validate_license()
            license_info = self.license_manager.get_license_info()
            
            print(f"[DEBUG] 授权状态更新: {status.value}, 消息: {message}")  # 调试信息
            
            # 检查是否有正式授权码（不是试用版）
            has_valid_license = (license_info.license_code and 
                               not license_info.is_trial() and 
                               status == LicenseStatus.VALID)
            
            # 设置状态文本 - 如果有正式授权，显示"授权版"
            if has_valid_license:
                status_text = "状态: 授权版"
            else:
                status_text = {
                    LicenseStatus.VALID: "状态: 已授权",
                    LicenseStatus.TRIAL: "状态: 试用版", 
                    LicenseStatus.EXPIRED: "状态: 已过期",
                    LicenseStatus.TRIAL_EXPIRED: "状态: 试用期已过期",
                    LicenseStatus.INVALID: "状态: 无效"
                }.get(status, "状态: 未知")
            
            self.status_var.set(status_text)
            print(f"[DEBUG] 状态文本设置为: {status_text}")
            
            # 构建详细信息，包含时间信息
            detail_text = ""
            
            # 添加具体的时间信息
            if has_valid_license:
                # 正式授权版 - 显示授权时间信息
                if license_info.expiry_date:
                    expiry_date_str = license_info.expiry_date.strftime('%Y年%m月%d日')
                    days_left = license_info.days_until_expiry()
                    detail_text = f"授权至: {expiry_date_str} (剩余{days_left}天)"
                else:
                    detail_text = "正式授权版本"
            elif status == LicenseStatus.TRIAL:
                try:
                    trial_status = self.license_manager.check_trial_period()
                    if trial_status and trial_status.end_date:
                        end_date_str = trial_status.end_date.strftime('%Y年%m月%d日')
                        detail_text = f"试用期至: {end_date_str} (剩余{trial_status.days_remaining}天)"
                        print(f"[DEBUG] 试用期详细信息: {detail_text}")
                except Exception as e:
                    detail_text = message
                    print(f"[DEBUG] 试用期信息获取失败: {e}")
            elif status == LicenseStatus.VALID and license_info.expiry_date:
                expiry_date_str = license_info.expiry_date.strftime('%Y年%m月%d日')
                days_left = license_info.days_until_expiry()
                detail_text = f"有效期至: {expiry_date_str} (剩余{days_left}天)"
            elif status == LicenseStatus.EXPIRED and license_info.expiry_date:
                expiry_date_str = license_info.expiry_date.strftime('%Y年%m月%d日')
                detail_text = f"已于 {expiry_date_str} 过期"
            elif status == LicenseStatus.TRIAL_EXPIRED:
                try:
                    trial_status = self.license_manager.check_trial_period()
                    if trial_status and trial_status.end_date:
                        end_date_str = trial_status.end_date.strftime('%Y年%m月%d日')
                        detail_text = f"试用期于 {end_date_str} 结束"
                except Exception as e:
                    detail_text = message
            else:
                detail_text = message
            
            if not detail_text:
                detail_text = message
            
            self.detail_var.set(detail_text)
            print(f"[DEBUG] 详细信息设置为: {detail_text}")
            
            # 设置颜色
            color = {
                LicenseStatus.VALID: "green",
                LicenseStatus.TRIAL: "blue",
                LicenseStatus.EXPIRED: "red",
                LicenseStatus.TRIAL_EXPIRED: "red",
                LicenseStatus.INVALID: "red"
            }.get(status, "black")
            
            self.status_label.configure(foreground=color)
            
        except Exception as e:
            error_msg = f"获取状态失败: {str(e)}"
            print(f"[DEBUG] 状态更新异常: {error_msg}")
            self.status_var.set("状态: 错误")
            self.detail_var.set(error_msg)
    
    def _show_license_dialog(self):
        """显示授权管理对话框"""
        if not self.license_manager:
            messagebox.showerror("错误", "授权管理器未初始化")
            return
        
        # 显示授权码输入对话框
        dialog = LicenseCodeDialog(self.parent)
        license_code = dialog.show()
        
        if license_code:
            # 尝试激活授权码
            success, message = self.license_manager.activate_license(license_code)
            
            if success:
                messagebox.showinfo("成功", message)
                self._update_status()  # 更新状态显示
            else:
                messagebox.showerror("失败", message)
    
    def refresh(self):
        """刷新状态显示"""
        self._update_status()


def show_license_warning(parent, days_left, is_trial=False):
    """显示授权警告对话框
    
    Args:
        parent: 父窗口
        days_left: 剩余天数
        is_trial: 是否为试用版
        
    Returns:
        str: 用户选择 ('license', 'contact', 'later', None)
    """
    dialog = ExpirationWarningDialog(parent, days_left, is_trial)
    return dialog.show()


def show_license_input(parent):
    """显示授权码输入对话框
    
    Args:
        parent: 父窗口
        
    Returns:
        str: 授权码或None
    """
    dialog = LicenseCodeDialog(parent)
    return dialog.show()


def contact_support():
    """联系技术支持"""
    contact_info = """技术支持联系方式：

邮箱: <EMAIL>
电话: 400-123-4567
QQ群: 123456789

工作时间: 周一至周五 9:00-18:00"""
    
    messagebox.showinfo("联系客服", contact_info)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller hook for tkinter module
确保所有tkinter子模块都被正确包含
"""

from PyInstaller.utils.hooks import collect_submodules

# 收集所有tkinter子模块
hiddenimports = collect_submodules('tkinter')

# 确保包含关键的tkinter模块
critical_tkinter_modules = [
    'tkinter',
    'tkinter.constants',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'tkinter.font',
    'tkinter.scrolledtext',
    'tkinter.simpledialog',
    'tkinter.colorchooser',
    'tkinter.commondialog',
    'tkinter.dnd'
]

# 添加关键模块到hiddenimports（如果还没有的话）
for module in critical_tkinter_modules:
    if module not in hiddenimports:
        hiddenimports.append(module)
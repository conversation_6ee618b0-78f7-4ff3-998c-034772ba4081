#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化功能测试脚本
"""

import time
import logging
from performance_optimizer import (
    get_performance_optimizer, optimize_startup, cached_operation, background_task
)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_startup_optimization():
    """测试启动性能优化"""
    print("=== 测试启动性能优化 ===")
    
    @optimize_startup
    def mock_application_startup():
        """模拟应用启动"""
        time.sleep(0.1)  # 模拟启动时间
        return "应用启动完成"
    
    start_time = time.time()
    result = mock_application_startup()
    end_time = time.time()
    
    print(f"启动结果: {result}")
    print(f"启动耗时: {end_time - start_time:.3f}秒")
    
    # 获取性能报告
    optimizer = get_performance_optimizer()
    print("启动性能报告:")
    print(optimizer.profiler.get_report())

def test_caching():
    """测试缓存功能"""
    print("\n=== 测试缓存功能 ===")
    
    @cached_operation("test_cache")
    def expensive_operation(n):
        """模拟耗时操作"""
        time.sleep(0.1)
        return n * n
    
    # 第一次调用（应该缓存）
    start_time = time.time()
    result1 = expensive_operation(5)
    time1 = time.time() - start_time
    print(f"第一次调用结果: {result1}, 耗时: {time1:.3f}秒")
    
    # 第二次调用（应该从缓存获取）
    start_time = time.time()
    result2 = expensive_operation(5)
    time2 = time.time() - start_time
    print(f"第二次调用结果: {result2}, 耗时: {time2:.3f}秒")
    
    print(f"缓存效果: 第二次调用比第一次快 {(time1 - time2) / time1 * 100:.1f}%")

def test_background_tasks():
    """测试后台任务"""
    print("\n=== 测试后台任务 ===")
    
    @background_task("test_background")
    def background_operation(data):
        """模拟后台操作"""
        time.sleep(0.2)
        return f"处理完成: {data}"
    
    # 提交后台任务
    task_id = background_operation("测试数据")
    print(f"后台任务已提交: {task_id}")
    
    # 获取任务管理器
    optimizer = get_performance_optimizer()
    
    # 等待任务完成
    time.sleep(0.3)
    
    try:
        result = optimizer.background_manager.get_task_result(task_id, timeout=1.0)
        print(f"后台任务结果: {result}")
    except Exception as e:
        print(f"获取后台任务结果失败: {e}")

def test_smart_cache():
    """测试智能缓存"""
    print("\n=== 测试智能缓存 ===")
    
    optimizer = get_performance_optimizer()
    
    @optimizer.smart_cached_operation("smart_test")
    def smart_operation(x):
        """智能缓存操作"""
        time.sleep(0.05)
        return x ** 2
    
    # 测试多次调用
    for i in range(3):
        start_time = time.time()
        result = smart_operation(10)
        elapsed = time.time() - start_time
        print(f"调用 {i+1}: 结果={result}, 耗时={elapsed:.3f}秒")
    
    # 显示智能缓存统计
    cache_stats = optimizer.smart_cache.get_stats()
    print(f"智能缓存统计: {cache_stats}")

def test_memory_optimization():
    """测试内存优化"""
    print("\n=== 测试内存优化 ===")
    
    optimizer = get_performance_optimizer()
    
    # 获取优化前的内存统计
    before_stats = optimizer.memory_optimizer.get_memory_stats()
    print(f"优化前内存统计: {before_stats}")
    
    # 创建一些对象进行跟踪
    test_objects = []
    for i in range(100):
        obj = {"id": i, "data": f"test_data_{i}"}
        optimizer.memory_optimizer.track_object(obj)
        test_objects.append(obj)
    
    # 执行内存优化
    optimization_result = optimizer.memory_optimizer.optimize_memory()
    print(f"内存优化结果: {optimization_result}")
    
    # 获取优化后的内存统计
    after_stats = optimizer.memory_optimizer.get_memory_stats()
    print(f"优化后内存统计: {after_stats}")

def test_batch_processing():
    """测试批处理"""
    print("\n=== 测试批处理 ===")
    
    optimizer = get_performance_optimizer()
    
    processed_items = []
    
    def batch_processor(items):
        """批处理函数"""
        processed_items.extend([f"processed_{item}" for item in items])
        print(f"批处理了 {len(items)} 个项目")
    
    # 添加项目到批处理队列
    for i in range(15):
        optimizer.batch_processor.add_to_batch("test_batch", f"item_{i}", batch_processor)
    
    # 等待批处理完成
    time.sleep(2)
    
    # 强制处理剩余批次
    optimizer.batch_processor.flush_all()
    
    print(f"总共处理了 {len(processed_items)} 个项目")
    print(f"批处理统计: {optimizer.batch_processor.get_batch_stats()}")

def test_performance_report():
    """测试性能报告"""
    print("\n=== 性能报告 ===")
    
    optimizer = get_performance_optimizer()
    report = optimizer.get_performance_report()
    
    print("完整性能报告:")
    for key, value in report.items():
        print(f"{key}: {value}")

def main():
    """主测试函数"""
    print("开始性能优化功能测试...")
    
    try:
        test_startup_optimization()
        test_caching()
        test_background_tasks()
        test_smart_cache()
        test_memory_optimization()
        test_batch_processing()
        test_performance_report()
        
        print("\n所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        optimizer = get_performance_optimizer()
        optimizer.cleanup()
        print("资源清理完成")

if __name__ == "__main__":
    main()
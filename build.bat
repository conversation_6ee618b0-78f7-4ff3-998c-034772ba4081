@echo off
chcp 65001 >nul
title 医疗数据校验工具 - 打包脚本

echo.
echo ================================================
echo          医疗数据校验工具 - 打包脚本
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ✗ Python未安装或未添加到PATH环境变量
    echo 请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

echo ✓ 检测到Python环境

REM 检查pip是否可用
pip --version >nul 2>&1
if errorlevel 1 (
    echo ✗ pip不可用
    pause
    exit /b 1
)

echo ✓ pip可用

REM 升级pip
echo.
echo 升级pip...
python -m pip install --upgrade pip

REM 安装依赖echo.echo 安装依赖包...if exist requirements.txt (    pip install -r requirements.txt) else (    echo 直接安装核心依赖...    pip install openpyxl Pillow pyinstaller)

REM 运行打包脚本
echo.
echo 开始打包...
python build_exe.py

echo.
echo 打包完成！
echo 可执行文件位置: dist\医疗数据校验工具.exe
echo.
pause 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络客户端测试脚本
测试授权服务器通信协议的实现
"""

import logging
import sys
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from network_client import NetworkClient, get_network_client
from license_models import LicenseInfo, LicenseType, UsageStats


def test_network_client():
    """测试网络客户端功能"""
    print("=" * 60)
    print("网络客户端测试")
    print("=" * 60)
    
    # 创建测试用的授权服务器URL（这里使用模拟地址）
    server_url = "https://license-server.example.com"
    api_key = "test_api_key_12345"
    
    # 创建网络客户端
    logger = logging.getLogger("test_network_client")
    client = NetworkClient(server_url, api_key, logger)
    
    print(f"1. 网络客户端初始化完成")
    print(f"   服务器URL: {server_url}")
    print(f"   会话ID: {client.session_id}")
    
    # 测试连接（这会失败，因为服务器不存在，但可以测试请求构建逻辑）
    print(f"\n2. 测试服务器连接...")
    try:
        success, message = client.test_connection()
        print(f"   连接结果: {success}")
        print(f"   消息: {message}")
    except Exception as e:
        print(f"   连接测试异常（预期）: {str(e)}")
    
    # 测试签名生成
    print(f"\n3. 测试请求签名生成...")
    test_data = {
        'license_code': 'TEST-1234-5678-ABCD',
        'machine_id': 'test_machine_001',
        'timestamp': datetime.now().isoformat()
    }
    
    signature = client._generate_signature('/api/v1/test', 'POST', test_data)
    print(f"   测试数据: {test_data}")
    print(f"   生成签名: {signature}")
    print(f"   签名长度: {len(signature)} 字符")
    
    # 测试在线验证（模拟）
    print(f"\n4. 测试在线授权验证...")
    license_info = LicenseInfo(
        license_type=LicenseType.STANDARD,
        license_code='TEST-1234-5678-ABCD',
        machine_id='test_machine_001',
        user_name='测试用户',
        company_name='测试公司',
        expiry_date=datetime.now() + timedelta(days=365),
        last_verified=datetime.now()
    )
    
    try:
        result = client.verify_online_license(license_info)
        print(f"   验证成功: {result.success}")
        print(f"   授权有效: {result.license_valid}")
        print(f"   响应时间: {result.response_time:.2f}秒")
        if result.error_message:
            print(f"   错误信息: {result.error_message}")
    except Exception as e:
        print(f"   在线验证异常（预期）: {str(e)}")
    
    # 测试使用统计提交
    print(f"\n5. 测试使用统计提交...")
    stats = UsageStats(
        user_id='test_user_001',
        machine_id='test_machine_001',
        app_version='1.0',
        os_info='Windows 10',
        last_used=datetime.now(),
        usage_count=10,
        features_used=['basic', 'advanced']
    )
    
    try:
        success = client.submit_usage_statistics(stats)
        print(f"   提交成功: {success}")
    except Exception as e:
        print(f"   统计提交异常（预期）: {str(e)}")
    
    # 测试撤销检查
    print(f"\n6. 测试授权撤销检查...")
    try:
        revoked = client.check_license_revocation('test_license_001')
        print(f"   撤销状态: {revoked}")
    except Exception as e:
        print(f"   撤销检查异常（预期）: {str(e)}")
    
    # 测试更新下载
    print(f"\n7. 测试授权更新下载...")
    try:
        updates = client.download_license_updates()
        print(f"   更新数量: {len(updates)}")
    except Exception as e:
        print(f"   更新下载异常（预期）: {str(e)}")
    
    print(f"\n8. 测试SSL配置...")
    client.configure_ssl(verify_ssl=False)  # 禁用SSL验证用于测试
    print(f"   SSL验证已禁用")
    
    print(f"\n9. 测试超时和重试配置...")
    client.set_timeout(15)
    client.set_max_retries(2)
    print(f"   超时时间: {client.timeout}秒")
    print(f"   最大重试: {client.max_retries}次")
    
    print(f"\n" + "=" * 60)
    print("网络客户端测试完成")
    print("=" * 60)


def test_global_client():
    """测试全局客户端实例"""
    print("\n" + "=" * 60)
    print("全局客户端测试")
    print("=" * 60)
    
    server_url = "https://license-server.example.com"
    api_key = "global_test_key"
    logger = logging.getLogger("global_client_test")
    
    # 获取全局客户端
    client1 = get_network_client(server_url, api_key, logger)
    client2 = get_network_client()  # 应该返回同一个实例
    
    print(f"客户端1 ID: {id(client1)}")
    print(f"客户端2 ID: {id(client2)}")
    print(f"是否为同一实例: {client1 is client2}")
    
    if client1 and client2:
        print(f"会话ID1: {client1.session_id}")
        print(f"会话ID2: {client2.session_id}")
        print(f"会话ID相同: {client1.session_id == client2.session_id}")


if __name__ == "__main__":
    try:
        test_network_client()
        test_global_client()
        
        print(f"\n所有测试完成！")
        print(f"注意: 网络请求失败是正常的，因为测试服务器不存在")
        print(f"重点是验证请求构建、签名生成等逻辑是否正确")
        
    except KeyboardInterrupt:
        print(f"\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实际构建测试 - 验证PyInstaller能否成功构建可执行文件
"""
import os
import sys
import subprocess
import shutil
import tempfile

def test_spec_file_creation():
    """测试spec文件创建"""
    print("测试spec文件创建...")
    
    try:
        from build_exe import create_optimized_spec_file
        
        # 创建spec文件
        result = create_optimized_spec_file()
        
        if not result:
            print("✗ spec文件创建失败")
            return False
        
        # 检查spec文件是否存在
        spec_file = "医疗数据校验工具.spec"
        if not os.path.exists(spec_file):
            print("✗ spec文件不存在")
            return False
        
        print("✓ spec文件创建成功")
        return True
        
    except Exception as e:
        print(f"✗ spec文件创建测试失败: {e}")
        return False

def test_pyinstaller_analysis():
    """测试PyInstaller分析阶段"""
    print("\n测试PyInstaller分析阶段...")
    
    spec_file = "医疗数据校验工具.spec"
    if not os.path.exists(spec_file):
        print("✗ spec文件不存在，无法进行分析测试")
        return False
    
    try:
        # 运行PyInstaller分析阶段（不实际构建）
        cmd = f"pyinstaller --noconfirm --log-level=INFO --distpath=test_dist --workpath=test_build {spec_file}"
        
        print(f"执行命令: {cmd}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✓ PyInstaller分析阶段成功")
            
            # 检查是否生成了可执行文件
            exe_path = os.path.join("test_dist", "医疗数据校验工具.exe")
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"✓ 可执行文件已生成: {exe_path}")
                print(f"✓ 文件大小: {file_size:.2f} MB")
                return True
            else:
                print("⚠ PyInstaller运行成功但未找到可执行文件")
                return False
        else:
            print(f"✗ PyInstaller分析失败:")
            print(f"返回码: {result.returncode}")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            if result.stdout:
                print(f"输出信息: {result.stdout}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ PyInstaller分析超时（5分钟）")
        return False
    except Exception as e:
        print(f"✗ PyInstaller分析测试失败: {e}")
        return False

def test_executable_dependencies():
    """测试可执行文件的依赖"""
    print("\n测试可执行文件依赖...")
    
    exe_path = os.path.join("test_dist", "医疗数据校验工具.exe")
    if not os.path.exists(exe_path):
        print("✗ 可执行文件不存在，无法测试依赖")
        return False
    
    try:
        # 尝试运行可执行文件（快速退出模式）
        # 注意：这里只是测试能否启动，不测试完整功能
        cmd = f'"{exe_path}" --help'
        
        print(f"测试可执行文件启动: {cmd}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        
        # 对于GUI程序，返回码可能不是0，但如果没有模块错误就算成功
        if "ModuleNotFoundError" not in result.stderr and "ImportError" not in result.stderr:
            print("✓ 可执行文件依赖检查通过（无模块导入错误）")
            return True
        else:
            print("✗ 可执行文件存在模块导入错误:")
            if result.stderr:
                print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠ 可执行文件启动超时（可能是GUI程序正常行为）")
        return True  # GUI程序可能会一直运行，超时不算错误
    except Exception as e:
        print(f"✗ 可执行文件依赖测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n清理测试文件...")
    
    cleanup_items = [
        "医疗数据校验工具.spec",
        "test_dist",
        "test_build"
    ]
    
    for item in cleanup_items:
        try:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
                    print(f"✓ 删除目录: {item}")
                else:
                    os.remove(item)
                    print(f"✓ 删除文件: {item}")
        except Exception as e:
            print(f"⚠ 清理 {item} 失败: {e}")

def main():
    """运行所有测试"""
    print("=" * 60)
    print("     实际构建测试")
    print("=" * 60)
    
    tests = [
        test_spec_file_creation,
        test_pyinstaller_analysis,
        test_executable_dependencies
    ]
    
    passed = 0
    total = len(tests)
    
    try:
        for test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    # 如果某个测试失败，后续测试可能无法进行
                    break
            except Exception as e:
                print(f"✗ 测试 {test_func.__name__} 异常: {e}")
                break
    finally:
        # 无论如何都要清理测试文件
        cleanup_test_files()
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    if passed == total:
        print("✓ 所有构建测试通过!")
        print("✓ PyInstaller能够成功构建可执行文件")
    else:
        print(f"✗ {total - passed} 个测试失败")
        print("⚠ 构建过程可能存在问题")
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
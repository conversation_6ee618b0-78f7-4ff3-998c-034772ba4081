
"""
延迟导入模块 - 减少启动时间
"""
import importlib
from typing import Any, Dict

class LazyImporter:
    """延迟导入器"""
    
    def __init__(self):
        self._modules: Dict[str, Any] = {}
    
    def import_module(self, module_name: str):
        """延迟导入模块"""
        if module_name not in self._modules:
            try:
                self._modules[module_name] = importlib.import_module(module_name)
            except ImportError as e:
                print(f"延迟导入模块 {module_name} 失败: {e}")
                return None
        return self._modules[module_name]
    
    def get_attr(self, module_name: str, attr_name: str):
        """获取模块属性"""
        module = self.import_module(module_name)
        if module:
            return getattr(module, attr_name, None)
        return None

# 全局延迟导入器实例
lazy_importer = LazyImporter()

# 常用模块的延迟导入函数
def get_openpyxl():
    return lazy_importer.import_module('openpyxl')

def get_pil():
    return lazy_importer.import_module('PIL.Image')

def get_tkinter():
    return lazy_importer.import_module('tkinter')


"""
运行时完整性检查器
在程序启动时验证关键文件的完整性
"""
import os
import sys
import json
import hashlib
import time

class IntegrityChecker:
    """完整性检查器"""
    
    def __init__(self, manifest_path="integrity.json"):
        self.manifest_path = manifest_path
        self.manifest = None
        
    def load_manifest(self):
        """加载完整性清单"""
        try:
            if os.path.exists(self.manifest_path):
                with open(self.manifest_path, 'r', encoding='utf-8') as f:
                    self.manifest = json.load(f)
                return True
        except Exception as e:
            print(f"加载完整性清单失败: {e}")
        return False
    
    def calculate_file_hash(self, file_path):
        """计算文件哈希"""
        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        except:
            return None
    
    def verify_file_integrity(self, file_path):
        """验证单个文件完整性"""
        if not self.manifest or file_path not in self.manifest.get("files", {}):
            return True  # 如果没有清单或文件不在清单中，跳过检查
        
        expected_hash = self.manifest["files"][file_path]["hash"]
        actual_hash = self.calculate_file_hash(file_path)
        
        return actual_hash == expected_hash
    
    def verify_all_files(self):
        """验证所有文件完整性"""
        if not self.load_manifest():
            return True  # 如果无法加载清单，跳过检查
        
        failed_files = []
        for file_path in self.manifest.get("files", {}):
            if os.path.exists(file_path):
                if not self.verify_file_integrity(file_path):
                    failed_files.append(file_path)
            else:
                failed_files.append(f"{file_path} (文件不存在)")
        
        if failed_files:
            print("检测到文件完整性问题:")
            for file_path in failed_files:
                print(f"  - {file_path}")
            return False
        
        return True
    
    def check_runtime_environment(self):
        """检查运行时环境"""
        # 检查是否在调试器中运行
        if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
            print("检测到调试器，程序将退出")
            return False
        
        # 检查是否在虚拟机中运行（简单检查）
        vm_indicators = [
            'VMware', 'VirtualBox', 'QEMU', 'Xen', 'Hyper-V'
        ]
        
        try:
            import platform
            system_info = platform.platform().upper()
            for indicator in vm_indicators:
                if indicator.upper() in system_info:
                    print(f"检测到虚拟环境: {indicator}")
                    # 在实际应用中可能需要更严格的处理
                    break
        except:
            pass
        
        return True
    
    def perform_security_check(self):
        """执行安全检查"""
        print("执行安全检查...")
        
        # 文件完整性检查
        if not self.verify_all_files():
            print("文件完整性检查失败")
            return False
        
        # 运行环境检查
        if not self.check_runtime_environment():
            print("运行环境检查失败")
            return False
        
        print("安全检查通过")
        return True

# 全局完整性检查器实例
integrity_checker = IntegrityChecker()

def verify_startup_integrity():
    """启动时验证完整性"""
    return integrity_checker.perform_security_check()

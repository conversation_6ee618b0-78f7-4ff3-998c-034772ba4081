#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
授权码生成工具
用于生成和管理软件授权码
"""

import hashlib
import json
import random
import string
from datetime import datetime, timedelta
from typing import Dict, Any, Optional


class LicenseGenerator:
    """授权码生成器"""
    
    def __init__(self):
        self.secret_key = "MEDICAL_TOOL_SECRET_2025"  # 用于生成授权码的密钥
    
    def generate_license_code(self, 
                            license_type: str = "standard",
                            days: int = 365,
                            user_name: str = "Licensed User",
                            company_name: str = "Licensed Company",
                            machine_id: str = None) -> str:
        """生成授权码
        
        Args:
            license_type: 授权类型 (trial, standard, professional, enterprise)
            days: 有效天数
            user_name: 用户名
            company_name: 公司名
            machine_id: 机器ID (可选，用于硬件绑定)
            
        Returns:
            str: 生成的授权码
        """
        # 生成随机授权码
        prefix = {
            "trial": "TRIAL",
            "standard": "STD",
            "professional": "PRO", 
            "enterprise": "ENT"
        }.get(license_type, "STD")
        
        # 生成4段随机字符
        segments = []
        segments.append(prefix)
        
        for _ in range(3):
            segment = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
            segments.append(segment)
        
        license_code = '-'.join(segments)
        
        # 存储授权码信息到数据库文件
        self._save_license_data(license_code, {
            'type': license_type,
            'days': days,
            'user_name': user_name,
            'company_name': company_name,
            'machine_id': machine_id,
            'created_date': datetime.now().isoformat(),
            'expiry_date': (datetime.now() + timedelta(days=days)).isoformat()
        })
        
        return license_code
    
    def _save_license_data(self, license_code: str, data: Dict[str, Any]):
        """保存授权码数据"""
        try:
            # 读取现有数据
            license_db = {}
            try:
                with open('license_database.json', 'r', encoding='utf-8') as f:
                    license_db = json.load(f)
            except FileNotFoundError:
                pass
            
            # 添加新的授权码数据
            license_db[license_code] = data
            
            # 保存到文件
            with open('license_database.json', 'w', encoding='utf-8') as f:
                json.dump(license_db, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存授权码数据失败: {e}")
    
    def validate_license_code(self, license_code: str) -> Optional[Dict[str, Any]]:
        """验证授权码"""
        try:
            with open('license_database.json', 'r', encoding='utf-8') as f:
                license_db = json.load(f)
                return license_db.get(license_code)
        except FileNotFoundError:
            return None
    
    def list_all_licenses(self):
        """列出所有授权码"""
        try:
            with open('license_database.json', 'r', encoding='utf-8') as f:
                license_db = json.load(f)
                
            print("=" * 80)
            print("授权码列表")
            print("=" * 80)
            
            for code, data in license_db.items():
                expiry = datetime.fromisoformat(data['expiry_date'])
                status = "有效" if expiry > datetime.now() else "已过期"
                
                print(f"授权码: {code}")
                print(f"  类型: {data['type']}")
                print(f"  用户: {data['user_name']}")
                print(f"  公司: {data['company_name']}")
                print(f"  有效期: {expiry.strftime('%Y年%m月%d日')}")
                print(f"  状态: {status}")
                print("-" * 40)
                
        except FileNotFoundError:
            print("未找到授权码数据库")


def main():
    """主函数 - 授权码管理工具"""
    generator = LicenseGenerator()
    
    while True:
        print("\n" + "=" * 50)
        print("医疗数据校验工具 - 授权码管理")
        print("=" * 50)
        print("1. 生成试用版授权码 (30天)")
        print("2. 生成标准版授权码 (1年)")
        print("3. 生成专业版授权码 (1年)")
        print("4. 生成企业版授权码 (2年)")
        print("5. 自定义授权码")
        print("6. 验证授权码")
        print("7. 列出所有授权码")
        print("8. 退出")
        print("-" * 50)
        
        choice = input("请选择操作 (1-8): ").strip()
        
        if choice == "1":
            code = generator.generate_license_code("trial", 30)
            print(f"\n✅ 试用版授权码生成成功:")
            print(f"授权码: {code}")
            print(f"有效期: 30天")
            
        elif choice == "2":
            user = input("请输入用户名 (默认: Licensed User): ").strip() or "Licensed User"
            company = input("请输入公司名 (默认: Licensed Company): ").strip() or "Licensed Company"
            code = generator.generate_license_code("standard", 365, user, company)
            print(f"\n✅ 标准版授权码生成成功:")
            print(f"授权码: {code}")
            print(f"用户: {user}")
            print(f"公司: {company}")
            print(f"有效期: 1年")
            
        elif choice == "3":
            user = input("请输入用户名: ").strip() or "Professional User"
            company = input("请输入公司名: ").strip() or "Professional Company"
            code = generator.generate_license_code("professional", 365, user, company)
            print(f"\n✅ 专业版授权码生成成功:")
            print(f"授权码: {code}")
            print(f"用户: {user}")
            print(f"公司: {company}")
            print(f"有效期: 1年")
            
        elif choice == "4":
            user = input("请输入用户名: ").strip() or "Enterprise User"
            company = input("请输入公司名: ").strip() or "Enterprise Company"
            code = generator.generate_license_code("enterprise", 730, user, company)
            print(f"\n✅ 企业版授权码生成成功:")
            print(f"授权码: {code}")
            print(f"用户: {user}")
            print(f"公司: {company}")
            print(f"有效期: 2年")
            
        elif choice == "5":
            print("\n自定义授权码生成:")
            license_type = input("授权类型 (trial/standard/professional/enterprise): ").strip() or "standard"
            days = int(input("有效天数: ").strip() or "365")
            user = input("用户名: ").strip() or "Custom User"
            company = input("公司名: ").strip() or "Custom Company"
            
            code = generator.generate_license_code(license_type, days, user, company)
            print(f"\n✅ 自定义授权码生成成功:")
            print(f"授权码: {code}")
            print(f"类型: {license_type}")
            print(f"用户: {user}")
            print(f"公司: {company}")
            print(f"有效期: {days}天")
            
        elif choice == "6":
            code = input("请输入要验证的授权码: ").strip()
            data = generator.validate_license_code(code)
            if data:
                expiry = datetime.fromisoformat(data['expiry_date'])
                status = "有效" if expiry > datetime.now() else "已过期"
                print(f"\n✅ 授权码验证结果:")
                print(f"授权码: {code}")
                print(f"类型: {data['type']}")
                print(f"用户: {data['user_name']}")
                print(f"公司: {data['company_name']}")
                print(f"有效期: {expiry.strftime('%Y年%m月%d日')}")
                print(f"状态: {status}")
            else:
                print(f"\n❌ 授权码无效或不存在")
                
        elif choice == "7":
            generator.list_all_licenses()
            
        elif choice == "8":
            print("退出授权码管理工具")
            break
            
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
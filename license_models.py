#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
授权管理数据模型
定义授权系统中使用的所有数据结构和枚举类型
"""

from dataclasses import dataclass, field
from datetime import datetime, date
from enum import Enum
from typing import List, Optional, Dict, Any
import json


class LicenseStatus(Enum):
    """授权状态枚举"""
    VALID = "valid"                    # 有效
    EXPIRED = "expired"                # 已过期
    TRIAL = "trial"                    # 试用期
    TRIAL_EXPIRED = "trial_expired"    # 试用期过期
    INVALID = "invalid"                # 无效
    NETWORK_ERROR = "network_error"    # 网络错误
    TIME_ERROR = "time_error"          # 时间错误
    CONFIG_ERROR = "config_error"      # 配置错误


class LicenseType(Enum):
    """授权类型枚举"""
    TRIAL = "trial"                    # 试用版
    STANDARD = "standard"              # 标准版
    PROFESSIONAL = "professional"      # 专业版
    ENTERPRISE = "enterprise"          # 企业版


class ValidationResult(Enum):
    """验证结果枚举"""
    SUCCESS = "success"                # 验证成功
    FAILED = "failed"                  # 验证失败
    ERROR = "error"                    # 验证错误
    PENDING = "pending"                # 等待验证


@dataclass
class LicenseInfo:
    """授权信息数据结构"""
    license_type: LicenseType = LicenseType.TRIAL
    expiry_date: Optional[datetime] = None
    license_code: str = ""
    machine_id: str = ""
    user_name: str = ""
    company_name: str = ""
    features: List[str] = field(default_factory=list)
    max_users: int = 1
    created_date: Optional[datetime] = None
    last_verified: Optional[datetime] = None
    trial_start_date: Optional[datetime] = None
    trial_days: int = 30
    
    def is_trial(self) -> bool:
        """检查是否为试用版"""
        return self.license_type == LicenseType.TRIAL
    
    def is_expired(self) -> bool:
        """检查是否已过期"""
        if not self.expiry_date:
            return False
        return datetime.now() > self.expiry_date
    
    def days_until_expiry(self) -> int:
        """计算距离过期的天数"""
        if not self.expiry_date:
            return -1
        delta = self.expiry_date - datetime.now()
        return max(0, delta.days)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'license_type': self.license_type.value,
            'expiry_date': self.expiry_date.isoformat() if self.expiry_date else None,
            'license_code': self.license_code,
            'machine_id': self.machine_id,
            'user_name': self.user_name,
            'company_name': self.company_name,
            'features': self.features,
            'max_users': self.max_users,
            'created_date': self.created_date.isoformat() if self.created_date else None,
            'last_verified': self.last_verified.isoformat() if self.last_verified else None,
            'trial_start_date': self.trial_start_date.isoformat() if self.trial_start_date else None,
            'trial_days': self.trial_days
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LicenseInfo':
        """从字典创建实例"""
        def parse_datetime(date_str: Optional[str]) -> Optional[datetime]:
            if date_str:
                try:
                    return datetime.fromisoformat(date_str)
                except ValueError:
                    return None
            return None
        
        return cls(
            license_type=LicenseType(data.get('license_type', 'trial')),
            expiry_date=parse_datetime(data.get('expiry_date')),
            license_code=data.get('license_code', ''),
            machine_id=data.get('machine_id', ''),
            user_name=data.get('user_name', ''),
            company_name=data.get('company_name', ''),
            features=data.get('features', []),
            max_users=data.get('max_users', 1),
            created_date=parse_datetime(data.get('created_date')),
            last_verified=parse_datetime(data.get('last_verified')),
            trial_start_date=parse_datetime(data.get('trial_start_date')),
            trial_days=data.get('trial_days', 30)
        )


@dataclass
class LicenseConfig:
    """授权配置数据结构"""
    server_url: str = "https://license.example.com/api"
    check_interval: int = 24                    # 检查间隔(小时)
    trial_days: int = 30                        # 试用天数
    warning_days: int = 7                       # 提前警告天数
    max_offline_days: int = 30                  # 最大离线天数
    enable_hardware_binding: bool = True        # 启用硬件绑定
    enable_network_check: bool = True           # 启用网络检查
    enable_time_check: bool = True              # 启用时间检查
    log_level: str = "INFO"                     # 日志级别
    config_file_path: str = "license.dat"      # 配置文件路径
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'server_url': self.server_url,
            'check_interval': self.check_interval,
            'trial_days': self.trial_days,
            'warning_days': self.warning_days,
            'max_offline_days': self.max_offline_days,
            'enable_hardware_binding': self.enable_hardware_binding,
            'enable_network_check': self.enable_network_check,
            'enable_time_check': self.enable_time_check,
            'log_level': self.log_level,
            'config_file_path': self.config_file_path
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LicenseConfig':
        """从字典创建实例"""
        return cls(
            server_url=data.get('server_url', "https://license.example.com/api"),
            check_interval=data.get('check_interval', 24),
            trial_days=data.get('trial_days', 30),
            warning_days=data.get('warning_days', 7),
            max_offline_days=data.get('max_offline_days', 30),
            enable_hardware_binding=data.get('enable_hardware_binding', True),
            enable_network_check=data.get('enable_network_check', True),
            enable_time_check=data.get('enable_time_check', True),
            log_level=data.get('log_level', "INFO"),
            config_file_path=data.get('config_file_path', "license.dat")
        )


@dataclass
class TrialStatus:
    """试用期状态"""
    is_trial: bool = False
    is_active: bool = False
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    days_remaining: int = 0
    days_used: int = 0
    
    def is_expired(self) -> bool:
        """检查试用期是否已过期"""
        if not self.end_date:
            return False
        return datetime.now() > self.end_date
    
    def needs_warning(self, warning_days: int = 7) -> bool:
        """检查是否需要显示警告"""
        return self.is_active and self.days_remaining <= warning_days


@dataclass
class TimeValidationResult:
    """时间验证结果"""
    is_valid: bool = True
    system_time: Optional[datetime] = None
    network_time: Optional[datetime] = None
    time_difference: int = 0  # 秒
    error_message: str = ""
    
    def has_time_manipulation(self, threshold_seconds: int = 300) -> bool:
        """检查是否存在时间篡改（默认阈值5分钟）"""
        return abs(self.time_difference) > threshold_seconds


@dataclass
class OnlineVerificationResult:
    """在线验证结果"""
    success: bool = False
    license_valid: bool = False
    server_time: Optional[datetime] = None
    new_expiry_date: Optional[datetime] = None
    revoked: bool = False
    error_message: str = ""
    response_time: float = 0.0  # 响应时间(秒)


@dataclass
class UsageStats:
    """使用统计数据"""
    user_id: str = ""
    machine_id: str = ""
    app_version: str = ""
    os_info: str = ""
    last_used: Optional[datetime] = None
    usage_count: int = 0
    features_used: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'user_id': self.user_id,
            'machine_id': self.machine_id,
            'app_version': self.app_version,
            'os_info': self.os_info,
            'last_used': self.last_used.isoformat() if self.last_used else None,
            'usage_count': self.usage_count,
            'features_used': self.features_used
        }


@dataclass
class LicenseUpdate:
    """授权更新信息"""
    version: str = ""
    update_type: str = ""  # config, license, revocation
    data: Dict[str, Any] = field(default_factory=dict)
    timestamp: Optional[datetime] = None
    signature: str = ""
    
    def is_valid_signature(self, public_key: str) -> bool:
        """验证数字签名（占位符实现）"""
        # TODO: 实现真实的数字签名验证
        return len(self.signature) > 0


# 异常类定义
class LicenseError(Exception):
    """授权相关错误基类"""
    pass


class LicenseExpiredError(LicenseError):
    """授权过期错误"""
    pass


class InvalidLicenseCodeError(LicenseError):
    """无效授权码错误"""
    pass


class TimeValidationError(LicenseError):
    """时间验证错误"""
    pass


class NetworkValidationError(LicenseError):
    """网络验证错误"""
    pass


class ConfigurationError(LicenseError):
    """配置错误"""
    pass


class TrialExpiredError(LicenseError):
    """试用期过期错误"""
    pass


class HardwareBindingError(LicenseError):
    """硬件绑定错误"""
    pass

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线授权缓存机制
实现在无法访问网络时，对授权信息进行本地缓存和验证，确保用户在一定期限内可以离线使用。
"""

import os
import json
import hashlib
from datetime import datetime, timedelta
from cryptography.fernet import Fernet, InvalidToken
from license_models import LicenseInfo, LicenseConfig

class OfflineLicenseCache:
    """
    离线授权缓存管理器
    - 负责将授权信息安全地存储在本地
    - 在无法访问网络时，提供有效的授权状态
    - 防止缓存文件被篡改
    """

    def __init__(self, config: LicenseConfig, machine_id: str):
        """
        初始化离线缓存管理器
        :param config: 授权配置
        :param machine_id: 机器指纹
        """
        self.config = config
        self.cache_file_path = os.path.join(os.path.dirname(config.config_file_path), "offline_cache.dat")
        self.encryption_key = self._generate_key(machine_id)
        self.cipher_suite = Fernet(self.encryption_key)

    def _generate_key(self, machine_id: str) -> bytes:
        """
        根据机器指纹和固定的盐值生成加密密钥
        :param machine_id: 机器指纹
        :return: 加密密钥
        """
        salt = b'offline-cache-salt-for-license-system'
        key_material = hashlib.sha256(salt + machine_id.encode('utf-8')).digest()
        return Fernet.generate_key()

    def save_license_info(self, license_info: LicenseInfo):
        """
        将授权信息加密并保存到缓存文件
        :param license_info: 授权信息
        """
        try:
            license_info.last_verified = datetime.now()
            data_to_cache = {
                "license_info": license_info.to_dict(),
                "timestamp": datetime.now().isoformat()
            }
            
            serialized_data = json.dumps(data_to_cache).encode('utf-8')
            encrypted_data = self.cipher_suite.encrypt(serialized_data)
            
            with open(self.cache_file_path, 'wb') as f:
                f.write(encrypted_data)
        except (IOError, TypeError) as e:
            # TODO: Add logging
            print(f"Error saving license cache: {e}")

    def load_license_info(self) -> Optional[LicenseInfo]:
        """
        从缓存文件加载并解密授权信息
        :return: 授权信息，如果缓存无效或过期则返回None
        """
        if not os.path.exists(self.cache_file_path):
            return None

        try:
            with open(self.cache_file_path, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.cipher_suite.decrypt(encrypted_data)
            cached_data = json.loads(decrypted_data.decode('utf-8'))
            
            timestamp = datetime.fromisoformat(cached_data.get("timestamp"))
            if datetime.now() - timestamp > timedelta(days=self.config.max_offline_days):
                # 缓存已过期
                self.clear_cache()
                return None
            
            license_info = LicenseInfo.from_dict(cached_data.get("license_info"))
            
            # 验证机器ID是否匹配
            if license_info.machine_id != self.machine_id:
                # 缓存与当前机器不匹配
                self.clear_cache()
                return None

            return license_info
        except (IOError, json.JSONDecodeError, InvalidToken, TypeError) as e:
            # TODO: Add logging
            print(f"Error loading license cache: {e}")
            self.clear_cache()
            return None

    def clear_cache(self):
        """
        清除无效的缓存文件
        """
        if os.path.exists(self.cache_file_path):
            try:
                os.remove(self.cache_file_path)
            except IOError as e:
                # TODO: Add logging
                print(f"Error clearing license cache: {e}")

    def is_cache_valid(self) -> bool:
        """
        检查缓存是否有效且未过期
        :return: 如果缓存有效则返回True
        """
        license_info = self.load_license_info()
        return license_info is not None

if __name__ == '__main__':
    # 示例用法
    
    # 模拟配置和机器ID
    class MockLicenseConfig(LicenseConfig):
        def __init__(self):
            super().__init__()
            self.config_file_path = "./license.dat"
            self.max_offline_days = 15

    mock_config = MockLicenseConfig()
    mock_machine_id = "test-machine-12345"
    
    # 创建缓存管理器
    cache_manager = OfflineLicenseCache(mock_config, mock_machine_id)
    
    # 1. 清除旧缓存
    cache_manager.clear_cache()
    print("旧缓存已清除。")
    
    # 2. 创建并保存授权信息
    license_to_cache = LicenseInfo(
        license_type=LicenseType.STANDARD,
        expiry_date=datetime(2025, 12, 31),
        license_code="CACHE-TEST-CODE",
        machine_id=mock_machine_id,
        user_name="Cache User"
    )
    cache_manager.save_license_info(license_to_cache)
    print(f"授权信息已保存到缓存: {cache_manager.cache_file_path}")
    
    # 3. 从缓存加载授权信息
    loaded_license = cache_manager.load_license_info()
    if loaded_license:
        print("从缓存加载的授权信息:")
        print(f"  - 类型: {loaded_license.license_type.value}")
        print(f"  - 过期日期: {loaded_license.expiry_date}")
        print(f"  - 用户名: {loaded_license.user_name}")
        print(f"  - 上次验证时间: {loaded_license.last_verified}")
    else:
        print("无法从缓存加载授权信息。")
        
    # 4. 验证缓存有效性
    print(f"缓存是否有效: {cache_manager.is_cache_valid()}")
    
    # 5. 模拟缓存过期
    print("\n模拟缓存过期...")
    cache_manager.config.max_offline_days = 0  # 设置为0天，使其立即过期
    expired_license = cache_manager.load_license_info()
    if not expired_license:
        print("缓存已过期，并已成功清除。")
    else:
        print("缓存过期测试失败。")
        
    # 6. 模拟机器ID不匹配
    print("\n模拟机器ID不匹配...")
    cache_manager.config.max_offline_days = 15 # 恢复
    cache_manager.save_license_info(license_to_cache) # 重新保存
    
    wrong_machine_manager = OfflineLicenseCache(mock_config, "wrong-machine-id")
    mismatched_license = wrong_machine_manager.load_license_info()
    if not mismatched_license:
        print("机器ID不匹配，缓存加载失败。")
    else:
        print("机器ID不匹配测试失败。")

